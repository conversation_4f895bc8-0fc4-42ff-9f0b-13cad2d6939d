package com.howbuy.tms.high.batch.service.business.crmfilemonitor.everyhour;

import com.alibaba.fastjson.JSON;
import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.batch.service.business.BatchMessageProcessor;
import com.howbuy.tms.high.batch.service.business.crmfilemonitor.CrmFileHkNotifyProcessor;
import com.howbuy.tms.high.batch.service.business.crmfilemonitor.CrmFileMessageVo;
import com.howbuy.tms.high.batch.service.common.enums.CrmFileReceiveTypeEnum;
import com.howbuy.tms.high.batch.service.repository.CmFileProcessRecRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @api {MQ} crm_direct_prehour_version
 * @apiGroup schedule
 * @apiName crm每小时推送消息
 * @apiDescription crm每小时推送消息
 */
@Service("crmFileEveryHourNotifyProcessor")
public class CrmFileEveryHourNotifyProcessor extends BatchMessageProcessor {
    private static final Logger logger = LogManager.getLogger(CrmFileEveryHourNotifyProcessor.class);

    @Value("${high.crmFileEveryHourNotifyQueue}")
    private String queueName;

    @Autowired
    private CrmFileEveryHourLogicService crmFileEveryHourLogicService;

    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;

    @Autowired
    private CmFileProcessRecRepository cmFileProcessRecRepository;

    @Override
    public void doProcessMessage(SimpleMessage message) {
        // 1.参数校验
        if (message == null) {
            logger.info("CrmFileEveryHourNotifyProcessor|processMessage|消息是空的");
            return;
        }
        String content = (String) message.getContent();
        logger.info("CrmFileEveryHourNotifyProcessor|processMessage|start:{}", content);
        CrmFileMessageVo vo = JSON.parseObject(content, CrmFileMessageVo.class);
        vo.setFileType(CrmFileReceiveTypeEnum.CRM_EVERY_HOUR.getType());
        if (vo.getVersion() == null || StringUtils.isEmpty(vo.getDealdt()) || StringUtils.isEmpty(vo.getSversion())) {
            logger.error("CrmFileMessageProcessor error: param is null");
            return;
        }

        Date appDt = new Date();
        // 2.如果开始接收到crm的全量文件,就不处理
        String workDay = queryTradeDayOuterService.getWorkDay(appDt);
        long count = cmFileProcessRecRepository.countDayTypeByWorkday(workDay);
        if (count > 0) {
            logger.error("CrmFileEveryHourNotifyProcessor-已经接受每天推送一次的crm文件,就不接受每小时增量推送的消息,vo={}", JSON.toJSONString(vo));
            return;
        }
        // 3.文件处理
        try {
            crmFileEveryHourLogicService.process(vo);
        } catch (Exception e) {
            logger.error("CrmFileEveryHourNotifyProcessor error:{}", e.getMessage(), e);
        }

        logger.info("CrmFileEveryHourNotifyProcessor|processMessage|end");

    }

    @Override
    protected String getQuartMessageChannel() {
        return queueName;
    }


}
