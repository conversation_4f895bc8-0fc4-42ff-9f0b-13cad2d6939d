package com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.bean;

import lombok.Data;

/**
 * @Description:普通股权份额转让订单详情
 * @Author: yun.lu
 * Date: 2023/5/18 16:36
 */
@Data
public class NormalOwnershipRightTransferDtlBean extends AbstractOwnershipRightTransferDtlBean {
    private static final long serialVersionUID = -1852223765423137L;
    /**
     * 客户名
     */
    private String custName;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 投资者类型
     */
    private String invstType;

    /**
     * 是否是非交易转让
     */
    private String isNoTradeTransfer;

    /**
     * 之前是否非交易转让状态
     */
    private String oldIsNoTradeTransfer;


}
