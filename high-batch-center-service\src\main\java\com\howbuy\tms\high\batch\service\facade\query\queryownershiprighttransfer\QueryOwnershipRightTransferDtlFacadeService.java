package com.howbuy.tms.high.batch.service.facade.query.queryownershiprighttransfer;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.QueryOwnershipRightTransferDtlFacade;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.bean.AbstractOwnershipRightTransferDtlBean;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.QueryOwnershipRightTransferDtlRequest;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.QueryOwnershipRightTransferDtlResponse;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.facade.AbstractService;
import com.howbuy.tms.high.batch.service.logic.OwnershipTransferOrderLogicService;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description:查询股权份额转让订单详情接口
 * @Author: yun.lu
 * Date: 2023/5/18 16:43
 */
@DubboService
@Service("queryOwnershipRightTransferDtlFacade")
public class QueryOwnershipRightTransferDtlFacadeService extends AbstractService<QueryOwnershipRightTransferDtlRequest, QueryOwnershipRightTransferDtlResponse> implements QueryOwnershipRightTransferDtlFacade {

    @Autowired
    private OwnershipTransferOrderLogicService ownershipTransferOrderLogicService;

    /**
     * @api {dubbo} com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.QueryOwnershipRightTransferDtlFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryOwnershipRightTransferDtlFacadeService
     * @apiName process
     * @apiParam (请求参数) {String} dealDtlNo 订单号
     * @apiParam (请求参数) {String} dealAppNo 变更申请单号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * pageSize=8769&disCode=agrF&txChannel=ys&appTm=c&dealAppNo=sDq4oq&subOutletCode=hsFMRsbW&pageNo=9030&operIp=pmyQlN9bk&appDt=9Cpg&dataTrack=vHU9&txCode=C8ZK&outletCode=6BoP&dealDtlNo=x1OFZE
     * @apiSuccess (响应结果) {Object} abstractOwnershipRightTransferDtlBean 非交易转让信息
     * @apiSuccess (响应结果) {String} abstractOwnershipRightTransferDtlBean.txAcctNo 客户账号
     * @apiSuccess (响应结果) {String} abstractOwnershipRightTransferDtlBean.dealDtlNo 当前订单号
     * @apiSuccess (响应结果) {String} abstractOwnershipRightTransferDtlBean.fundCode 基金代码
     * @apiSuccess (响应结果) {String} abstractOwnershipRightTransferDtlBean.fundName 基金名称
     * @apiSuccess (响应结果) {String} abstractOwnershipRightTransferDtlBean.mBusinessCode 中台业务编码
     * @apiSuccess (响应结果) {String} abstractOwnershipRightTransferDtlBean.ackDt 确认日期
     * @apiSuccess (响应结果) {Number} abstractOwnershipRightTransferDtlBean.transferVol 转让份额
     * @apiSuccess (响应结果) {Number} abstractOwnershipRightTransferDtlBean.transferPrice 转让价格
     * @apiSuccess (响应结果) {Number} abstractOwnershipRightTransferDtlBean.oldTransferPrice 之前转让价格
     * @apiSuccess (响应结果) {String} abstractOwnershipRightTransferDtlBean.fundType 产品类型
     * @apiSuccess (响应结果) {String} abstractOwnershipRightTransferDtlBean.fundSubType 产品二级类型
     * @apiSuccess (响应结果) {Number} abstractOwnershipRightTransferDtlBean.ackAmt 确认金额
     * @apiSuccess (响应结果) {Number} abstractOwnershipRightTransferDtlBean.ackVol 确认份额
     * @apiSuccess (响应结果) {Number} abstractOwnershipRightTransferDtlBean.totalSubsAmt 总认缴金额
     * @apiSuccess (响应结果) {Number} abstractOwnershipRightTransferDtlBean.subsAmt 过户份额对应的认缴金额
     * @apiSuccess (响应结果) {Number} abstractOwnershipRightTransferDtlBean.oldTotalSubsAmt 变更前总认缴金额
     * @apiSuccess (响应结果) {Number} abstractOwnershipRightTransferDtlBean.oldSubsAmt 变更前过户份额对应的认缴金额
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"abstractOwnershipRightTransferDtlBean":{"ackVol":4164.929373527537,"oldTransferPrice":8610.252675051197,"oldTotalSubsAmt":5511.522672259246,"transferVol":8837.237540580314,"mBusinessCode":"tqwZg5hnO","fundType":"w2s","totalSubsAmt":2364.411997053595,"fundSubType":"guPcN","fundCode":"0zI9hHiM7","ackAmt":2851.663863923346,"txAcctNo":"8t6e","subsAmt":747.5778631737618,"fundName":"Qbex","ackDt":"q","oldSubsAmt":4020.326872144403,"dealDtlNo":"Ag","transferPrice":6395.697670489699},"returnCode":"LyggEF","totalPage":3465,"pageNo":6088,"description":"BFIAe","totalCount":6760}
     */
    @Override
    public QueryOwnershipRightTransferDtlResponse process(QueryOwnershipRightTransferDtlRequest request) {
        // 1.参数校验
        if (StringUtils.isBlank(request.getDealDtlNo()) && StringUtils.isBlank(request.getDealAppNo())) {
            throw new BatchException(ExceptionCodes.PARAMS_ERROR, "订单号/申请单号不能为空");
        }
        // 2.查询订单信息
        AbstractOwnershipRightTransferDtlBean abstractOwnershipRightTransferDtlBean;
        if (StringUtils.isNotBlank(request.getDealAppNo())) {
            abstractOwnershipRightTransferDtlBean = ownershipTransferOrderLogicService.queryOwnershipRightTransferDtlByDealAppNo(request.getDealAppNo());
        } else {
            abstractOwnershipRightTransferDtlBean = ownershipTransferOrderLogicService.queryOwnershipRightTransferDtlByDealDtlNo(request.getDealDtlNo());
        }
        // 3.构建返回实体
        QueryOwnershipRightTransferDtlResponse resp = new QueryOwnershipRightTransferDtlResponse();
        resp.setAbstractOwnershipRightTransferDtlBean(abstractOwnershipRightTransferDtlBean);
        resp.setReturnCode(ExceptionCodes.SUCCESS);
        return resp;
    }
}
