/**
 * Copyright (c) 2016, <PERSON>gHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.facade.query.querytadayclose;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.batch.facade.common.BatchBaseRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * Description:查询高端TA收市状态请求类
 *
 * <AUTHOR>
 * @date 2025-05-15 15:35:23
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryHighTaDayCloseStatusRequest extends BatchBaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * TA代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "TA代码", isRequired = true)
    private String taCode;

    /**
     * 工作日
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "工作日", isRequired = true)
    private String workDay;

    public QueryHighTaDayCloseStatusRequest() {
        super.setTxCode(TxCodes.QUERY_HIGH_TA_DAY_CLOSE_STATUS);
    }
} 