/**
 * Copyright (c) 2016, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.query.querytadayclose;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusFacade;
import com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusRequest;
import com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusResponse;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.util.jar.JarEntry;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Description: 查询高端TA收市状态接口客户端调用测试
 * 注意：此测试需要在Dubbo服务启动的情况下运行
 *
 * <AUTHOR>
 * @date 2025-05-16 10:56:54
 * @since JDK 1.8
 */
public class QueryHighTaDayCloseStatusFacadeClientTest {

    private QueryHighTaDayCloseStatusFacade queryHighTaDayCloseStatusFacade;

    @Before
    public void setUp() {
        // 方法1：直接使用XML配置
        try {
            ClassPathXmlApplicationContext context = new ClassPathXmlApplicationContext("dubbo-batch-reference-test.xml");
            context.start();
            queryHighTaDayCloseStatusFacade = context.getBean("queryHighTaDayCloseStatusFacade", QueryHighTaDayCloseStatusFacade.class);
        } catch (Exception e) {
            System.err.println("通过XML加载Dubbo客户端失败，尝试使用API方式: " + e.getMessage());
            // 方法2：使用API配置
            configDubboClient();
        }
    }

    private void configDubboClient() {
        try {
            // 当前应用配置
            ApplicationConfig application = new ApplicationConfig();
            application.setName("high-batch-center-test-client");

            // 连接注册中心配置
            RegistryConfig registry = new RegistryConfig();
            registry.setProtocol("zookeeper");
            registry.setAddress("zookeeper:2181");

            // 引用远程服务
            ReferenceConfig<QueryHighTaDayCloseStatusFacade> reference = new ReferenceConfig<>();
            reference.setApplication(application);
            reference.setRegistry(registry);
            reference.setInterface(QueryHighTaDayCloseStatusFacade.class);
            reference.setCheck(false);
            reference.setTimeout(30000);

            // 获取代理对象
            queryHighTaDayCloseStatusFacade = reference.get();
        } catch (Exception e) {
            System.err.println("使用API方式配置Dubbo客户端失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试客户端调用 - 参数校验
     */
    @Test
    public void testClientCallWithEmptyParams() {
        // 准备测试数据
        QueryHighTaDayCloseStatusRequest request = new QueryHighTaDayCloseStatusRequest();

        try {
            // 执行测试
            QueryHighTaDayCloseStatusResponse response = queryHighTaDayCloseStatusFacade.execute(request);

            // 验证结果
            assertNotNull(response);
            assertEquals(ExceptionCodes.PARAMS_ERROR, response.getReturnCode());

            System.out.println(JSON.toJSONString(response));
        } catch (Exception e) {
            System.err.println("调用服务异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试客户端调用 - 未初始化
     */
    @Test
    public void testClientCallNotDayInit() {
        // 准备测试数据
        QueryHighTaDayCloseStatusRequest request = new QueryHighTaDayCloseStatusRequest();
        request.setTaCode("98");
        request.setWorkDay("20251104");

        try {
            // 执行测试
            QueryHighTaDayCloseStatusResponse response = queryHighTaDayCloseStatusFacade.execute(request);

            // 验证结果
            assertNotNull(response);
            assertNotNull(response.getReturnCode());
            assertEquals(ExceptionCodes.HIGH_BATCH_WORKDAY_NOT_DAY_INIT, response.getReturnCode());

            System.out.println(JSON.toJSONString(response));
        } catch (Exception e) {
            System.err.println("调用服务异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试客户端调用 - 不支持
     */
    @Test
    public void testClientCallWithNotSupported() {
        // 准备测试数据
        QueryHighTaDayCloseStatusRequest request = new QueryHighTaDayCloseStatusRequest();
        request.setTaCode("98");
        request.setWorkDay("20221104");

        try {
            // 执行测试
            QueryHighTaDayCloseStatusResponse response = queryHighTaDayCloseStatusFacade.execute(request);

            // 验证结果
            assertNotNull(response);
            assertNotNull(response.getReturnCode());
            assertEquals("2", response.getDayCloseStatus());

            System.out.println(JSON.toJSONString(response));
        } catch (Exception e) {
            System.err.println("调用服务异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试客户端调用 - 正常查询
     */
    @Test
    public void testClientCall() {
        // 准备测试数据
        QueryHighTaDayCloseStatusRequest request = new QueryHighTaDayCloseStatusRequest();
        request.setTaCode("47");
        request.setWorkDay("20221104");

        try {
            // 执行测试
            QueryHighTaDayCloseStatusResponse response = queryHighTaDayCloseStatusFacade.execute(request);

            // 验证结果
            assertNotNull(response);
            assertNotNull(response.getReturnCode());
            assertEquals("1", response.getDayCloseStatus());

            System.out.println(JSON.toJSONString(response));
        } catch (Exception e) {
            System.err.println("调用服务异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 