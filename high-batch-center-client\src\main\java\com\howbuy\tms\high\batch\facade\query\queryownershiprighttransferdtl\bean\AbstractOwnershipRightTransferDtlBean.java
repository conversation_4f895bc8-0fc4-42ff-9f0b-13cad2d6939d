package com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.bean;

import com.howbuy.tms.high.batch.facade.common.BaseDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:查詢股权份额转让订单详情
 * @Author: yun.lu
 * Date: 2023/5/18 15:45
 */
@Data
public class AbstractOwnershipRightTransferDtlBean extends BaseDto {
    private static final long serialVersionUID = -18523213765423137L;

    /**
     * 客户账号
     */
    private String txAcctNo;
    /**
     * 当前订单号
     */
    private String dealDtlNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 中台业务编码
     */
    private String mBusinessCode;

    /**
     * 确认日期
     */
    private String ackDt;

    /**
     * 转让份额
     */
    private BigDecimal transferVol;
    /**
     * 转让价格
     */
    private BigDecimal transferPrice;

    /**
     * 之前转让价格
     */
    private BigDecimal oldTransferPrice;
    /**
     * 产品类型
     */
    private String fundType;
    /**
     * 产品二级类型
     */
    private String fundSubType;
    /**
     * 确认金额
     */
    private BigDecimal ackAmt;
    /**
     * 确认份额
     */
    private BigDecimal ackVol;

    /**
     * 总认缴金额
     */
    private BigDecimal totalSubsAmt;

    /**
     * 过户份额对应的认缴金额
     */
    private BigDecimal subsAmt;


    /**
     * 变更前总认缴金额
     */
    private BigDecimal oldTotalSubsAmt;

    /**
     * 变更前过户份额对应的认缴金额
     */
    private BigDecimal oldSubsAmt;

}
