package com.howbuy.tms.high.batch.service.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.ccms.util.FastJsonUtil;
import com.howbuy.message.MessageService;
import com.howbuy.message.SimpleMessage;
import com.howbuy.message.processor.MessageProcessor;
import com.howbuy.tms.common.enums.busi.QuartzResultEnum;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.trace.RequestChainTrace;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.MDC;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description:批处理调度任务消息接收处理器
 * @reason:
 * @date 2016年9月14日 上午11:08:42
 * @since JDK 1.6
 */
public abstract class BatchMessageProcessor extends MessageProcessor {
    /**
     * 日志记录器
     */
    protected static final Logger logger = LogManager.getLogger(BatchMessageProcessor.class);

    /**
     * 缓存中间件
     */
    protected CacheService cacheService = CacheServiceImpl.getInstance();

    @PostConstruct
    public void init() {
        try {
            if (isSchedule()) {
                // 初始化Key
                cacheService.initializeKey(Constant.QRTZ_LOG_LIST_KEY);
            }
            String queueTag = getQueueTag();
            // 加载消息处理器
            if (queueTag != null) {
                MessageService.getInstance().addMessageProcessor(getQuartMessageChannel(), queueTag, this);
            } else {
                MessageService.getInstance().addMessageProcessor(getQuartMessageChannel(), this);
            }
        } catch (Exception e) {
            logger.error("BatchMessageProcessor-init,初始化异常,eMsg={},e={}", e.getMessage(), e);
        }
    }

    @Override
    public void processMessage(SimpleMessage message) {
        // 设置追踪上下文，RequestChainTrace.buildAndSet会自动设置SLF4J MDC
        String uuid = UUID.randomUUID().toString().replace("-", "");
        RequestChainTrace.buildAndSet(uuid, null);

        // 获取ranNo
        String currentRanNo = RequestChainTrace.getRanNo();

        // 同时设置SLF4J MDC和Log4j2 ThreadContext来确保兼容性
        MDC.put("uuid", uuid);
        MDC.put("ranNo", currentRanNo);
        ThreadContext.put("uuid", uuid);
        ThreadContext.put("ranNo", currentRanNo);

        logger.info("BatchMessageProcessor|processMessage start -> message:{}", JSONObject.toJSONString(message));
        int procResult = 0;
        try {
            doProcessMessage(message);
            procResult = QuartzResultEnum.SUCCESS.getCode();
        } catch (Exception ex) {
            procResult = QuartzResultEnum.FAIL.getCode();
            logger.error("Error process Message " + message.getContent(), ex);
        } finally {
            logger.info("BatchMessageProcessor|processMessage procResult:{}, content:{}", procResult, JSONObject.toJSONString(message.getContent()));
            // 返回结果给调度中心
            if (isSchedule()) {
                responseMessage(message, procResult);
            }

            // 清理上下文
            RequestChainTrace.remove();
            try {
                MDC.remove("uuid");
                MDC.remove("ranNo");
                ThreadContext.remove("uuid");
                ThreadContext.remove("ranNo");
            } catch (Exception e) {
                // 忽略清理异常
            }
        }
    }

    /**
     * 是否调度
     *
     * @return
     */
    public boolean isSchedule() {
        return true;
    }

    /**
     * responseMessage:针对调度消息的返回结果方法
     *
     * @param message
     * <AUTHOR>
     * @date 2016年10月13日 下午3:04:16
     */
    private void responseMessage(SimpleMessage message, int quartzResult) {
        if (message.getContent() instanceof String) {
            responseQuartzLog((String) message.getContent(), quartzResult);
        } else {
            @SuppressWarnings("unchecked")
            Map<String, String> requestMap = (Map<String, String>) message.getContent();
            responseQuartzLog(requestMap, quartzResult);
        }
    }

    /**
     * responseQuartzLog:返回结果给调度中心
     *
     * @param value        消息内容
     * @param quartzResult 调度结果
     * <AUTHOR>
     * @date 2016年10月13日 下午4:16:51
     */
    private void responseQuartzLog(String value, int quartzResult) {
        Map<String, Object> qrtzLogMap = new HashMap<>(3);
        JSONObject jsonObject = JSONObject.parseObject(value);
        qrtzLogMap.put("qrtz_log_id", jsonObject.getString("qrtz_log_id"));
        qrtzLogMap.put("end_time", System.currentTimeMillis());
        qrtzLogMap.put("result", quartzResult);
        cacheService.append(Constant.QRTZ_LOG_LIST_KEY, JSON.toJSONString(qrtzLogMap));
    }

    /**
     * responseQuartzLog:返回结果给调度中心
     *
     * @param requestMap
     * @param quartzResult
     * <AUTHOR>
     * @date 2016年10月13日 下午4:16:51
     */
    private void responseQuartzLog(Map<String, String> requestMap, int quartzResult) {
        String qrtzLogId = requestMap.get("qrtz_log_id");
        Map<String, Object> qrtzLogMap = new HashMap<String, Object>();
        qrtzLogMap.put("qrtz_log_id", qrtzLogId);
        qrtzLogMap.put("end_time", System.currentTimeMillis());
        qrtzLogMap.put("result", quartzResult);
        cacheService.append(Constant.QRTZ_LOG_LIST_KEY, FastJsonUtil.toJson(qrtzLogMap));
    }

    /**
     * getQuartMessageChannel:获取调度消息队列名
     *
     * @return
     * <AUTHOR>
     * @date 2016年9月14日 上午11:28:58
     */
    protected abstract String getQuartMessageChannel();

    /**
     * tag
     *
     * @return
     */
    protected String getQueueTag() {
        return null;
    }

    /**
     * doProcessMessage:具体执行的回调方法
     *
     * @param message
     * <AUTHOR>
     * @date 2016年10月13日 下午3:03:58
     */
    protected abstract void doProcessMessage(SimpleMessage message);

}
