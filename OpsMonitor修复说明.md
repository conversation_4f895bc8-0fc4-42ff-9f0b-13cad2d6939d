# OpsMonitor.warn() 日志不打印问题修复说明

## 问题描述
`OpsMonitor.warn(msg, OpsMonitor.ERROR)` 调用时，日志没有打印到 warn.log 文件中。

## 问题根因分析

### 调用链分析
1. `OnWayMonitorProcessor` (继承自 `BatchMessageProcessor`) 
2. → `OnWayMonitorService.process()`
3. → `OpsMonitor.warn()` (在 finally 块中调用)

### 问题原因
在 `BatchMessageProcessor.processMessage()` 方法中：

1. **设置上下文**：在方法开始时设置 ThreadContext 和 MDC
   ```java
   ThreadContext.put("uuid", uuid);
   ThreadContext.put("ranNo", currentRanNo);
   ```

2. **执行业务逻辑**：调用 `doProcessMessage()` → `OnWayMonitorService.process()`

3. **清理上下文**：在 finally 块中清理 ThreadContext
   ```java
   ThreadContext.remove("uuid");
   ThreadContext.remove("ranNo");
   ```

4. **问题所在**：`OpsMonitor.warn()` 在 `OnWayMonitorService.process()` 的 finally 块中调用，此时 ThreadContext 已经被清理，导致获取不到 uuid。

### 原始代码问题
```java
// OpsMonitor.warn() 原始实现
String uuid = ThreadContext.get("uuid");  // 此时已经被清理，返回 null
warnMsg.put("traceId", uuid);  // traceId 为 null
```

## 解决方案

修改 `OpsMonitor.warn()` 方法，增加多级 fallback 机制来获取 traceId：

```java
// 获取traceId，优先级：ThreadContext > MDC > RequestChainTrace > 默认值
String uuid = ThreadContext.get("uuid");
if (StringUtils.isEmpty(uuid)) {
    uuid = MDC.get("uuid");
}
if (StringUtils.isEmpty(uuid)) {
    uuid = RequestChainTrace.getReqId();
}
if (StringUtils.isEmpty(uuid)) {
    uuid = "unknown-uuid";
}
```

### 优先级说明
1. **ThreadContext** - 优先使用 Log4j2 的 ThreadContext
2. **MDC** - 如果 ThreadContext 为空，使用 SLF4J 的 MDC
3. **RequestChainTrace** - 如果 MDC 也为空，使用项目的 RequestChainTrace
4. **默认值** - 如果都为空，使用 "unknown-uuid"

## 修复效果

### 修复前
- `OpsMonitor.warn()` 只能从 ThreadContext 获取 uuid
- 在某些调用场景下 ThreadContext 已被清理，导致 traceId 为 null
- 日志无法正确记录 traceId，影响问题追踪

### 修复后
- 支持多种来源获取 traceId，增强了健壮性
- 即使 ThreadContext 被清理，仍能从 RequestChainTrace 获取 traceId
- 确保日志始终有有效的 traceId，便于问题追踪

## 验证方法

可以通过以下方式验证修复效果：

1. **查看 warn.log 文件**：确认 OpsMonitor.warn() 的日志能够正常输出
2. **检查 traceId 字段**：确认日志中的 traceId 不再为 null
3. **测试不同场景**：验证在各种上下文环境下都能正确获取 traceId

## 影响范围

此修复只影响 `OpsMonitor.warn()` 方法的 traceId 获取逻辑，不会影响其他功能：

- ✅ 向后兼容：原有的 ThreadContext 方式仍然有效
- ✅ 无副作用：只是增加了 fallback 机制
- ✅ 性能影响微小：只是增加了几个条件判断

## 相关文件

- **修改文件**：`high-batch-center-service/src/main/java/com/howbuy/tms/high/batch/service/common/OpsMonitor.java`
- **测试文件**：`high-batch-center-service/src/test/java/com/howbuy/tms/high/batch/service/common/OpsMonitorTest.java`
