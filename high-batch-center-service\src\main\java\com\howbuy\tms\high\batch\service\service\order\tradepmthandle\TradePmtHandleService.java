/**
 * Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.batch.service.service.order.tradepmthandle;


import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.enums.TxPmtFlagEnum;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.order.*;
import com.howbuy.tms.high.batch.service.business.interposeflagprocess.InterposeFlagProcessService;
import com.howbuy.tms.high.batch.service.business.message.MsgNotifySendService;
import com.howbuy.tms.high.batch.service.repository.*;
import com.howbuy.tms.high.batch.service.service.order.dealorder.DealOrderService;
import com.howbuy.tms.high.batch.service.service.sequence.SequenceService;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:(支付处理)
 * @reason:
 * @date 2018年5月18日 下午2:43:36
 * @since JDK 1.6
 */
@Service("tradePmtHandleService")
public class TradePmtHandleService {

    private static final Logger logger = LoggerFactory.getLogger(TradePmtHandleService.class);
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private CustProtocolRepository custProtocolRepository;
    @Autowired
    private CustBooksDtlRepository custBooksDtlRepository;
    @Autowired
    private DealOrderRepository dealOrderRepository;
    @Autowired
    private InterposeFlagProcessService interposeFlagProcessService;
    @Autowired
    private EsProOrderMonitorRepository esProOrderMonitorRepository;
    @Autowired
    private PaymentOrderRepository paymentOrderRepository;
    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;
    @Autowired
    private MsgNotifySendService msgNotifySendService;
    @Autowired
    private DealOrderService dealOrderService;

    public void updateStateOfDelOdrAndPmtOrdDtl(HighDealOrderDtlPo highDealOrderDtlPo, PaymentOrderPo pmtOrder, boolean isForceUpdate) {

        pmtOrder.setTxPmtFlag(TxPmtFlagEnum.NOT_NEED.getKey());
        pmtOrder.setPmtCompFlag(PmtCompFlagEnum.NOT_NEED.getCode());

        // 提交修改,更新支付订单
        paymentOrderRepository.updateFlagOfTxPmtAndPmtComp(pmtOrder, isForceUpdate);

        if (highDealOrderDtlPo != null) {
            // 提交修改，更新高端订单明细
            highDealOrderDtlRepository.updateStateOfNotify(highDealOrderDtlPo.getDealDtlNo(), null, NotifySubmitFlagEnum.NO_NEED.getCode(), highDealOrderDtlPo.getUpdateDtm(), isForceUpdate);
        }

    }

    /**
     * updateStateOfDelOdrAndPmdOdr:更新客户明细订单通知上报状态,支付明细订单的交易支付标记和对账状态
     *
     * @param order              交易订单
     * @param highDealOrderDtlPo 客户明细订单
     * @param pmtDelOdr          支付明细订单
     * @param isForceUpdate      是否强制更新（不带最后更新时间戳），true：强制更新，false：带时间戳更新
     * @param processResult      额度处理结果 true 校验通过 false 校验失败
     * <AUTHOR>
     * @date 2018年5月21日 上午13:47:15
     */
    public void updateStateOfDelOdrAndPmdOdr(DealOrderPo order, HighDealOrderDtlPo highDealOrderDtlPo, PaymentOrderPo pmtDelOdr,
                                             boolean processResult, boolean isForceUpdate, String esSysCode) {
        paymentOrderRepository.updateFlagOfTxPmtAndPmtComp(pmtDelOdr, isForceUpdate);
        // 新增监控
        EsProOrderMonitorPo monitor = new EsProOrderMonitorPo();
        monitor.setDealNo(order.getDealNo());
        monitor.setSysCode(esSysCode);
        monitor.setTxAcctNo(order.getTxAcctNo());
        esProOrderMonitorRepository.insertSelective(monitor);

        if (!TxAppFlagEnum.APP_SUCCESS.getCode().equals(highDealOrderDtlPo.getTxAppFlag())) {
            return;
        }

        highDealOrderDtlPo.setTxAppFlag(null);
        highDealOrderDtlPo.setNotifySubmitFlag(null);
        Date now = new Date();
        if (TxPmtFlagEnum.FAILED.getKey().equals(pmtDelOdr.getTxPmtFlag())) {
            // 支付失败
            highDealOrderDtlPo.setTxAppFlag(TxAppFlagEnum.APP_FAIL.getCode());
            highDealOrderDtlPo.setNotifySubmitFlag(NotifySubmitFlagEnum.NO_NEED.getCode());

            // 更新订单状态
            UpdateOrderStatusParams params = new UpdateOrderStatusParams();
            params.setTxAppFlag(highDealOrderDtlPo.getTxAppFlag());
            params.setNotifySubmitFlag(highDealOrderDtlPo.getNotifySubmitFlag());
            params.setOrderStatus(OrderStatusEnum.FORCE_CANCELED.getCode());
            params.setPayStatus(PayStatusEnum.PAY_FAIL.getCode());
            updateOrderStatus(order, highDealOrderDtlPo, params, now);

        } else if (TxPmtFlagEnum.PIGGY_FRZ_FAIL.getKey().equals(pmtDelOdr.getTxPmtFlag())) {
            // 冻结失败
            logger.info("TradePmtHandleService|冻结失败，不更新订单状态");
        } else if (TxPmtFlagEnum.SUCCESSFUL.getKey().equals(pmtDelOdr.getTxPmtFlag())
                || TxPmtFlagEnum.PIGGY_FRZ_SUCC.getKey().equals(pmtDelOdr.getTxPmtFlag())) {
            logger.info("TradePmtHandleService|updateStateOfDelOdrAndPmdOdr|highDealOrderDtlPo:{}", JSON.toJSONString(highDealOrderDtlPo));
            // 支付成功，计算冷静时间
            Date calmDtm = calcCalmDtm(order, pmtDelOdr, highDealOrderDtlPo);
            logger.info("TradePmtHandleService|updateStateOfDelOdrAndPmdOdr|dealNo:{}, calmDtm:{}", order.getDealNo(), calmDtm);

            // 额度处理失败
            if (!processResult) {
                // 更新订单状态
                UpdateOrderStatusParams params = new UpdateOrderStatusParams();
                params.setCalmDtm(calmDtm);
                params.setTxAppFlag(TxAppFlagEnum.APP_FAIL.getCode());
                params.setNotifySubmitFlag(NotifySubmitFlagEnum.NO_NEED.getCode());
                params.setCancelSrc(CancelOrderSrcEnum.LIMIT_CANCEL.getCode());
                params.setOrderStatus(OrderStatusEnum.FORCE_CANCELED.getCode());
                params.setPayStatus(PayStatusEnum.PAY_SUC.getCode());
                updateOrderStatus(order, highDealOrderDtlPo, params, now);

                // 订单干预标识处理
                interposeFlagProcessService.interposeFlagProc(highDealOrderDtlPo);
                return;
            } else {
                highDealOrderDtlPo.setTxAppFlag(TxAppFlagEnum.APP_SUCCESS.getCode());

                // 更新订单状态
                UpdateOrderStatusParams params = new UpdateOrderStatusParams();
                params.setCalmDtm(calmDtm);
                params.setTxAppFlag(highDealOrderDtlPo.getTxAppFlag());
                params.setNotifySubmitFlag(highDealOrderDtlPo.getNotifySubmitFlag());
                params.setPayStatus(PayStatusEnum.PAY_SUC.getCode());
                updateOrderStatus(order, highDealOrderDtlPo, params, now);

                // 订单干预标识处理
                interposeFlagProcessService.interposeFlagProc(highDealOrderDtlPo);
            }

            // 成功需要记录账务变动明细 支付成功或冻结成功
            addCustBooksDtl(order, highDealOrderDtlPo, pmtDelOdr, now);
        }

    }

    /**
     * 支付完成更新订单状态
     *
     * @param order
     * @param highDealOrderDtlPo
     * @param params
     * @param now
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/6/7 16:42
     * @since JDK 1.8
     */
    private void updateOrderStatus(DealOrderPo order, HighDealOrderDtlPo highDealOrderDtlPo, UpdateOrderStatusParams params, Date now) {
        // 更新订单明细
        highDealOrderDtlRepository.updateStateOfNotify(highDealOrderDtlPo.getDealDtlNo(), params.getTxAppFlag(),
                params.getNotifySubmitFlag(), highDealOrderDtlPo.getUpdateDtm(), true, params.getCalmDtm(), params.getCancelSrc());

        // 更新订单状态
        dealOrderService.updateOrderStatOrPayStat(order.getDealNo(), params.getOrderStatus(), params.getPayStatus(), null, now);

        logger.info("TradePmtHandleService|updateOrderStatus|dealNo:{},mergeSubmitFlag:{},mainDealOrderNo:{},txAppFlag:{}", order.getDealNo(), highDealOrderDtlPo.getMergeSubmitFlag(), highDealOrderDtlPo.getMainDealOrderNo(), params.getTxAppFlag());
        // 合并上报单，更新其他订单
        if (YesOrNoEnum.YES.getCode().equals(highDealOrderDtlPo.getMergeSubmitFlag())) {
            if (TxAppFlagEnum.APP_FAIL.getCode().equals(params.getTxAppFlag())) {
                // 更新其他订单为 强制撤单，无需通知
                String workDay = queryTradeDayOuterService.getWorkDay(now);
                int cancelDtlNum = highDealOrderDtlRepository.updateFaceCancelNoNeedNotifyOtherMergeOrders(
                        highDealOrderDtlPo.getMainDealOrderNo(), workDay, CancelOrderSrcEnum.MERGE_ORDER_PAY_FAIL.getCode(), now);
                // 更新其他未支付的订单为无需支付
                int cancelPayNum = paymentOrderRepository.updateNoNeedPayOtherMergeOrders(highDealOrderDtlPo.getMainDealOrderNo(), now);
                // 更新其他订单为 强制撤单
                int cancelOrderNum = dealOrderRepository.updateOrderForceCancelOtherMergeOrders(highDealOrderDtlPo.getMainDealOrderNo(), now);
                logger.info("TradePmtHandleService|updateOrderStatus|cancel other order dtlNum:{},payNum:{},orderNum:{}", cancelDtlNum, cancelPayNum, cancelOrderNum);
                // 储蓄罐退款
                List<String> dealNoList = paymentOrderRepository.selectNeedCxgPayCancelOrders(highDealOrderDtlPo.getMainDealOrderNo());
                logger.info("TradePmtHandleService|updateOrderStatus|refundOrders:{}", JSON.toJSONString(dealNoList));
                if (!CollectionUtils.isEmpty(dealNoList)) {
                    String appDt = DateUtils.formatToString(now, DateUtils.YYYYMMDD);
                    String appTm = DateUtils.formatToString(now, DateUtils.HHMMSS);
                    for (String dealNo : dealNoList) {
                        msgNotifySendService.sendCxgPayCancelMsg(dealNo, appDt, appTm);
                    }
                }
            }
        }
    }

    @Data
    private static class UpdateOrderStatusParams {
        private String txAppFlag = null;
        private String notifySubmitFlag = null;
        private String cancelSrc = null;
        private Date calmDtm = null;
        private String orderStatus = null;
        private String payStatus = null;
    }

    /**
     * 成功记录账务变动明细
     *
     * @param order
     * @param highDealOrderDtlPo
     * @param pmtDelOdr
     * @param now
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/6/7 16:42
     * @since JDK 1.8
     */
    private void addCustBooksDtl(DealOrderPo order, HighDealOrderDtlPo highDealOrderDtlPo, PaymentOrderPo pmtDelOdr, Date now) {
        // 新增账务变动明细
        CustBooksDtlPo bookPo = new CustBooksDtlPo();
        bookPo.setRecordNo(sequenceService.getCustBooksDtlNo(pmtDelOdr.getTxAcctNo()));
        bookPo.setAppAmt(highDealOrderDtlPo.getNetAppAmt());

        ChangeBusiCodeEnum chgBusiCode = ChangeBusiCodeEnum.PUR_APP;
        if (BusinessCodeEnum.SUBS.getMCode().equals(highDealOrderDtlPo.getmBusiCode())) {
            chgBusiCode = ChangeBusiCodeEnum.SUBS_APP;
        } else if (BusinessCodeEnum.FUND_SCHEDULE.getMCode().equals(highDealOrderDtlPo.getmBusiCode())) {
            chgBusiCode = ChangeBusiCodeEnum.FUND_SCHEDULE_APP;
        }
        bookPo.setChangeBusiCode(chgBusiCode.getCode());
        bookPo.setCpAcctNo(pmtDelOdr.getCpAcctNo());
        bookPo.setDealDtlNo(highDealOrderDtlPo.getDealDtlNo());
        bookPo.setDealNo(highDealOrderDtlPo.getDealNo());
        bookPo.setDisCode(highDealOrderDtlPo.getDisCode());
        bookPo.setDisTxAcctNo(order.getDisTxAcctNo());
        bookPo.setFundShareClass(highDealOrderDtlPo.getFundShareClass());
        bookPo.setProductCode(highDealOrderDtlPo.getFundCode());
        bookPo.setProductChannel(highDealOrderDtlPo.getProductChannel());
        bookPo.setProductName(highDealOrderDtlPo.getFundName());
        bookPo.setProductType(highDealOrderDtlPo.getFundType());
        String protocolNo = pmtDelOdr.getProtocolNo();
        CustProtocolPo protocol = custProtocolRepository.getByProtocolNo(protocolNo);
        if (protocol != null) {
            bookPo.setProtocolType(protocol.getProtocolType());
        }
        bookPo.setProtocolNo(protocolNo);
        bookPo.setTaTradeDt(highDealOrderDtlPo.getSubmitTaDt());
        bookPo.setTradeDt(DateUtils.formatToString(now, DateUtils.YYYYMMDD));
        bookPo.setTradeTm(DateUtils.formatToString(now, DateUtils.HHMMSS));
        bookPo.setTxAcctNo(pmtDelOdr.getTxAcctNo());
        bookPo.setTaCode(highDealOrderDtlPo.getTaCode());
        bookPo.setProductClass(order.getProductClass());// 产品类别

        custBooksDtlRepository.add(bookPo);
    }

    /**
     * calcCalmDtm:计算冷静时间
     *
     * @param order
     * @param pmtDelOdr
     * @param highDealOrderDtl
     * @return
     * <AUTHOR>
     * @date 2017年7月11日 上午9:51:43
     */
    private Date calcCalmDtm(DealOrderPo order, PaymentOrderPo pmtDelOdr, HighDealOrderDtlPo highDealOrderDtl) {

        int calmTime = 0;
        if (highDealOrderDtl.getCalmTime() != null) {
            calmTime = highDealOrderDtl.getCalmTime();
        }

        logger.info("calcCalmDtm|dealNo:{}, calmTime:{}", order.getDealNo(), calmTime);

        // 自划款支付方式的冷静期非柜台下单从客户网银打款成功时间和交易下单时间，两者取较晚时间开始计算
        // 柜台下单从客户网银打款成功时间开始计算
        if (PaymentTypeEnum.SELF_DRAWING.getCode().equals(pmtDelOdr.getPaymentType())) {
            Date orderAppDtm = order.getAppDtm();
            Date ptmCompleteDtm = pmtDelOdr.getPmtCompleteDtm();
            if (TxChannelEnum.COUNTER.getCode().equals(order.getTxChannel())) {
                return DateUtils.addHourOfDay(ptmCompleteDtm, calmTime);
            } else {
                if (orderAppDtm.before(ptmCompleteDtm)) {
                    return DateUtils.addHourOfDay(ptmCompleteDtm, calmTime);
                }
            }

            return DateUtils.addHourOfDay(orderAppDtm, calmTime);
        }
        return DateUtils.addHourOfDay(pmtDelOdr.getAppDtm(), calmTime);
    }
}

