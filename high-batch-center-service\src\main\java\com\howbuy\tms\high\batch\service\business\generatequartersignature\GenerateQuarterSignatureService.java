/**
 * Copyright (c) 2022, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.generatequartersignature;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.howbuy.dfile.HInputStream;
import com.howbuy.interlayer.product.model.FundManModel;
import com.howbuy.interlayer.product.service.HighProductParamConfService;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.HighProductAgreementValueEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.SignatureStatusEnums;
import com.howbuy.tms.common.outerservice.acccenter.querycustsensitiveinfo.QueryCustSensitiveInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustsensitiveinfo.QueryCustSensitiveInfoOutService;
import com.howbuy.tms.common.outerservice.acccenter.querycustsensitiveinfo.QueryCustSensitiveInfoResult;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.facade.query.querycustesignature.QueryCustEsignatureRequest;
import com.howbuy.tms.high.batch.facade.query.querycustesignature.QueryCustEsignatureResponse;
import com.howbuy.tms.high.batch.facade.query.querycustesignature.bean.CustEsignatureBean;
import com.howbuy.tms.high.batch.facade.query.querycustesignature.bean.QueryCustEsignatureCondition;
import com.howbuy.tms.high.batch.service.common.utils.FileSdkPathInfo;
import com.howbuy.tms.high.batch.service.common.utils.FileSdkUtil;
import com.howbuy.tms.high.batch.service.common.utils.HowBuyRunTaskUil;
import com.howbuy.tms.high.batch.service.common.utils.AbstractHowbuyBaseTask;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.facade.query.querycustesignature.QueryCustEsignatureFacadeService;
import com.howbuy.tms.high.batch.service.service.file.fileexport.excel.bean.ExcelFileExportContext;
import com.howbuy.tms.high.batch.service.service.file.fileexport.excel.impl.ExportQuarterSignatureService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: (每季度, 生成电子合同, 风险书, 生成电子签名明细excel)
 * @date 2022/2/15 14:37
 * @since JDK 1.8
 */
@Service
public class GenerateQuarterSignatureService {
    private static final Logger logger = LoggerFactory.getLogger(GenerateQuarterSignatureService.class);

    /**
     * 队列容量
     */
    private static final int POOL_CAPACITY = 10240;


    @Autowired
    private QueryCustEsignatureFacadeService queryCustEsignatureFacadeService;

    @Autowired
    private QueryCustSensitiveInfoOutService queryCustSensitiveInfoOutService;

    @Autowired
    private HighProductParamConfService highProductParamConfService;

    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;

    @Autowired
    private ExportQuarterSignatureService exportQuarterSignatureService;


    public void process() {
        // 季度目录
        String date = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD);
        String quarterPath = getQuarterByDate(date);
        // 上一个季度的开始日期
        String ackStartDt = getBeforQuarterStartDt();
        // 上一个季度的结束日期
        String ackEndDt = getBeforeQuarterEndDt();
        List<FundManModel> fundManList = highProductParamConfService.getAllManagerInfo();
        if (!CollectionUtils.isEmpty(fundManList)) {
            processTask(fundManList, quarterPath, ackStartDt, ackEndDt);
        }
    }

    public static ExecutorService createFixedThreadPool(String threadName, int poolSize) {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat(threadName).build();
        return new ThreadPoolExecutor(poolSize, poolSize,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(POOL_CAPACITY), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());
    }

    /**
     * @description:(执行任务类)
     * @return
     * @author: haiguang.chen
     * @date: 2021/11/26 14:09
     * @since JDK 1.8
     */
    private class GenerateQuarterSignatureTask extends AbstractHowbuyBaseTask {

        private FundManModel fundManModel;
        private String quarterPath;
        private String ackStartDt;
        private String ackEndDt;

        public GenerateQuarterSignatureTask(FundManModel fundManModel, String quarterPath, String ackStartDt, String ackEndDt) {
            this.fundManModel = fundManModel;
            this.quarterPath = quarterPath;
            this.ackStartDt = ackStartDt;
            this.ackEndDt = ackEndDt;
        }

        @Override
        protected void callTask() {
            try {

                String mainMiddlePath = File.separator + quarterPath + File.separator + fundManModel.getFundManName() + "+" + quarterPath;
                List<CustEsignatureBean> custEsignatureBeanList = getCustEsignatureBeans();
                if (!CollectionUtils.isEmpty(custEsignatureBeanList)) {
                    for (CustEsignatureBean bean : custEsignatureBeanList) {
                        // 信息补全
                        QueryCustSensitiveInfoContext context = new QueryCustSensitiveInfoContext();
                        context.setTxAcctNo(bean.getTxAcctNo());
                        context.setDisCode(DisCodeEnum.HM.getCode());
                        QueryCustSensitiveInfoResult result = queryCustSensitiveInfoOutService.queryCustSensitiveInfo(context);
                        bean.setCustName(result.getCustName());
                        bean.setIdNo(result.getIdNo());

                        // 复制风险揭示书/电子合同，并重命名
                        List<HighProductAgreementValueEnum> fileTypes = new ArrayList<>();
                        fileTypes.add(HighProductAgreementValueEnum.RISK_NOTICE);
                        fileTypes.add(HighProductAgreementValueEnum.ELEC_CONTRACT);
                        for (HighProductAgreementValueEnum fileType : fileTypes) {
                            generateRiskAndContractFile(fileType, bean, mainMiddlePath);
                        }
                    }
                    // 生成电子签名明细
                    generateNewFile(fundManModel.getFundManName(),custEsignatureBeanList,mainMiddlePath);
                }
            } catch (Exception e) {
                logger.error("GenerateQuarterSignatureDetailsTask|fundManModel{}", fundManModel.getFundManCode(), e);
            }
        }



        private void generateNewFile(String fundManName, List<CustEsignatureBean> custEsignatureBeanList, String middlePath) {
            try {
                ExcelFileExportContext excelFileExportContext = new ExcelFileExportContext();
                excelFileExportContext.setFileName("电子签名明细+" + fundManName + "+" + quarterPath + ".xls");
                List<Map<String, Object>> dataList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(custEsignatureBeanList)) {
                    for (CustEsignatureBean bean : custEsignatureBeanList) {
                        Map<String, Object> map = new HashMap<String, Object>(16);
                        map.put("getCustName", bean.getCustName());
                        map.put("getIdNo", bean.getIdNo());
                        map.put("getProductCode", bean.getProductCode());
                        map.put("getProductName", bean.getProductName());
                        map.put("getAppAmt", bean.getAppAmt());
                        map.put("getCreateDt", DateUtils.formatToString(bean.getCreateDtm(), DateUtils.YYYY_MM_DD));
                        map.put("getCreateTm", DateUtils.formatToString(bean.getCreateDtm(), DateUtils.HH_MM_SS));
                        map.put("getSignatureStatus", SignatureStatusEnums.getName(bean.getSignatureStatus()));
                        dataList.add(map);
                    }
                }
                excelFileExportContext.setDataMapList(dataList);
                excelFileExportContext.setRelationPath(middlePath);
                excelFileExportContext.setSheetName("电子签名明细");
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("taTradeDt",DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD));
                excelFileExportContext.setParams(paramMap);
                 exportQuarterSignatureService.process(excelFileExportContext);
            } catch (Exception e) {
                logger.info("每季度电子签约明细,生成异常:", e);
            }
            }


        private void generateRiskAndContractFile(HighProductAgreementValueEnum agreementType, CustEsignatureBean custEsignatureBean, String mainMiddlePath) throws Exception {
            String absolutePath = null;
            String newPath = null;
            // 原路径
            FileSdkPathInfo originalFileSdkPathInfo = new FileSdkPathInfo();
            if (HighProductAgreementValueEnum.RISK_NOTICE.getCode().equals(agreementType.getCode())) {
                // 原路径
                absolutePath = File.separator + custEsignatureBean.getTaCode() + File.separator + custEsignatureBean.getProductCode() + File.separator;
                originalFileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.PUBLIC_FUND_DOC);
                // 新路径
                newPath = File.separator + "风险揭示书" + File.separator + custEsignatureBean.getProductCode() + "+" + custEsignatureBean.getProductName();
            } else {
                // 原路径
                absolutePath = File.separator + custEsignatureBean.getTaCode() + File.separator + custEsignatureBean.getProductCode() + File.separator;
                originalFileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.PUBLIC_TRADEDOC);
                // 新路径
                newPath = File.separator + "电子合同" + File.separator + custEsignatureBean.getProductCode() + "+" + custEsignatureBean.getProductName();
            }

            originalFileSdkPathInfo.setMiddlePath(absolutePath);
            // 新路径
            FileSdkPathInfo newRiskFileSdkPathInfo = new FileSdkPathInfo();
            newRiskFileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.TRADEOC_EMPTY_STORE);
            newRiskFileSdkPathInfo.setMiddlePath(mainMiddlePath + newPath + File.separator);
            // 判断原始文件是否存在
            // 原始文件-输入
            String inFileName = EsignatrueUtils.getBuyEsignFileName(custEsignatureBean.getSignatureSid(), custEsignatureBean.getTxAcctNo(), agreementType.getCode(), custEsignatureBean.getProductCode());
            originalFileSdkPathInfo.setFileName(inFileName);
            if (!FileSdkUtil.exists(originalFileSdkPathInfo)) {
                logger.info(FileSdkUtil.getAbsolutePath(originalFileSdkPathInfo) + ",inFile is not exist!");
                return;
            }
            // 新文件-输出
            String outFileName = inFileName.split("\\.")[0] + "_" + custEsignatureBean.getCustName() + ".pdf";
            newRiskFileSdkPathInfo.setFileName(outFileName);
            HInputStream in = null;
            try {
                in = FileSdkUtil.buildHInputStream(originalFileSdkPathInfo);
                FileSdkUtil.write(newRiskFileSdkPathInfo,in.getInputStream());
            } catch (Exception ex) {
                logger.error("生成文件{},文件名:{},处理生成失败", FileSdkUtil.getAbsolutePath(newRiskFileSdkPathInfo), outFileName, ex);
            } finally {
                if (in != null) {
                    in.close();
                }
            }
        }

        private List<CustEsignatureBean> getCustEsignatureBeans() {
            QueryCustEsignatureRequest request = new QueryCustEsignatureRequest();
            request.setPageNo(1);
            request.setPageSize(5000);
            QueryCustEsignatureCondition condition = new QueryCustEsignatureCondition();

            // 管理人代码
            List<String> fundManCodeList = new ArrayList<>();
            fundManCodeList.add(fundManModel.getFundManCode());
            condition.setFundManCode(fundManCodeList);
            condition.setFirstFlag("1");
            condition.setSignatureStatus("2");
            condition.setAckDtStart(ackStartDt);
            condition.setIsHBJGAuth(YesOrNoEnum.YES.getCode());
            condition.setAckDtEnd(ackEndDt);
            request.setQueryCustEsignatureCondition(condition);
            request.setFirstFlag("1");

            QueryCustEsignatureResponse queryCustEsignatureResponse = queryCustEsignatureFacadeService.process(request);
            return queryCustEsignatureResponse.getCustEsignatureBeanList();
        }

    }

    private String getQuarterByDate(String date) {
        Integer year = Integer.valueOf(date.substring(0, 4));
        int mouth = Integer.parseInt(date.substring(4, 6));

        if (mouth >= 1 && mouth <= 3) {
            return (year - 1) + "年第四季度";
        } else if (mouth >= 4 && mouth <= 6) {
            return year + "年第一季度";
        } else if (mouth >= 7 && mouth <= 9) {
            return year + "年第二季度";
        } else if (mouth >= 10 && mouth <= 12) {
            return year + "年第三季度";
        } else {
            return null;
        }
    }

    /**
     * 获取上一个季度的开始日期
     *
     * @return
     */
    private String getBeforQuarterStartDt() {
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.set(Calendar.MONTH, (startCalendar.get(Calendar.MONTH) / 3 - 1) * 3);
        startCalendar.set(Calendar.DAY_OF_MONTH, 1);
        setMinTime(startCalendar);
        return DateUtils.formatToString(startCalendar.getTime(), DateUtils.YYYYMMDD);
    }

    /**
     * 获取上一个季度的结束日期
     *
     * @return
     */
    private String getBeforeQuarterEndDt() {
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.set(Calendar.MONTH, ((int) endCalendar.get(Calendar.MONTH) / 3 - 1) * 3 + 2);
        endCalendar.set(Calendar.DAY_OF_MONTH, endCalendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        setMaxTime(endCalendar);
        return DateUtils.formatToString(endCalendar.getTime(), DateUtils.YYYYMMDD);
    }

    private static void setMinTime(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
    }

    private static void setMaxTime(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMaximum(Calendar.HOUR_OF_DAY));
        calendar.set(Calendar.MINUTE, calendar.getActualMaximum(Calendar.MINUTE));
        calendar.set(Calendar.SECOND, calendar.getActualMaximum(Calendar.SECOND));
        calendar.set(Calendar.MILLISECOND, calendar.getActualMaximum(Calendar.MILLISECOND));
    }

    public void processTask(List<FundManModel> list, String quarterPath, String ackStartDt, String ackEndDt) {
        List<GenerateQuarterSignatureTask> taskList = new ArrayList<>();
        for (FundManModel fundManModel : list) {
            taskList.add(new GenerateQuarterSignatureTask(fundManModel, quarterPath, ackStartDt, ackEndDt));
        }
        howBuyRunTaskUil.runTask(taskList);
    }

}