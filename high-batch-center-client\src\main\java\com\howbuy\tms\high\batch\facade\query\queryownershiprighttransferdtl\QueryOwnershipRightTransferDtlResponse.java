package com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl;

import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.bean.AbstractOwnershipRightTransferDtlBean;
import lombok.Data;

/**
 * @Description:查询股权份额订单详情响应实体
 * @Author: yun.lu
 * Date: 2023/5/18 17:01
 */
@Data
public class QueryOwnershipRightTransferDtlResponse extends BaseResponse {
    private static final long serialVersionUID = -185424232325423137L;
    /**
     * 非交易转让信息
     */
    private AbstractOwnershipRightTransferDtlBean abstractOwnershipRightTransferDtlBean;
}
