/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.query.querytadayclose;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.BatchStatEnum;
import com.howbuy.tms.common.enums.database.BusinessProcessingStepEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.high.batch.dao.po.batch.BusinessBatchFlowPo;
import com.howbuy.tms.high.batch.dao.po.batch.TaBusinessBatchFlowPo;
import com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusFacade;
import com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusRequest;
import com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusResponse;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.repository.BusinessBatchFlowRepository;
import com.howbuy.tms.high.batch.service.repository.TaBusinessBatchFlowRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * Description:查询高端TA收市状态接口实现类
 *
 * <AUTHOR>
 * @date 2025-05-15 15:35:23
 * @since JDK 1.8
 */
@Slf4j
@DubboService
@Service("queryHighTaDayCloseStatusFacade")
public class QueryHighTaDayCloseStatusFacadeService implements QueryHighTaDayCloseStatusFacade {

    @Autowired
    private BusinessBatchFlowRepository businessBatchFlowRepository;

    @Autowired
    private TaBusinessBatchFlowRepository taBusinessBatchFlowRepository;

    //0-已收市，1-未收市，2-不支持TA
    private static final String ALREADY_DAY_CLOSE = "0";
    private static final String NOT_DAY_CLOSE = "1";
    private static final String NOT_SUPPORTED_TA = "2";

    /**
     * @api {DUBBO} com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusFacade.execute()
     * @apiGroup QueryHighTaDayCloseStatusFacadeService
     * @apiName execute()
     * @apiDescription 查询高端TA收市状态接口
     * @apiParam (请求体) {String} taCode TA代码
     * @apiParam (请求体) {String} workDay 工作日
     * @apiParamExample 请求体示例
     * {"sysCode":"jNWmq446Z4","pageSize":9473,"disCode":"F1Aa","workDay":"dvqY","txChannel":"m2uNTXeNP4","appTm":"mnqp3G","taCode":"gPL","subOutletCode":"6t3622wPnW","pageNo":9882,"operIp":"Y","appDt":"xn","dataTrack":"99lnOj4n6r","txCode":"Vt3HB","outletCode":"XP"}
     * @apiSuccess (响应结果) {String} dayCloseStatus 日终状态      0-已收市，1-未收市，2-不支持TA
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} description
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"QzbL","description":"Na","dayCloseStatus":"345k5M"}
     */
    @Override
    public QueryHighTaDayCloseStatusResponse execute(QueryHighTaDayCloseStatusRequest request) {
        QueryHighTaDayCloseStatusResponse response = new QueryHighTaDayCloseStatusResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));

        if (StringUtils.isEmpty(request.getTaCode()) || StringUtils.isEmpty(request.getWorkDay())) {
            log.error("QueryHighTaDayCloseStatusFacadeService|execute|taCode:{} or workDay:{} is empty", request.getTaCode(), request.getWorkDay());
            response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
            response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.PARAMS_ERROR));
            return response;
        }

        //1、根据workDay、taskId=101-日初始化 查询业务流程处理表-BUSINESS_BATCH_FLOW，记录不存在则返回异常returnCode=待定，description=工作日未初始化
        BusinessBatchFlowPo batchFlowPo = businessBatchFlowRepository.selectByTaskIdAndTradeDtAndSysCode(
                BusinessProcessingStepEnum.BPS_WORK_DAY_INIT.getCode(), request.getWorkDay(), SysCodeEnum.BATCH_HIGH.getCode());
        if (Objects.isNull(batchFlowPo)) {
            response.setReturnCode(ExceptionCodes.HIGH_BATCH_WORKDAY_NOT_DAY_INIT);
            response.setDescription(null);
            return response;
        }
        //2、根据taCode、workDay、taskId=109-柜台收市 查询TA业务批处理流程处理表-TA_BUSINESS_BATCH_FLOW
        TaBusinessBatchFlowPo taBusiness = taBusinessBatchFlowRepository.selectByTaCodeAndSysCodeAndTradeDtAndTaskId(
                request.getTaCode(), SysCodeEnum.BATCH_HIGH.getCode(), request.getWorkDay(),
                BusinessProcessingStepEnum.BPS_COUNTER_DAY_CLOSE.getCode());
        if (Objects.isNull(taBusiness)) {
            response.setDayCloseStatus(NOT_SUPPORTED_TA);
        } else {
            if (BatchStatEnum.PROCESS_SUCCESS.getKey().equals(taBusiness.getFlowStat())) {
                response.setDayCloseStatus(ALREADY_DAY_CLOSE);
            } else {
                response.setDayCloseStatus(NOT_DAY_CLOSE);
            }
        }
        return response;
    }
}