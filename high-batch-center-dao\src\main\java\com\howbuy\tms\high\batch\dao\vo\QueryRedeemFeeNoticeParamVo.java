package com.howbuy.tms.high.batch.dao.vo;

import lombok.Data;

import java.util.List;

/**
 * @Description:查询赎回费告警入参
 * @Author: yun.lu
 * Date: 2025/6/10 18:02
 */
@Data
public class QueryRedeemFeeNoticeParamVo {
    /**
     * 查询开始日期,yyyyMMdd
     */
    private String queryStartDt;
    /**
     * 查询结束日期,yyyyMMdd
     */
    private String queryEndDt;
    /**
     * 基金代码
     */
    private List<String> fundCodeList;
    /**
     * 上报状态,0-无需上报,1-上报中,2-上报完成,3-需重新上报
     */
    private List<String> submitAppFlagList;

    /**
     * 需要查询没有查询上报表的数据,1:需要,其他不需要
     */
    private String needQueryWithOutSubmitAppFlag;
    /**
     * 页码
     */
    private int pageNo = 1;
    /**
     * 页面大小
     */
    private int pageSize = 20;
}
