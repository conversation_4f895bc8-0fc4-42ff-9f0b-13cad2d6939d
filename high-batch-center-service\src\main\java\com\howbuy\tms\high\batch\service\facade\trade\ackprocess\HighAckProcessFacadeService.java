/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.trade.ackprocess;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.howbuy.tms.cache.service.lock.LockService;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.enums.database.WorkdayTypeEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.interlayer.querytainfo.QueryTaInfoOuterService;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.TradeParamLocalUtils;
import com.howbuy.tms.high.batch.dao.po.batch.WorkdayPo;
import com.howbuy.tms.high.batch.dao.po.order.DealOrderPo;
import com.howbuy.tms.high.batch.facade.trade.ackprocess.HighAckProcessFacade;
import com.howbuy.tms.high.batch.facade.trade.ackprocess.HighAckProcessRequest;
import com.howbuy.tms.high.batch.facade.trade.ackprocess.HighAckProcessResponse;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.common.ThreadExceptionStatus;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.facade.trade.task.AckProcessTask;
import com.howbuy.tms.high.batch.service.repository.DealOrderRepository;
import com.howbuy.tms.high.batch.service.repository.HighAckProcessRepository;
import com.howbuy.tms.high.batch.service.repository.SubCustBooksRepository;
import com.howbuy.tms.high.batch.service.service.batch.ackprocess.AckProcessService;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @description:高端确认处理实现类
 * <AUTHOR>
 * @date 2017年7月7日 下午7:14:19
 * @since JDK 1.6
 */
@DubboService
@Service("highAckProcessFacadeService")
public class HighAckProcessFacadeService implements HighAckProcessFacade {

    private static Logger logger = LogManager.getLogger(HighAckProcessFacadeService.class);

    @Autowired
    private AckProcessService ackProcessService;

    @Autowired
    private WorkdayService workdayService;

    @Autowired
    private QueryTaInfoOuterService queryTaInfoOuterService;

    @Qualifier("cache.lockService")
    @Autowired
    private LockService lockService;

    @Autowired
    private DealOrderRepository dealOrderRepository;

    @Autowired
    private QueryCustInfoOuterService queryCustInfoOuterService;

    @Autowired
    private HighAckProcessRepository highAckProcessRepository;

    @Autowired
    private SubCustBooksRepository subCustBooksRepository;

    @Override
    public HighAckProcessResponse execute(HighAckProcessRequest request) {
        logger.info("开始执行确认处理");
        HighAckProcessResponse resp = new HighAckProcessResponse();
        resp.setReturnCode(ExceptionCodes.SUCCESS);
        resp.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        String tradeDt = workdayService.getSaleSysCurrWorkay();
        List<String> taCodeList = request.getTaCodeList();
        String sysCode = SysCodeEnum.BATCH_HIGH.getCode();
        if (CollectionUtils.isEmpty(taCodeList)) {
            throw new BatchException(ExceptionCodes.PARAM_IS_NULL, "未选择TA");
        }
        // 处理
        process(taCodeList, tradeDt, sysCode, resp);
        // 检查所有TA业务是否做完，更新主业务表
        checkAllEnd(tradeDt, sysCode);
        // 填充客户交易订单表deal_order中客户姓名CUST_NAME 20221128
        modifyCustName();
        logger.info("确认处理执行结束:{}", JSON.toJSONString(resp));
        return resp;
    }

    /**
     * 填充客户交易订单表deal_order中客户姓名CUST_NAME 20221128
     * 需求：【非交易过户转入订单，该客户未购买过高端产品，在生成非交易过户转入订单时，没有记客户姓名。因此需要填充客户姓名】
     */
    private void modifyCustName() {
        // 1、查询【客户交易订单表deal_order o】join【客户高端订单明细表high_deal_order_dtl d】where o.DEAL_NO=d.DEAL_NO and o.CUST_NAME is null and d.ACK_DT between 上一工作日 and 当前工作日。查询得到deal_order中DEAL_NO及TX_ACCT_NO
        // 2、拿TX_ACCT_NO调用客户中心接口，获取客户姓名【com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService#queryCustInfo 】
        // 3、将客户姓名，更新入deal_order中
        WorkdayPo workdayPo = workdayService.getWorkay(WorkdayTypeEnum.SYS_TYPE);
        String currentWorkDay = workdayPo.getWorkday();
        String lastWorkday = workdayPo.getLastWorkday();
        List<DealOrderPo> orderPoList = dealOrderRepository.selectByAckDtAndNullCustName(lastWorkday, currentWorkDay);
        if (CollectionUtil.isEmpty(orderPoList)) {
            logger.info("{}无待填充客户姓名数据", currentWorkDay);
            return;
        } else {
            logger.info("{}待填充客户姓名数据共{}条", currentWorkDay, orderPoList.size());
        }
        for (DealOrderPo po : orderPoList) {
            TradeParamLocalUtils.setDisCode(po.getDisCode());
            QueryCustInfoResult custInfo = queryCustInfoOuterService.queryCustInfo(po.getTxAcctNo());
            if (custInfo == null || custInfo.getCustName() == null) {
                logger.warn("dealNo:{}，txAcctNo:{}，原订单客户姓名为空，且客户中心未获取到客户信息", po.getDealNo(), po.getTxAcctNo());
                return;
            }
            logger.info("dealNo:{}，txAcctNo:{}，原订单客户姓名为空，查询客户中心所得姓名:{}",
                    po.getDealNo(), po.getTxAcctNo(), custInfo.getCustName());
            DealOrderPo updatePo = new DealOrderPo();
            updatePo.setCustName(custInfo.getCustName());
            updatePo.setDealNo(po.getDealNo());
            try {
                int updateResult = dealOrderRepository.updateByDealNoSelective(updatePo);
                if (updateResult != 1) {
                    logger.error("dealNo:{}，txAcctNo:{}，客户姓名为空，查询客户中心所得姓名:{}，更新客户姓名失败，updateResult:{}",
                            po.getDealNo(), po.getTxAcctNo(), custInfo.getCustName(), updateResult);
                }
            } catch (Exception e) {
                logger.error("dealNo:{}，txAcctNo:{}，客户姓名为空，查询客户中心所得姓名:{}，更新客户姓名失败，e:{}",
                        po.getDealNo(), po.getTxAcctNo(), custInfo.getCustName(), e);
            }
        }
    }

    /**
     *
     * process:多线程处理
     * @param taCodeList
     * @param tradeDt
     * @param sysCode
     * @param resp
     * <AUTHOR>
     * @date 2018年12月5日 下午4:06:50
     */
    private void process(List<String> taCodeList, String tradeDt, String sysCode, HighAckProcessResponse resp) {
        List<ThreadExceptionStatus> exList = new ArrayList<>();
        ThreadExceptionStatus exStatus = null;
        CountDownLatch latch = new CountDownLatch(taCodeList.size());
        for (String taCode : taCodeList) {
            boolean smTaFlag = isSMTa(taCode);
            exStatus = new ThreadExceptionStatus();
            exList.add(exStatus);
            exStatus.setTaCode(taCode);
            CommonThreadPool.execute(new AckProcessTask(taCode, tradeDt, sysCode, ackProcessService, exStatus, smTaFlag, latch, lockService, highAckProcessRepository));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("latch await error.", e);
            Thread.currentThread().interrupt();
        }
        // 异常处理
        processEx(exList, resp);
    }

    private boolean isSMTa(String taCode) {
        int num = subCustBooksRepository.countByTaCode(taCode);
        return num > 0;
    }

    private void processEx(List<ThreadExceptionStatus> resultList, HighAckProcessResponse resp) {
        BusinessException bizEx = null;
        StringBuffer errorCode = new StringBuffer();
        StringBuffer errorMsg = new StringBuffer();
        List<String> needWarnMsg = new LinkedList<>();
        for (ThreadExceptionStatus exStatus : resultList) {
            if (exStatus.isExsitException()) {
                if (exStatus.getException() instanceof BusinessException) {
                    bizEx = (BusinessException) exStatus.getException();
                    if (StringUtils.isEmpty(errorCode)) {
                        errorCode.append("TA:" + exStatus.getTaCode() + ",");
                        errorCode.append(bizEx.getErrorCode());
                    } else {
                        errorCode.append(",TA:" + exStatus.getTaCode() + ",");
                        errorCode.append(bizEx.getErrorCode());
                    }
                    errorMsg.append("TA:" + exStatus.getTaCode() + "," + bizEx.getErrorDesc() + "\n");
                    if (bizEx.isNeedWarn()) {
                        needWarnMsg.add("TA:" + exStatus.getTaCode() + "," + bizEx.getErrorDesc());
                    }
                } else {
                    if (StringUtils.isEmpty(errorCode)) {
                        errorCode.append("TA:" + exStatus.getTaCode() + ",");
                        errorCode.append(ExceptionCodes.HIGH_BATCH_CENTER_TRADE_ACK_PROCESS_FAIL);
                    } else {
                        errorCode.append(",TA:" + exStatus.getTaCode() + ",");
                        errorCode.append(ExceptionCodes.HIGH_BATCH_CENTER_TRADE_ACK_PROCESS_FAIL);
                    }
                    errorMsg.append("TA:" + exStatus.getTaCode() + ",批处理执行失败！确认处理发生异常\n");
                    needWarnMsg.add("TA:" + exStatus.getTaCode() + ",批处理执行失败！确认处理发生异常");
                }
            }
        }
        if (!StringUtils.isEmpty(errorCode)) {
            resp.setReturnCode(errorCode.toString());
            resp.setDescription(errorMsg.toString());
            resp.setNeedWarnMsgs(needWarnMsg);
        }
    }

    /**
     *
     * checkAllEnd:当所有TA处理完，更新主业务表
     * @param tradeDt
     * @param sysCode
     * <AUTHOR>
     * @date 2018年12月5日 下午4:00:18
     */
    private void checkAllEnd(String tradeDt, String sysCode) {
        ackProcessService.endAllOff(tradeDt, sysCode);
    }

}
