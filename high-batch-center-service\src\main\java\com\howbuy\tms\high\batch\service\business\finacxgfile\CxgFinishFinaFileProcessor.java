/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.finacxgfile;

import com.howbuy.tms.high.batch.dao.po.batch.FinaFileProcessRecPo;
import com.howbuy.tms.high.batch.facade.enums.FileOpStatusEnum;
import com.howbuy.tms.high.batch.service.business.finafilemonitor.AbstractFinaFileService;
import com.howbuy.tms.high.batch.service.business.message.FinaFileMessageBean;
import com.howbuy.tms.high.batch.service.common.utils.HowBuyRunTaskUil;
import com.howbuy.tms.high.batch.service.task.FinaCxgFinishCheckTask;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 *
 * @description:资金给中台储蓄罐OK文件
 *
 * @reason:
 * <AUTHOR>
 * @date 2018年5月30日 下午2:07:59
 * @since JDK 1.6
 */
@Service("finaCxgFinishFileProcessor")
public class CxgFinishFinaFileProcessor extends AbstractFinaFileService {
    private final Logger logger = LogManager.getLogger();

    @Autowired
    private FinaCxgFinishCheckProcessor finaCxgFinishCheckProcessor;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;

    @Override
    public boolean userThis(String code) {
        return "PIG_BUY_FINISH".equals(code);
    }

    @Override
    public void process(FinaFileMessageBean finaFileMessageBean) {
        logger.info("FinaCxgFinishFileProcessor|process start");
        FinaFileProcessRecPo finaFileProcessRecPo = new FinaFileProcessRecPo();
        try {
            // 消息数据落库
            preFileProcess(finaFileMessageBean, finaFileProcessRecPo);
        } catch (Exception e) {
            if (!StringUtils.isEmpty(finaFileProcessRecPo.getRecordNo())) {
                finaFileProcessRecPo.setFileOpStatus(FileOpStatusEnum.IMPORT_FAILED.getKey());
            }
            logger.error("FinaCxgFinishFileProcessor|process|msg:{}", e.getMessage(), e);
        } finally {
            if (!StringUtils.isEmpty(finaFileProcessRecPo.getRecordNo())) {
                if (!FileOpStatusEnum.IMPORT_FAILED.getKey().equals(finaFileProcessRecPo.getFileOpStatus())) {
                    finaFileProcessRecPo.setFileOpStatus(FileOpStatusEnum.IMPORT_SUCCESSFUL.getKey());
                }
                // 更新文件处理状态
                afterFileProcess(finaFileProcessRecPo);
            }
            logger.info("FinaCxgFinishFileProcessor|process end,recordNo:{} processStatus:{}", finaFileProcessRecPo.getRecordNo(), finaFileProcessRecPo.getProcessStatus());
        }

        // 数据处理
        dataExecute(finaFileProcessRecPo.getRecordNo(), finaFileMessageBean);
    }

    /**
     *
     * dataExecute:储蓄罐结果文件检查
     * @param batchNo
     * <AUTHOR>
     * @date 2019年7月9日 下午4:13:50
     */
    private void dataExecute(final String batchNo, final FinaFileMessageBean finaFileMessageBean) {
        howBuyRunTaskUil.runTask(new FinaCxgFinishCheckTask(batchNo, finaFileMessageBean, finaCxgFinishCheckProcessor));
    }

    /**
     *
     * buildFinaDirectionFileRecPo:回款方向
     *
     * @param recDtl
     * @param ackDt
     * @param date
     * @return
     * <AUTHOR>
     * @date 2018年6月15日 下午1:44:52
     */
    @Override
    public Object build(String[] recDtl, String ackDt, Date date, String dataSourceType, String batchNo) {
        return null;
    }

    @Override
    public void insert(List<?> list) {
    }

    @Override
    public void validate(String[] recDtl) {
    }
}
