package com.howbuy.tms.high.batch.service.logic.bean;

import com.howbuy.tms.high.batch.facade.common.BaseDto;

/**
 * @Description:上报前合规校验
 * @Author: yun.lu
 * Date: 2025/6/5 13:25
 */
public class ComplianceSubmitCheckResultDto extends BaseDto {
    /**
     * 资产证明校验,ture  通过,false 未通过
     */
    private boolean assetPass;

    /**
     * 冷静期校验,ture  通过,false 未通过
     */
    private boolean calmDtmPass;


    /**
     * 双录校验,ture  通过,false 未通过
     */
    private boolean dualentryPass;


    /**
     * 回访校验,ture  通过,false 未通过
     */
    private boolean callbackPass;

    /**
     * 配置缺失,ture  缺失,false 未缺失
     */
    private boolean cfgMiss;

    public boolean isCfgMiss() {
        return cfgMiss;
    }

    public void setCfgMiss(boolean cfgMiss) {
        this.cfgMiss = cfgMiss;
    }

    /**
     * 是否都校验通过,ture  通过,false 未通过
     */
    public boolean checkAllPass() {
        return !cfgMiss && assetPass && calmDtmPass && dualentryPass && callbackPass;
    }

    public boolean isAssetPass() {
        return assetPass;
    }

    public void setAssetPass(boolean assetPass) {
        this.assetPass = assetPass;
    }

    public boolean isCalmDtmPass() {
        return calmDtmPass;
    }

    public void setCalmDtmPass(boolean calmDtmPass) {
        this.calmDtmPass = calmDtmPass;
    }

    public boolean isDualentryPass() {
        return dualentryPass;
    }

    public void setDualentryPass(boolean dualentryPass) {
        this.dualentryPass = dualentryPass;
    }

    public boolean isCallbackPass() {
        return callbackPass;
    }

    public void setCallbackPass(boolean callbackPass) {
        this.callbackPass = callbackPass;
    }
}
