<?xml version="1.0" encoding="UTF-8"?>
<!-- - Copyright 1999-2011 Alibaba Group. - - Licensed under the Apache License,
	Version 2.0 (the "License"); - you may not use this file except in compliance
	with the License. - You may obtain a copy of the License at - - http://www.apache.org/licenses/LICENSE-2.0
	- - Unless required by applicable law or agreed to in writing, software -
	distributed under the License is distributed on an "AS IS" BASIS, - WITHOUT
	WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. - See the
	License for the specific language governing permissions and - limitations
	under the License. -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!-- 声明需要暴露的服务接口 -->
    <!-- 签订电子合同 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.signecontract.SignEcontractFacade"
                   ref="signEcontractFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端批处理系统工作日查询 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryworkday.QueryHighWorkdayFacade"
                   ref="queryHighWorkdayFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端批处理工作流节点查询 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querybatchflowinfo.QueryHighBatchFlowInfoFacade"
                   ref="queryHighBatchFlowInfoFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端批处理TA工作流节点查询 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.querytabusinessbatchflow.QueryHighTaBatchFlowInfoFacade"
            ref="queryHighTaBatchFlowInfoFacade" protocol="dubbo" registry="high-batch-center"/>
    <!--高端批处理查询支付对账查询  -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.pmtcheck.QueryHighPmtCheckFacade"
                   ref="queryHighPmtCheckFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端查询交易申请对账 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querydealapplycheck.QueryHighDealApplyCheckFacade"
                   ref="queryHighDealApplyCheckFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端查询交易申请 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryfundcheckorder.QuerySimuFundCheckOrderFacade"
                   ref="querySimuFundCheckOrderFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端查询支付对账异常支付订单查-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.querypmtcheckexception.QueryHighPmtCheckExceptionFacade"
            ref="queryHighPmtCheckExceptionFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端查询支付对账确认-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.querypmtcheckconfirm.QueryHighPmtCheckConfirmFacade"
            ref="queryHighPmtCheckConfirmFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端查询支付对账确认结果-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.querypmtcheckconfirmflag.QueryHighPmtCheckConfirmFlagFacade"
            ref="queryHighPmtCheckConfirmFlagFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端查询高端交易订单明细 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.QueryHighFundDealOrderDtlFacade"
            ref="queryHighFundDealOrderDtlFacade" protocol="dubbo" registry="high-batch-center" timeout="360000"/>
    <!-- 高端查询高端交易订单明细  换卡 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtlchangecard.QueryHighFundDealOrderDtlChangeCardFacade"
            ref="queryHighFundDealOrderDtlChangeCardFacade" protocol="dubbo" registry="high-batch-center"
            timeout="360000"/>
    <!-- 查询限额类型描述信息接口 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querylimittypedes.QueryLimitTypeDesFacade"
                   ref="queryLimitTypeDesFacade" protocol="dubbo" registry="high-batch-center"/>
    <!--查询客户持仓-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryacctbalance.QueryAccountBalanceFacade"
                   ref="queryAccountBalanceFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 份额确认书查询 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryvolconfirmbook.QueryHighVolConfirmBookFacade"
                   ref="queryHighVolConfirmBookFacade" protocol="dubbo" registry="high-batch-center" timeout="360000"/>
    <!-- 资金到账证明文件查询 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryhighfundarrivalproof.QueryHighFundArrivalProofFacade"
            ref="queryHighFundArrivalProofFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 修改资金到账证明文件 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.trade.highfundarrivalproof.ModifyHighFundArrivalProofFacade"
            ref="modifyHighFundArrivalProofFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 生成资金到账证明文件 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.trade.highfundarrivalproof.CreateHighFundArrivalProofFacade"
            ref="createHighFundArrivalProofFacade" protocol="dubbo" registry="high-batch-center" timeout="360000"/>

    <!-- 高端日初始化 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.dayinit.HighDayInitFacade"
                   ref="highDayInitFacadeService" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端导入支付对账文件 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.importpaychkfile.HighImportPayChkFileFacade"
                   ref="highImportPayChkFileFacadeService" protocol="dubbo" registry="high-batch-center"
                   timeout="120000"/>
    <!-- 高端支付对账确认 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.paychkack.HighPayChkAckFacade"
                   ref="highPayChkAckFacadeService" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端交易申请日终 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.tradeappdayend.HighTradeAppDayendFacade"
                   ref="highTradeAppDayEndFacadeService" protocol="dubbo" registry="high-batch-center"
                   timeout="300000"/>
    <!-- 高端导入确认 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.importack.HighImportAckFileFacade"
                   ref="highImportAckFileFacadeService" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端确认处理 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.ackprocess.HighAckProcessFacade"
                   ref="highAckProcessFacadeService" protocol="dubbo" registry="high-batch-center" timeout="600000"/>
    <!-- 高端确认日终处理 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.ackdayend.HighAckDayEndProcessFacade"
                   ref="highAckDayEndProcessFacadeService" protocol="dubbo" registry="high-batch-center"
                   timeout="600000"/>
    <!-- 高端日终 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.dayend.HighDayEndProcessFacade"
                   ref="highDayEndProcessFacadeService" protocol="dubbo" registry="high-batch-center" timeout="300000"/>

    <!-- 高端交易对账 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.tradecheck.HighTradeCheckFacade"
                   ref="highTradeCheckFacadeService" protocol="dubbo" registry="high-batch-center"/>

    <!--查询电子签名 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryesignature.QueryEsignatureFacade"
                   ref="queryEsignatureFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 修改电子签名 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.modifyesignature.ModifyEsignatureFacade"
                   ref="modifyEsignatureFacade" protocol="dubbo" registry="high-batch-center"/>

    <!-- 柜台收市 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.counterend.CounterEndFacade" ref="counterEndFacade"
                   protocol="dubbo" registry="high-batch-center"/>

    <!-- 柜台购买 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.counterpurchase.CounterPurchaseFacade"
                   ref="counterPurchaseFacadeService" protocol="dubbo" registry="high-batch-center"/>
    <!-- 柜台赎回 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.counterredeem.CounterRedeemFacade"
                   ref="counterRedeemFacadeService" protocol="dubbo" registry="high-batch-center"/>
    <!-- 柜台撤单 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.countercancel.CounterCancelFacade"
                   ref="counterCancelFacadeService" protocol="dubbo" registry="high-batch-center"/>
    <!-- 柜台修改分红方式 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.countermodifydiv.CounterModifyDivFacade"
                   ref="counterModifyDivFacadeService" protocol="dubbo" registry="high-batch-center"/>
    <!-- 柜台强制测单 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.counterforcecancel.CounterForceCancelFacade"
                   ref="counterForceCancelFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 查询柜台订单 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querycounterorder.QueryCounterOrderFacade"
                   ref="queryCounterOrderFacadeService" protocol="dubbo" registry="high-batch-center"/>
    <!-- 柜台审核 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.countercheck.CounterCheckFacade"
                   ref="counterCheckFacadeService" protocol="dubbo" registry="high-batch-center"/>
    <!-- 柜台报表查询 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querycountertrade.QueryCounterTradeFacade"
                   ref="queryCounterTradeFacadeService" protocol="dubbo" registry="high-batch-center"/>
    <!-- 交易对账异常处理 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.tradecheckexcpproc.TradeCheckExcpProcFacade"
                   ref="tradeCheckExcpProcFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 重置上报标识 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.resetsubmitflag.ResetSubmitFlagFacade"
                   ref="resetSubmitFlagFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 份额确认书手工生成 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.volconfirmbook.HighVolConfirmBookFacade"
                   ref="highVolConfirmBookFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 日终检查 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.dayendcheck.QueryDayendCheckFacade"
                   ref="queryDayendCheckFacade" protocol="dubbo" registry="high-batch-center"/>

    <!-- 支付对账 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.paymentcheck.PaymentCheckFacade"
                   ref="paymentCheckFacadeService" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端交易异常 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryhightradeexcp.QueryHighTradeExcpFacade"
                   ref="queryHighTradeExcpFacadeService" protocol="dubbo" registry="high-batch-center"/>

    <!--  查询未干预的订单列表 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querycomplinfo.QueryComplInfoListFacade"
                   ref="queryComplInfoListService" protocol="dubbo" registry="high-batch-center"/>
    <!-- 产品销售报表查询  -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryproductsale.QueryProductSaleFacade"
                   ref="queryProductSaleFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 更新合规信息 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.updatecompl.UpdateComplInfoFacade"
                   ref="updateComplInfoFacadeService" protocol="dubbo" registry="high-batch-center"/>
    <!-- 交易申请报表  -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querytradeappsummary.QueryTradeAppSummaryFacade"
                   ref="queryTradeAppSummaryFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 预约报表  -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryappointmentreport.QueryAppointmentReportFacade"
            ref="queryAppointmentReportFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 查询可以实时退款的列表 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryhighrefund.QueryHighRefundFacade"
                   ref="queryHighRefundFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 发起实时退款请求  -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.realtimerefund.RealTimeRefundFacade"
                   ref="realTimeRefundFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 查询退款报表 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryhighrefundreport.QueryHighRefundReportFacade"
                   ref="queryHighRefundReportFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 部分赎回报表 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryhighpartredeemreport.QueryHighPartRedeemFacade"
            ref="queryHighPartRedeemFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 巨额赎回报表 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryhighlargeredeemreport.QueryHighLargeRedeemFacade"
            ref="queryHighLargeRedeemFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 客服回访报表 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryreturnvisitreport.QueryHighReturnVisitFacade"
                   ref="queryHighReturnVisitFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 交易合规上报检查报表4合1 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querytradereportcheck.QueryTradeReportCheckFacade"
                   ref="queryTradeReportCheckFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端交易申请日终 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.QueryHighDealAppDayendCheckFacade"
            ref="queryHighDealAppDayendCheckFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 无效资产证明交易  -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.asset.QueryInvalidAssetFacade"
            ref="queryInvalidAssetFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 未过冷静期交易  -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.calm.QueryNotPassCalmFacade"
                   ref="queryNotPassCalmFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 交易异常订单 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.dealex.QueryDealExFacade"
                   ref="queryDealExFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 交易未对账订单 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.dealnotcheck.QueryDealNotCheckFacade"
            ref="queryDealNotCheckFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 双录回访 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.dualentryandcallback.QueryDualentryAndCallbackFacade"
            ref="queryDualentryAndCallbackFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 无需通知交易 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.notneednotify.QueryNotNeedNotifyFacade"
            ref="queryNotNeedNotifyFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 支付异常订单 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.pmtdiff.QueryPmtDiffFacade"
                   ref="queryPmtDiffFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 支付未对账订单 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.pmtnotcheck.QueryPmtNotCheckFacade"
            ref="queryPmtNotCheckFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 上报中或重新上报订单 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.submitingorresubmit.QuerySubmitingOrResubmitFacade"
            ref="querySubmitingOrResubmitFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 未通知or重新通知订单 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.unnotifyorrenotify.QueryUnnotifyOrRenotifyFacade"
            ref="queryUnnotifyOrRenotifyFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 机构未申请日终的TA -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.instnotappdayendta.QueryInstNotAppDayEndTaFacade"
            ref="queryInstNotAppDayEndTaFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 储蓄罐支付撤单异常 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.cxgpaycancel.QueryCxgPayCancelExFacade"
            ref="queryCxgPayCancelExFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 储蓄罐存入对账异常 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.cxgpaysave.QueryCxgPaySaveExFacade"
            ref="queryCxgPaySaveExFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 预约报表详情 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryappointmentreport.QueryAppointmentDetailFacade"
            ref="queryAppointmentDetailFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 查询干预列表 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.querydealdtlinterpose.QueryHighDealDtlInterposeFacade"
            ref="queryHighDealDtlInterposeFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 人工干预 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.highdealdtlinterpose.HighDealDtlInterposeFacade"
                   ref="highDealDtlInterposeFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 批处理状态查询 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querybatchflowstat.QueryBatchFlowStatFacade"
                   ref="queryBatchFlowStatFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 到期赎回报表 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryexpireredeemreport.QueryHighExpireRedeemReportFacade"
            ref="queryHighExpireRedeemReportFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 到期赎回干预 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.trade.highexpiredateinterpose.HighExpireDateInterposeFacade"
            ref="highExpireDateInterposeFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 人工上报查询 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryhighdealsubmit.QueryHighDealSubmitFacade"
                   ref="queryHighDealSubmitFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 人工上报详情 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryhighdealsubmitdtl.QueryHighDealSubmitDtlFacade"
            ref="queryHighDealSubmitDtlFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 查询拆单明细 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryhighsplitdtl.QueryHighSplitDtlFacade"
                   ref="queryHighSplitDtlFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 人工上报 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.highdealsubmit.HighDealSubmitFacade"
                   ref="highDealSubmitFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 查询子账本 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querysubcustbooks.QuerySubCustBooksFacade"
                   ref="querySubCustBooksFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 查询电子签名 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querycustesignature.QueryCustEsignatureFacade"
                   ref="queryCustEsignatureFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 查询电子合同 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querycustecontract.QueryHighCustEcontractFacade"
                   ref="queryHighCustEcontractFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 高端查询交易确认 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryfundackfilerec.QueryHighFundAckFileRecFacade"
                   ref="queryHighFundAckFileRecFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- TA列表查询 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querytabusinesslist.QueryHighTaBusinessListFacade"
                   ref="queryHighTaBusinessListFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- TA汇总统计 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.querytabusinessbatchcount.QueryTaBusinessBatchCountFacade"
            ref="queryTaBusinessBatchCountFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- TA不收市 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querytacounternotend.QueryTaCounterNotEndFacade"
                   ref="queryTaCounterNotEndFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- TA不收市维护 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.tacounternotend.CounterSaveOrDelNotEndTaFacade"
                   ref="counterSaveOrDelNotEndTaFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 人工上报 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.trade.tabusinessbatchflow.ResetTaBusinessBatchFlowStatFacade"
            ref="resetTaBusinessBatchFlowStatFacade" protocol="dubbo" registry="high-batch-center"/>
    <!--查询滚动赎回参数-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryexpiredredeemparams.QueryExpiredRedeemParamsFacade"
            ref="queryExpiredRedeemParamsFacade" protocol="dubbo" registry="high-batch-center"/>
    <!--查询滚动赎回参数-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryfixedredeemreport.QueryFixedRedeemReportFacade"
            ref="queryFixedRedeemReportFacade" protocol="dubbo" registry="high-batch-center"/>

    <!-- 查询实时退款报表 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryhighrealrefundreport.QueryHighRealRefundReportFacade"
            ref="queryHighRealRefundReportFacade" protocol="dubbo" registry="high-batch-center"/>
    <!--查询未完成复购协议-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.querycounterendcheck.QueryUnFinishRepurchaseProtocolFacade"
            ref="queryUnFinishRepurchaseProtocolFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--修改复购协议-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.trade.countermodifyrepurchaseproctol.CounterModifyRepurchaseProctolFacade"
            ref="counterModifyRepurchaseProctolFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--查询购买状态-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querybuystatus.QueryBuyStatusFacade"
                   ref="queryBuyStatusFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--查询赎回状态-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryredeemstatus.QueryRedeemStatusFacade"
                   ref="queryRedeemStatusFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--查询指定上报日客户持仓-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querycustbaldtl.QueryCustBalDtlWithSubmitDtFacade"
                   ref="queryCustBalDtlWithSubmitDtFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--修改份额并修改上报状态-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.trade.tradecheckexcpprocmodiyVol.TradeCheckExcpProcModiyVolFacade"
            ref="tradeCheckExcpProcModiyVolFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--查询CRM订单-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querydealforcrm.QueryDealForCrmFacade"
                   ref="queryDealForCrmFacadeService" protocol="dubbo" registry="high-batch-center"/>


    <!--查询投资经历-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querycustinvestdeal.QueryCustInvestDealFacade"
                   ref="queryCustInvestDealFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--生成投资经历-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.custinvesthisfile.GenerateCustInvestHisFileFacade"
                   ref="generateCustInvestHisFileFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--修改份额-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.modifyvol.ModifyVolFacade" ref="modifyVolFacade"
                   protocol="dubbo" registry="high-batch-center"/>

    <!--查询修改份额-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querymodifyvol.QueryModifyVolFacade"
                   ref="queryModifyVolFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--交易申请日终检查，查询预约日历开放日变化，订单上报日未变化-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.submitdt.QuerySubmitDtUnChangeFacade"
            ref="querySubmitDtUnChangeFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--交易申请日终检查，支付日未发起储蓄罐支付的交易-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.piggyunpay.QueryPiggyUnPayFacade"
            ref="queryPiggyUnPayFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--交易申请日终检查，日历变化，打款日未同步变化交易-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.highdealappdayendcheck.pmtdtunchange.QueryPmtDtUnChangeFacade"
            ref="queryPmtDtUnChangeFacade" protocol="dubbo" registry="high-batch-center"/>

    <!-- 查询管理人邮件发送明细 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryhighmangeremailsenddtl.QueryHighMangerEmailSendDtlFacade"
            ref="queryHighMangerEmailSendDtlFacade" protocol="dubbo" registry="high-batch-center"/>
    <!-- 重发管理人邮件 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.resendhighmangeremail.ResendHighMangerEmailFacade"
                   ref="resendHighMangerEmailFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--干预预约支付日-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.trade.highdealdtlinterposepmtdt.HighDealDtlInterposePmtDtFacade"
            ref="highDealDtlInterposePmtDtFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--柜台非交易过户下单-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.trade.counternotradeoveraccount.CounterNoTradeOverAccountFacade"
            ref="counterNoTradeOverAccountFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--上报认缴金额报表-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.submitsubsamt.SubmitPeSubsAmtProcessFacade"
                   ref="submitPeSubsAmtProcessFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--查询分次CALL款股权产品认缴金额报表-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querypesubsamtreport.QueryPeSubsAmtFacade"
                   ref="queryPeSubsAmtFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--查询查询线上补签协议客户列表-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querySupSignCust.QuerySupSignCustListFacade"
                   ref="querySupSignCustListFacade" protocol="dubbo" registry="high-batch-center"/>
    <!--查询赎回拆单失败数据-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryredeemspliterror.QueryRedeemSplitErrorFacade"
                   ref="queryRedeemSplitErrorFacade" protocol="dubbo" registry="high-batch-center"/>
    <!--重新拆分赎回订单-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.resplitredeemorders.ResplitRedeemOrdersFacade"
                   ref="resplitRedeemOrdersFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--查询补签协议签署状态-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryagreementsupsignstatus.QueryAgreementSupSignStatusFacade"
            ref="queryAgreementSupSignStatusFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--添加线下补签记录-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.addsupsignoffline.AddSupSignOfflineFacade"
                   ref="addSupSignOfflineFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--全量对账-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryallhighorderdtlpage.QueryAllHighOrderDtlPageFacade"
            ref="queryAllHighOrderDtlPageFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--单条对账-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryhighorderdtl.QueryHighOrderDtlFacade"
                   ref="queryHighOrderDtlFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--机构订单重新上报-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.resubmitinstorder.ResubmitHighInstOrderFacade"
                   ref="resubmitHighInstOrderFacade" protocol="dubbo" registry="high-batch-center"/>
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.trade.modifyrefunddirection.CounterModifyRefundDirectionFacade"
            ref="counterModifyRefundDirectionFacade" protocol="dubbo" registry="high-batch-center"/>

    <!-- 高端查询高端定投计划 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryhighfundinvplan.QueryHighFundInvPlanFacade"
                   ref="queryHighFundInvPlanFacade" protocol="dubbo" registry="high-batch-center" timeout="360000"/>

    <!-- 高端查询高端定投计划明细 -->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryhighfundinvplandtl.QueryHighFundInvPlanDtlFacade"
            ref="queryHighFundInvPlanDtlFacade" protocol="dubbo" registry="high-batch-center" timeout="360000"/>

    <!-- 高端查询高端定投计划明细 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.counterendcheck.CounterEndCheckFacade"
                   ref="counterEndCheckFacade" protocol="dubbo" registry="high-batch-center"/>

    <!-- 刷新直销黑名单缓存 -->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.refreshcmblackcache.RefreshCmBlackCacheFacade"
                   ref="refreshCmBlackCacheFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--更新合伙人人数-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.partnersnumberupdate.PartnersNumberUpdateFacade"
                   ref="partnersNumberUpdateFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--检查是否存在电子合同-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.signecontract.CheckSpecialProductEcontractFacade"
                   ref="checkSpecialProductEcontractFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--查询股权份额转让订单-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryownershiprighttransfer.QueryOwnershipRightTransferFacade"
            ref="queryOwnershipRightTransferFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--查询股权份额转让订单详情-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.QueryOwnershipRightTransferDtlFacade"
            ref="queryOwnershipRightTransferDtlFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--股权份额转让订单修改申请-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.trade.counterownershiprighttransferfacade.CounterOwnershipRightTransferFacade"
            ref="counterOwnershipRightTransferFacade" protocol="dubbo" registry="high-batch-center"/>

    <!--修改股权份额转让信息接口-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.trade.modifyownershiprighttransferface.ModifyOwnershipRightTransferFace"
            ref="modifyOwnershipRightTransferFace" protocol="dubbo" registry="high-batch-center"/>

    <!--修改非交易认缴信息接口-->
    <dubbo:service
            interface="com.howbuy.tms.high.batch.facade.trade.noTradeUpdateSubscribeAmtInfo.NoTradeUpdateSubscribeAmtInfoFacade"
            ref="noTradeUpdateSubscribeAmtInfoFacade" protocol="dubbo" registry="high-batch-center"/>
    <!--好臻金额锁定配置新增-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.hzfundamtlockcfg.HzFundAmtLockConfAddFacade"
                   ref="hzFundAmtLockConfAddFacade" protocol="dubbo" registry="high-batch-center"/>

	<!--查询订单信息接口-->
	<dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryhighdealorder.QueryHighDealOrderFacade" ref="queryHighDealOrderFacade" protocol="dubbo" registry="high-batch-center"/>


    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.customerSubsAmtApply.CustomerSubsAmtUpdateApplyFacade"
                   ref="customerSubsAmtUpdateApplyFacade" protocol="dubbo" registry="high-batch-center"/>

    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querySubsAmtChangeDetail.QuerySubsAmtChangeDetailFacade"
                   ref="querySubsAmtChangeDetailFacade" protocol="dubbo" registry="high-batch-center"/>

    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.subsAmtChangeCheck.SubsAmtChangeCheckFacade"
                   ref="subsAmtChangeCheckFacade" protocol="dubbo" registry="high-batch-center"/>

    <dubbo:service interface="com.howbuy.tms.high.batch.facade.trade.subsAmtApplyUpdate.SubsAmtApplyUpdateFacade"
                   ref="subsAmtApplyUpdateFacade" protocol="dubbo" registry="high-batch-center"/>

    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querydealinfo.QueryHighMiddleDealInfoFacade"
                   ref="queryHighMiddleDealInfoService" protocol="dubbo" registry="high-batch-center"/>

    <!--查询高端TA收市状态接口-->
    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusFacade"
                   ref="queryHighTaDayCloseStatusFacade" protocol="dubbo" registry="high-batch-center"/>


    <dubbo:service interface="com.howbuy.tms.high.batch.facade.query.queryredeemfeenotice.QueryRedeemFeeNoticeFacade"
                   ref="queryRedeemFeeNoticeFacade" protocol="dubbo" registry="high-batch-center"/>
</beans>