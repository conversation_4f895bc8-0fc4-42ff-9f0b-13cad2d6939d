<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.tms.high.batch.dao.mapper.customize.order.HighDealOrderDtlPoMapper" >
  <resultMap id="BaseResultMap" type="com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo" extends="com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper.BaseResultMap">
  </resultMap>

  <!-- 自定义sql -->

  <!-- 根据主订单号查询交易高端订单明细列表信息 -->
	<select id="selectByDealNo" parameterType="map" resultMap="BaseResultMap">
		select
		<include refid="com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
		from high_deal_order_dtl
		where deal_no = #{dealNo,jdbcType=VARCHAR}
	</select>

	<resultMap id="DealOrdersToNofity"
		type="com.howbuy.tms.high.batch.dao.po.batch.SimuFundCheckOrderDto"
		extends="com.howbuy.tms.high.batch.dao.mapper.customize.order.SimuFundCheckOrderPoMapper.BaseResultMap">
		<result column="NOTIFY_SUBMIT_FLAG" jdbcType="CHAR" property="notifySubmitFlag" />
		<result column="PROTOCOL_TYPE" jdbcType="CHAR" property="protocolType" />
		<result column="PRODUCT_CHANNEL" jdbcType="VARCHAR" property="productChannel" />
		<result column="PMT_CHECK_DT" jdbcType="VARCHAR" property="pmtCheckDt" />
		<result column="MERGE_SUBMIT_FLAG" jdbcType="VARCHAR" property="mergeSubmitFlag" />
		<result column="MAIN_DEAL_ORDER_NO" jdbcType="VARCHAR" property="mainDealOrderNo" />
	</resultMap>
    <sql id="DealOrdersToNofity_Column_List" >
               DTL.DEAL_NO,
		       DTL.DEAL_DTL_NO,
		       DTL.FUND_CODE,
		       DTL.FUND_NAME,
		       DTL.FUND_TYPE,
		       DTL.FUND_SUB_TYPE,
		       DTL.FUND_SHARE_CLASS,
		       DTL.APP_AMT,
		       DTL.APP_VOL,
		       DTL.NAV,
		       DTL.REDEEM_DIRECTION,
		       DTL.DISCOUNT_RATE,
		       DTL.TX_APP_FLAG,
		       DTL.RISK_FLAG,
		       DTL.LARGE_REDM_FLAG,
		       DTL.FUND_DIV_MODE,
		       DTL.M_BUSI_CODE,
		       DTL.ACK_AMT,
		       DTL.ACK_VOL,
		       DTL.ACK_DT,
		       DTL.CANCEL_ORDER_SRC,
		       DTL.MEMO,
		       DTL.NOTIFY_SUBMIT_FLAG,
		       DTL.TA_TRADE_DT,
		       DTL.UPDATE_DTM,
		       DTL.TA_CODE,
		       DTL.INTEREST,
		       DTL.ACHIEVEMENT_PAY,
		       DTL.ACHIEVEMENT_COMPEN,
		       DTL.VOL_BY_INTEREST,
		       DTL.CUST_RISK_LEVEL,
		       DTL.FUND_RISK_LEVEL,
		       DTL.FEE,
		       ODR.APP_DATE,
		       ODR.APP_TIME,
		       ODR.APP_DTM,
		       ODR.TX_ACCT_NO,
		       ODR.DIS_CODE,
		       ODR.DIS_TX_ACCT_NO,
		       ODR.TX_CHANNEL,
		       ODR.OUTLET_CODE,
		       ODR.SUB_TX_ACCT_NO,
		       ODR.INVST_TYPE,
		       ODR.CUST_NAME,
		       ODR.ID_TYPE,
		       ODR.ID_NO,
		       ODR.APP_RATIO,
		       PO.PAYMENT_TYPE,
		       PO.SUB_BANK_NAME,
		       PO.PMT_CHECK_DT,
		       DTL.PRODUCT_CHANNEL,
		       ODR.DEAL_TYPE,
		       EX.TRANSACTOR_ID_NO,
		       EX.TRANSACTOR_ID_TYPE,
		       EX.TRANSACTOR_NAME,
		       DTL.UNUSUAL_TRANS_TYPE,
		       DTL.SUBMIT_TA_DT,
		       CHK.SUBMIT_APP_FLAG,
		       CASE WHEN DTL.M_BUSI_CODE IN ('1365', '1366') THEN DTL.CP_ACCT_NO ELSE ODR.CP_ACCT_NO END AS CP_ACCT_NO,
			   CASE WHEN DTL.M_BUSI_CODE IN ('1365', '1366') THEN DTL.PROTOCOL_TYPE ELSE ODR.PROTOCOL_TYPE END AS PROTOCOL_TYPE,
			   CASE WHEN DTL.M_BUSI_CODE IN ('1365', '1366') THEN DTL.PROTOCOL_NO ELSE ODR.PROTOCOL_NO END AS PROTOCOL_NO,
		       CASE WHEN DTL.M_BUSI_CODE IN ('1365', '1366') THEN DTL.BANK_ACCT ELSE ODR.BANK_ACCT END AS BANK_ACCT,
		       CASE WHEN DTL.M_BUSI_CODE IN ('1365', '1366') THEN DTL.BANK_CODE ELSE ODR.BANK_CODE END AS BANK_CODE,
		       DTL.MERGE_SUBMIT_FLAG,
		       DTL.MAIN_DEAL_ORDER_NO
    </sql>
	<!--  查询未通知/需重新通知的订单明细列表 -->
  	<select id="selectDealOrdersToNotify" resultMap="DealOrdersToNofity">
		SELECT <include refid="DealOrdersToNofity_Column_List"/>
		  FROM HIGH_DEAL_ORDER_DTL DTL
		  INNER JOIN (SELECT * FROM DEAL_ORDER_EXTEND WHERE REC_STAT = '0') EX
		    ON DTL.DEAL_NO = EX.DEAL_NO
		  INNER JOIN DEAL_ORDER ODR
		    ON DTL.DEAL_NO = ODR.DEAL_NO
		  LEFT JOIN PAYMENT_ORDER PO
		    ON DTL.DEAL_NO = PO.DEAL_NO
		  LEFT JOIN SIMU_FUND_CHECK_ORDER CHK
        	ON DTL.DEAL_NO = CHK.DEAL_NO
          WHERE DTL.PRODUCT_CHANNEL IN ('6', '7')
            AND DTL.SUBMIT_TA_DT &lt;= #{tradeDt,jdbcType=VARCHAR}
            AND DTL.NOTIFY_SUBMIT_FLAG IN ('1', '3')
            AND (DTL.MERGE_SUBMIT_FLAG IS NULL OR DTL.MERGE_SUBMIT_FLAG != '1' OR DTL.DEAL_NO = DTL.MAIN_DEAL_ORDER_NO)
		 ORDER BY DTL.CREATE_DTM
	</select>

	<resultMap id="waitSubmitDealMap"
			   type="com.howbuy.tms.high.batch.dao.vo.WaitSubmitOrderDto">
		<result column="DEAL_DTL_NO" jdbcType="CHAR" property="dealDtlNo" />
		<result column="DEAL_NO" jdbcType="CHAR" property="dealNo" />
		<result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
		<result column="FUND_NAME" jdbcType="VARCHAR" property="fundName" />
		<result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo" />
		<result column="APP_DTM" jdbcType="TIMESTAMP" property="appDate" />
		<result column="DIS_CODE" jdbcType="VARCHAR" property="disCode" />
		<result column="FUND_SHARE_CLASS" jdbcType="VARCHAR" property="fundShareClass" />
		<result column="M_BUSI_CODE" jdbcType="VARCHAR" property="mBusiCode" />
		<result column="CUST_NAME" jdbcType="VARCHAR" property="custName" />
	</resultMap>
	<select id="queryWaitSubmitDealOrderDtl" resultMap="waitSubmitDealMap">
		SELECT dtl.DEAL_DTL_NO,
			   dtl.DEAL_NO,
			   dtl.FUND_CODE,
			   dtl.FUND_NAME,
			   dtl.TX_ACCT_NO,
			   deal.APP_DTM,
			   deal.DIS_CODE,
			   dtl.FUND_SHARE_CLASS,
			   dtl.M_BUSI_CODE,
			   deal.CUST_NAME
		FROM HIGH_DEAL_ORDER_DTL dtl
				 inner join DEAL_ORDER deal on dtl.DEAL_NO = deal.DEAL_NO
		where dtl.TX_APP_FLAG = '0' AND deal.ORDER_STATUS='1'  AND deal.APP_DTM> #{endDate, jdbcType=TIMESTAMP}
		  AND dtl.M_BUSI_CODE in ('1120', '1122', '1124')
		  AND (dtl.TX_ACK_FLAG  ='1' or dtl.TX_ACK_FLAG is null )
	</select>

	<!--  查询未通知/需重新通知的子订单明细列表 -->
  	<select id="selectMergeSubmitOrdersToNotify" resultMap="DealOrdersToNofity">
		SELECT <include refid="DealOrdersToNofity_Column_List"/>
		  FROM HIGH_DEAL_ORDER_DTL DTL
		  INNER JOIN (SELECT * FROM DEAL_ORDER_EXTEND WHERE REC_STAT = '0') EX
		    ON DTL.DEAL_NO = EX.DEAL_NO
		  INNER JOIN DEAL_ORDER ODR
		    ON DTL.DEAL_NO = ODR.DEAL_NO
		  LEFT JOIN PAYMENT_ORDER PO
		    ON DTL.DEAL_NO = PO.DEAL_NO
		  LEFT JOIN SIMU_FUND_CHECK_ORDER CHK
        	ON DTL.DEAL_NO = CHK.DEAL_NO
          WHERE DTL.PRODUCT_CHANNEL IN ('6', '7')
            AND DTL.NOTIFY_SUBMIT_FLAG IN ('1', '3')
            AND DTL.MAIN_DEAL_ORDER_NO = #{dealNo,jdbcType=VARCHAR}
	</select>

    <!-- 更新通知状态 -->
	<update id="updateNotifySubmitFlagByDealDtlNo" parameterType="com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo">
		update HIGH_DEAL_ORDER_DTL
		set NOTIFY_SUBMIT_FLAG = #{notifySubmitFlag,jdbcType=CHAR},
			UPDATE_DTM = #{updateDtm, jdbcType=TIMESTAMP}
		where DEAL_DTL_NO = #{dealDtlNo,jdbcType=VARCHAR}
		  AND NOTIFY_SUBMIT_FLAG &lt;&gt; '2'
	</update>

	<!-- 更新订单明细中的交易申请状态或通知上报标记 -->
	<update id="updateAppFlagOrSubmitFlag" parameterType="map" >
		update HIGH_DEAL_ORDER_DTL
		set
		<if test="txAppFlag != null">
		TX_APP_FLAG = #{txAppFlag,jdbcType=CHAR},
		</if>
		<if test="notifySubmitFlag != null">
		NOTIFY_SUBMIT_FLAG = #{notifySubmitFlag,jdbcType=CHAR},
		</if>
		<if test="cancelOrderSrc != null">
		CANCEL_ORDER_SRC = #{cancelOrderSrc,jdbcType=CHAR},
		</if>
		<if test="memo != null">
		MEMO = #{memo,jdbcType=CHAR},
		</if>
		UPDATE_DTM = #{now,jdbcType=TIMESTAMP}
		where DEAL_DTL_NO = #{dealDtlNo,jdbcType=VARCHAR}
		and UPDATE_DTM = #{oldUpdateDtm,jdbcType=TIMESTAMP}
	</update>

  <update id="updateOtherMergeSubOrderSubmitFlag" parameterType="map" >
    update HIGH_DEAL_ORDER_DTL
    set
      NOTIFY_SUBMIT_FLAG = #{notifySubmitFlag,jdbcType=CHAR},
      UPDATE_DTM = #{now,jdbcType=TIMESTAMP}
    where MAIN_DEAL_ORDER_NO = #{mainDealOrderNo,jdbcType=VARCHAR}
    and DEAL_DTL_NO != #{dealDtlNo,jdbcType=VARCHAR}
  </update>

	<!-- 更新订单明细中的交易申请状态或通知上报标记 -->
	<update id="updateAppFlagAndTaTradeDt" parameterType="map">
		update HIGH_DEAL_ORDER_DTL
		set
		<if test="txAppFlag != null">
		TX_APP_FLAG = #{txAppFlag,jdbcType=CHAR},
		</if>
		<if test="taTradeDt != null">
		TA_TRADE_DT = #{taTradeDt, jdbcType=VARCHAR},
		</if>
		<if test="cancelOrderSrc != null">
		CANCEL_ORDER_SRC = #{cancelOrderSrc, jdbcType=VARCHAR},
		</if>
		UPDATE_DTM = #{now,jdbcType=TIMESTAMP}
		where DEAL_DTL_NO = #{dealDtlNo,jdbcType=VARCHAR}
		 and TX_APP_FLAG = #{oldTxAppFlag, jdbcType=CHAR}
	</update>

	<!-- 查询未签订电子合同的高端订单明细信息 -->
	<select id="selectUnEcontractOrderDtl" parameterType="map" resultMap="BaseResultMap">
		SELECT
		T.DEAL_NO,
		MIN(T.CREATE_DTM) as CREATE_DTM,
		T.FUND_CODE,
		T.TX_ACCT_NO,
		T.PRODUCT_CHANNEL,
		min(T.contract_version) as contract_version
		FROM HIGH_DEAL_ORDER_DTL T
		LEFT JOIN DEAL_ORDER_EXTEND E ON T.DEAL_NO = E.DEAL_NO
		LEFT JOIN SIMU_FUND_CHECK_ORDER S ON S.DEAL_NO = T.Deal_No
		LEFT JOIN CUST_ECONTRACT C ON
		C.TX_ACCT_NO = T.TX_ACCT_NO
		AND C.PRODUCT_CODE = T.FUND_CODE
		AND C.contract_status != '4'
		AND C.contract_version = T.contract_version
		WHERE
		(E.REC_STAT = '0' OR E.REC_STAT IS NULL)
		AND S.SUBMIT_APP_FLAG = '2' and T.tx_app_flag='0'
		AND S.TX_CHANNEL != '1'
		AND T.m_busi_code IN ('1120','1122')
		AND (T.main_deal_order_no IS NULL OR T.main_deal_order_no = T.deal_no)
		<if test="startDt != null">
			AND T.ta_trade_dt  <![CDATA[>=]]> #{startDt,jdbcType=VARCHAR}
		</if>
		AND C.TX_ACCT_NO IS NULL  -- 替换 NOT EXISTS 的条件
		GROUP BY
		T.FUND_CODE, T.TX_ACCT_NO, T.PRODUCT_CHANNEL, t.contract_version
	</select>

	<!-- 查询未签订电子合同的高端订单明细信息 -->
	<select id="selectCounterUnEcontractOrderDtl" parameterType="map" resultMap="BaseResultMap">
		SELECT
		MIN(T.DEAL_NO) as DEAL_NO,
		MIN(T.CREATE_DTM) as CREATE_DTM,
		T.FUND_CODE,
		T.TX_ACCT_NO,
		T.PRODUCT_CHANNEL,
		MIN(T.contract_version) as contract_version
		FROM
		HIGH_DEAL_ORDER_DTL T
		LEFT JOIN
		DEAL_ORDER_EXTEND E ON T.DEAL_NO = E.DEAL_NO
		LEFT JOIN
		SIMU_FUND_CHECK_ORDER S ON S.DEAL_NO = T.Deal_No
		INNER JOIN
		(
		<foreach collection="counterConfigs" item="cfg" separator=" union all ">
			<foreach collection="cfg.submitUserTypes" item="type" separator=" union all ">
				SELECT #{cfg.fundCode} as fund_code, #{type} as invst_type FROM dual
			</foreach>
		</foreach>
		) cfg ON T.fund_code = cfg.fund_code AND S.invst_type = cfg.invst_type
		LEFT JOIN
		CUST_ECONTRACT C ON
		C.TX_ACCT_NO = T.TX_ACCT_NO
		AND C.PRODUCT_CODE = T.FUND_CODE
		AND C.contract_status != '4'
		AND C.contract_version = T.contract_version
		WHERE
		(E.REC_STAT = '0' OR E.REC_STAT IS NULL)
		AND S.SUBMIT_APP_FLAG = '2'
		AND S.TX_CHANNEL = '1'
		AND T.m_busi_code IN ('1120','1122') and T.tx_app_flag='0'
		AND (T.main_deal_order_no IS NULL OR T.main_deal_order_no = T.deal_no)
		<if test="startDt != null">
			AND T.ta_trade_dt  <![CDATA[>=]]> #{startDt,jdbcType=VARCHAR}
		</if>
		AND C.TX_ACCT_NO IS NULL  -- 替换 NOT EXISTS 的条件
		GROUP BY
		T.FUND_CODE, T.TX_ACCT_NO, T.PRODUCT_CHANNEL, T.contract_version
	</select>

	<!-- 根据TA工作日将未支付的订单明细的交易申请标志设置为申请失败 -->
  <update id="updateUnpaymentAppFlagToFailByPmtCheckDt" parameterType="map">
  	update high_deal_order_dtl t
	set tx_app_flag = '1',
		tx_ack_flag = '0'
	where t.ta_code = #{taCode,jdbcType=VARCHAR}
  		and exists (
			select 1
			from payment_order p
			where t.deal_no = p.deal_no
			and tx_pmt_flag = '1'
			and pmt_check_dt = #{pmtCheckDt,jdbcType=VARCHAR}
			and deal_type = '2'
		)
  </update>

	<!-- 中控台-统计高端交易订单明细 -->
	<select id="selectCountHighDealOrderDtlForConsole" parameterType="map" resultMap="BaseResultMap">
  	select ifnull(sum(t1.APP_AMT), 0) as APP_AMT, ifnull(sum(t1.APP_VOL), 0) as APP_VOL
    from deal_order t1
	inner join HIGH_DEAL_ORDER_DTL t2 on t1.deal_no = t2.deal_no
	left join payment_order t3 on t1.deal_no = t3.deal_no
	left join deal_order_extend t4 on t1.deal_no = t4.deal_no
    <where>
    	(t4.REC_STAT='0' OR t4.REC_STAT is null)
        and t1.deal_type = '2'
    	<if test="condition.fundType != null">
	    	AND t1.PRODUCT_TYPE = #{condition.fundType,jdbcType=VARCHAR}
    	</if>
		<if test="condition.productClass != null">
	    	AND t1.PRODUCT_CLASS = #{condition.productClass,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.txAcctNo != null">
	    	AND t1.TX_ACCT_NO = #{condition.txAcctNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.custName != null">
	    	AND t1.CUST_NAME = #{condition.custName,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.idNo != null">
	    	AND t1.ID_NO = #{condition.idNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.bankAcct != null">
	    	AND t1.BANK_ACCT = #{condition.bankAcct,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.mBusiCode != null">
	    	AND t2.M_BUSI_CODE = #{condition.mBusiCode,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.fundCode != null">
	    	AND t1.PRODUCT_CODE = #{condition.fundCode,jdbcType=VARCHAR}
    	</if>
    	<if test="appDateStart != null">
	    	AND t1.APP_DTM  <![CDATA[ >= ]]> #{appDateStart,jdbcType=TIMESTAMP}
    	</if>
    	<if test="appDateEnd != null">
	    	AND t1.APP_DTM   <![CDATA[ < ]]> #{appDateEnd,jdbcType=TIMESTAMP}
    	</if>
    	<if test="taTradeDtStart != null">
	    	AND t1.TA_TRADE_DT <![CDATA[ >= ]]> #{taTradeDtStart,jdbcType=VARCHAR}
    	</if>
    	<if test="taTradeDtEnd != null">
	    	AND t1.TA_TRADE_DT <![CDATA[ <= ]]> #{taTradeDtEnd,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.ackDtStart != null">
	    	AND t2.ACK_DT <![CDATA[ >= ]]> #{condition.ackDtStart,jdbcType=VARCHAR}
    	</if>
        <if test="condition.ackDtEnd != null">
          AND t2.ACK_DT <![CDATA[ <= ]]> #{condition.ackDtEnd,jdbcType=VARCHAR}
        </if>
    	<if test="condition.dealNo != null">
	    	AND t1.DEAL_NO = #{condition.dealNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.dealDtlNo != null">
	    	AND t2.DEAL_DTL_NO = #{condition.dealDtlNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.pmtDealNo != null">
	    	AND t3.PMT_DEAL_NO = #{condition.pmtDealNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.tPmtDealNo != null">
	    	AND t3.T_PMT_DEAL_NO = #{condition.tPmtDealNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.disCode != null">
	    	AND t1.DIS_CODE = #{condition.disCode,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.txChannel != null">
	    	AND t1.TX_CHANNEL = #{condition.txChannel,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.payStatus != null">
	    	AND t1.PAY_STATUS = #{condition.payStatus,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.dtlPayStatus != null">
	    	AND t3.TX_PMT_FLAG = #{condition.dtlPayStatus,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.pmtCompFlag != null">
	    	AND t3.PMT_COMP_FLAG = #{condition.pmtCompFlag,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.paymentType != null">
	    	AND t1.PAYMENT_TYPE = #{condition.paymentType,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.orderStatus != null">
	    	AND t1.ORDER_STATUS = #{condition.orderStatus,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.txAppFlag != null">
	    	AND t2.TX_APP_FLAG = #{condition.txAppFlag,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.txAckFlag != null">
	    	AND t2.TX_ACK_FLAG = #{condition.txAckFlag,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.notifySubmitFlag != null">
	    	AND t2.NOTIFY_SUBMIT_FLAG = #{condition.notifySubmitFlag,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.redeemDirection != null">
	    	AND t2.REDEEM_DIRECTION = #{condition.redeemDirection,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.pmtCompleteStartDtm !=null">
    		AND t3.pmt_complete_dtm <![CDATA[>=]]> #{condition.pmtCompleteStartDtm,jdbcType=TIMESTAMP}
    	</if>
    	<if test="condition.pmtCompleteEndDtm !=null">
    		AND t3.pmt_complete_dtm <![CDATA[<]]> #{condition.pmtCompleteEndDtm,jdbcType=TIMESTAMP}
    	</if>
    	<if test="condition.zBusiCode !=null">
    		AND t1.Z_BUSI_CODE = #{condition.zBusiCode,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.custRiskLevel !=null">
    		AND t2.CUST_RISK_LEVEL = #{condition.custRiskLevel,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.advanceFlag !=null">
    		AND t1.ADVANCE_FLAG = #{condition.advanceFlag,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.orderFormType !=null">
    		AND t2.ORDER_FORM_TYPE = #{condition.orderFormType,jdbcType=VARCHAR}
    	</if>
    </where>
  </select>

  	<!-- 中控台-高端交易订单明细查询 -->
  <resultMap id="ConsoleResultMap" type="com.howbuy.tms.high.batch.dao.vo.ConsoleHighFundDealOrderDtlVo">
  	<id column="DEAL_DTL_NO" jdbcType="VARCHAR" property="dealDtlNo" />
    <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo" />
	<result column="Z_BUSI_CODE" jdbcType="VARCHAR" property="zBusiCode"/>
	<result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo" />
	<result column="CUST_NAME" jdbcType="VARCHAR" property="custName" />
	<result column="ID_NO" jdbcType="VARCHAR" property="idNo" />
	<result column="TX_CODE" jdbcType="VARCHAR" property="txCode" />
	<result column="DIS_CODE" jdbcType="VARCHAR" property="disCode" />
	<result column="TX_CHANNEL" jdbcType="VARCHAR" property="txChannel" />
    <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt" />
    <result column="APP_VOL" jdbcType="DECIMAL" property="appVol" />
    <result column="APP_DATE" jdbcType="VARCHAR" property="appDate" />
    <result column="APP_TIME" jdbcType="VARCHAR" property="appTime" />
	<result column="APP_DTM" jdbcType="TIMESTAMP" property="appDtm" />
	<result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt" />
	<result column="PAYMENT_TYPE" jdbcType="VARCHAR" property="paymentType" />
	<result column="ORDER_STATUS" jdbcType="VARCHAR" property="orderStatus" />
	<result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode" />
	<result column="ADVANCE_FLAG" jdbcType="VARCHAR" property="advanceFlag" />
	<result column="DEAL_TYPE" jdbcType="VARCHAR" property="dealType" />
	<result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode" />
	<result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName" />
	<result column="PRODUCT_CLASS" jdbcType="VARCHAR" property="productClass" />
	<result column="CP_ACCT_NO" jdbcType="VARCHAR" property="cpAcctNo" />
	<result column="BANK_ACCT" jdbcType="VARCHAR" property="bankAcct" />
	<result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode" />
	<result column="PROTOCOL_TYPE" jdbcType="VARCHAR" property="protocolType" />
	<result column="PROTOCOL_NO" jdbcType="VARCHAR" property="protocolNo" />
    <result column="M_BUSI_CODE" jdbcType="CHAR" property="mBusiCode" />
    <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
	<result column="FUND_NAME" jdbcType="VARCHAR" property="fundName" />
	<result column="FUND_DIV_MODE" jdbcType="CHAR" property="fundDivMode" />
	<result column="FUND_TYPE" jdbcType="VARCHAR" property="fundType" />
	<result column="FUND_SUB_TYPE" jdbcType="VARCHAR" property="fundSubType" />
	<result column="NAV" jdbcType="VARCHAR" property="nav" />
	<result column="PRODUCT_CHANNEL" jdbcType="VARCHAR" property="productChannel"/>
	<result column="ACK_AMT" jdbcType="DECIMAL" property="ackAmt" />
	<result column="ACK_VOL" jdbcType="DECIMAL" property="ackVol" />
	<result column="ACK_DT" jdbcType="VARCHAR" property="ackDt" />
	<result column="TX_APP_FLAG" jdbcType="CHAR" property="txAppFlag" />
	<result column="TX_ACK_FLAG" jdbcType="CHAR" property="txAckFlag" />
	<result column="NOTIFY_SUBMIT_FLAG" jdbcType="CHAR" property="notifySubmitFlag" />
	<result column="MEMO" jdbcType="VARCHAR" property="memo" />
    <result column="DISCOUNT_RATE" jdbcType="DECIMAL" property="discountRate" />
    <result column="REDEEM_DIRECTION" jdbcType="CHAR" property="redeemDirection" />
    <result column="LIMIT_TYPE" jdbcType="VARCHAR" property="limitType" />
    <result column="INTEREST" jdbcType="DECIMAL" property="interest" />
	<result column="APPOINTMENT_DEALNO_TYPE" jdbcType="VARCHAR" property="appointmentDealNoType"/>
	<result column="APPOINTMENT_DISCOUNT" jdbcType="DECIMAL" property="appointmentDiscount" />
    <result column="ACHIEVEMENT_PAY" property="achievementPay" jdbcType="DECIMAL" />
    <result column="ACHIEVEMENT_COMPEN" property="achievementCompen" jdbcType="DECIMAL" />
    <result column="VOL_BY_INTEREST" jdbcType="DECIMAL" property="volByInterest" />
    <result column="CALM_DTM" jdbcType="VARCHAR" property="calmDtm" />
    <result column="CALM_TIME" jdbcType="VARCHAR" property="calmTime" />
    <result column="FIRST_BUY_FLAG" jdbcType="VARCHAR" property="firstBuyFlag" />
    <result column="ESIGNATURE_FLAG" jdbcType="VARCHAR" property="esignatureFlag" />
    <result column="ECONTRACT_FLAG" jdbcType="VARCHAR" property="econtractFlag" />
    <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt" />
	<result column="ALLOW_DT" jdbcType="VARCHAR" property="allowDt" />
	<result column="RISK_FLAG" jdbcType="VARCHAR" property="riskFlag" />
	<result column="CUST_RISK_LEVEL" jdbcType="VARCHAR" property="custRiskLevel" />
	<result column="ORDER_FORM_TYPE" jdbcType="VARCHAR" property="orderFormType" />
	<result column="CANCEL_ORDER_SRC" jdbcType="CHAR" property="cancelOrderSrc"/>
	<result column="UPDATE_DTM" jdbcType="TIMESTAMP" property="updateDtm" />
    <result column="DTL_CP_ACCT_NO" jdbcType="VARCHAR" property="dtlCpAcctNo" />
	<result column="DTL_BANK_ACCT" jdbcType="VARCHAR" property="dtlBankAcct" />
	<result column="DTL_BANK_CODE" jdbcType="VARCHAR" property="dtlBankCode" />
	<result column="DTL_PROTOCOL_TYPE" jdbcType="VARCHAR" property="dtlProtocolType" />
	<result column="DTL_PROTOCOL_NO" jdbcType="VARCHAR" property="dtlProtocolNo" />
	<result column="PMT_DEAL_NO" jdbcType="VARCHAR" property="pmtDealNo" />
	<result column="T_PMT_DEAL_NO" jdbcType="VARCHAR" property="tPmtDealNo" />
    <result column="TX_PMT_FLAG" jdbcType="VARCHAR" property="payStatus" />
    <result column="PMT_COMP_FLAG" jdbcType="VARCHAR" property="pmtCompFlag" />
	<result column="pmt_complete_dtm" jdbcType="TIMESTAMP" property="pmtCompleteDtm" />
	<result column="PMT_CHECK_DT" jdbcType="VARCHAR" property="pmtCheckDt" />
    <result column="RET_DESC" jdbcType="VARCHAR" property="retDesc" />
    <result column="SUB_BANK_NAME" jdbcType="VARCHAR" property="subBankName" />
    <result column="APPOINTMENT_DEAL_NO" jdbcType="VARCHAR" property="appointmentDealNo" />
    <result column="REC_STAT" jdbcType="VARCHAR" property="recStat" />
    <result column="RISK_ACK_DTM" jdbcType="VARCHAR" property="riskAckDtm" />
    <result column="HIGH_RISK_TIP_DTM" jdbcType="VARCHAR" property="highRiskTipDtm" />
    <result column="NORMAL_CUST_TIP_DTM" jdbcType="VARCHAR" property="normalCustTipDtm" />
    <result column="TRADE_DT" jdbcType="VARCHAR" property="tradeDt" />
    <result column="TARGET_PROTOCOL_NO" jdbcType="VARCHAR" property="targetProtocolNo" />
    <result column="TARGET_BANK_ACCT" jdbcType="VARCHAR" property="targetBankAcct" />
    <result column="DUALENTRY_STATUS" jdbcType="VARCHAR" property="dualentryStatus"/>
    <result column="DUALENTRY_FINISH_DTM" jdbcType="TIMESTAMP" property="dualentryFinishDtm"/>
    <result column="DUALENTRY_INTERPOSE_FLAG" jdbcType="VARCHAR" property="dualentryInterposeFlag"/>
    <result column="CALLBACK_STATUS" jdbcType="VARCHAR" property="callbackStatus"/>
    <result column="CALLBACK_FINISH_DTM" jdbcType="TIMESTAMP" property="callbackFinishDtm"/>
    <result column="CALLBACK_INTERPOSE_FLAG" jdbcType="VARCHAR" property="callbackInterposeFlag"/>
    <result column="CALMDTM_INTERPOSE_FLAG" jdbcType="VARCHAR" property="calmdtmInterposeFlag"/>
    <result column="ASSETCERTIFICATE_STATUS" jdbcType="VARCHAR" property="assetcertificateStatus"/>
    <result column="ASSET_INTERPOSE_FLAG" jdbcType="VARCHAR" property="assetInterposeFlag"/>
    <result column="SUBMIT_RET_CODE" jdbcType="VARCHAR" property="submitRetCode"/>
    <result column="SUBMIT_RET_DESC" jdbcType="VARCHAR" property="submitRetDesc"/>
    <result column="BUSI_CODE" jdbcType="VARCHAR" property="busiCode"/>
    <result column="NET_APP_AMT" jdbcType="VARCHAR" property="netAppAmt"/>
    <result column="QUALIFICATION_TYPE" jdbcType="VARCHAR" property="qualificationType"/>
    <result column="FUND_RISK_LEVEL" jdbcType="VARCHAR" property="fundRiskLevel"/>
    <result column="FEE" jdbcType="DECIMAL" property="fee"/>
    <result column="TARGET_CP_ACCT_NO" jdbcType="VARCHAR" property="targetCpAcctNo"/>
    <result column="HIGH_FUND_INV_PLAN_FLAG" jdbcType="VARCHAR" property="highFundInvPlanFlag"/>
    <result column="INVEST_ACK_DTM" jdbcType="VARCHAR" property="investAckDtm"/>
    <result column="continuance_flag" jdbcType="VARCHAR" property="continuanceFlag"/>
    <result column="stage_flag" jdbcType="VARCHAR" property="stageFlag"/>
	<result column="service_entity_name" jdbcType="VARCHAR" property="serviceEntityName" />
	<result column="fpqc_code" jdbcType="VARCHAR" property="fpqcCode" />
	<result column="service_confirm_time" jdbcType="TIMESTAMP" property="serviceConfirmTime" />
	<result column="risk_tolerance_date" jdbcType="VARCHAR" property="riskToleranceDate" />
	<result column="investor_qualified_date" jdbcType="VARCHAR" property="investorQualifiedDate" />
	<result column="risk_hint_confirm_dtm" jdbcType="VARCHAR" property="riskHintConfirmDtm" />
	<result column="investor_qualified_hint_confirm_dtm" jdbcType="VARCHAR" property="investorQualifiedHintConfirmDtm" />
  </resultMap>

  <select id="selectHighDealOrderDtlForConsole" parameterType="map" resultMap="ConsoleResultMap">
  	select
  		t2.DEAL_DTL_NO,
    	t1.DEAL_NO,
    	t1.Z_BUSI_CODE,
    	t1.TX_ACCT_NO,
    	t1.CUST_NAME,
    	t1.ID_NO,
    	t1.TX_CODE,
    	t1.DIS_CODE,
    	t1.TX_CHANNEL,
    	t1.APP_AMT,
    	t1.APP_VOL,
    	t1.APP_DATE,
    	t1.APP_TIME,
    	t1.APP_DTM,
    	t1.TA_TRADE_DT,
    	t1.PAYMENT_TYPE,
    	t1.ORDER_STATUS,
    	t1.CHANNEL_CODE,
        t1.ADVANCE_FLAG,
        t1.DEAL_TYPE,
        t1.PRODUCT_CODE,
        t1.PRODUCT_NAME,
        t1.PRODUCT_CLASS,
	  ifnull(t2.CP_ACCT_NO, t1.CP_ACCT_NO) as CP_ACCT_NO,
	  ifnull(t2.BANK_ACCT, t1.BANK_ACCT) as BANK_ACCT,
	  ifnull(t2.BANK_CODE,t1.BANK_CODE) as BANK_CODE,
    	t1.PROTOCOL_TYPE,
    	t2.PROTOCOL_NO as PROTOCOL_NO,
    	t2.M_BUSI_CODE,
    	t2.FUND_CODE,
    	t2.FUND_NAME,
    	t2.FUND_DIV_MODE,
    	t2.FUND_TYPE,
        t2.FUND_SUB_TYPE,
        t2.NAV,
        t2.PRODUCT_CHANNEL,
    	t2.ACK_AMT,
    	t2.ACK_VOL,
    	t2.ACK_DT,
    	t2.TX_APP_FLAG,
    	t2.TX_ACK_FLAG,
    	t2.NOTIFY_SUBMIT_FLAG,
    	t2.MEMO,
    	t2.DISCOUNT_RATE,
    	t2.REDEEM_DIRECTION,
        t2.LIMIT_TYPE,
        t2.INTEREST,
        t2.APPOINTMENT_DEALNO_TYPE,
        t2.APPOINTMENT_DISCOUNT,
        t2.ACHIEVEMENT_PAY,
        t2.ACHIEVEMENT_COMPEN,
        t2.VOL_BY_INTEREST,
        t2.CALM_DTM,
        t2.CALM_TIME,
        t2.FIRST_BUY_FLAG,
        t2.ESIGNATURE_FLAG,
        t2.ECONTRACT_FLAG,
        t2.SUBMIT_TA_DT,
        t2.ALLOW_DT,
        t2.RISK_FLAG,
        t2.CUST_RISK_LEVEL,
        t2.ORDER_FORM_TYPE,
        t2.CANCEL_ORDER_SRC,
        t2.UPDATE_DTM,
        t2.CP_ACCT_NO     as DTL_CP_ACCT_NO,
    	t2.BANK_ACCT      as DTL_BANK_ACCT,
    	t2.BANK_CODE      as DTL_BANK_CODE,
    	t2.PROTOCOL_TYPE  as DTL_PROTOCOL_TYPE,
        t2.PROTOCOL_NO    as DTL_PROTOCOL_NO,
        t3.PMT_DEAL_NO,
        t3.T_PMT_DEAL_NO,
    	t3.TX_PMT_FLAG,
    	t3.PMT_COMP_FLAG,
    	t3.PMT_COMPLETE_DTM,
    	t3.PMT_CHECK_DT,
    	t3.RET_DESC,
        t3.SUB_BANK_NAME,
        t4.APPOINTMENT_DEAL_NO,
        t4.REC_STAT,
        t4.RISK_ACK_DTM,
        t4.HIGH_RISK_TIP_DTM,
        t4.NORMAL_CUST_TIP_DTM,
        t4.INVEST_ACK_DTM,
	  	t4.service_entity_name,
	  	t4.fpqc_code,
	  	t4.service_confirm_time,
		t5.TRADE_DT,
		t1.PROTOCOL_NO as TARGET_PROTOCOL_NO,
        t1.BANK_ACCT as TARGET_BANK_ACCT,
        t1.CP_ACCT_NO as TARGET_CP_ACCT_NO,
        t2.DUALENTRY_STATUS,
        t2.DUALENTRY_FINISH_DTM,
        t2.DUALENTRY_INTERPOSE_FLAG,
        t2.CALLBACK_STATUS,
        t2.CALLBACK_FINISH_DTM,
        t2.CALLBACK_INTERPOSE_FLAG,
        t2.CALMDTM_INTERPOSE_FLAG,
        t2.ASSETCERTIFICATE_STATUS,
        t2.ASSET_INTERPOSE_FLAG,
        t5.RET_CODE as SUBMIT_RET_CODE,
        t5.RET_DESC as SUBMIT_RET_DESC,
        t5.busi_code,
        t2.NET_APP_AMT,
        t1.QUALIFICATION_TYPE,
        t2.FUND_RISK_LEVEL,
        t2.fee,
        t2.HIGH_FUND_INV_PLAN_FLAG,
        t2.continuance_flag,
        t2.stage_flag,
	    t4.risk_tolerance_date,
	    t4.investor_qualified_date,
	  	t4.risk_hint_confirm_dtm,
	  	t4.investor_qualified_hint_confirm_dtm
    FROM HIGH_DEAL_ORDER_DTL t2
    LEFT JOIN deal_order t1 ON t1.deal_no = t2.deal_no
    LEFT JOIN payment_order t3 ON t3.deal_no = t2.deal_no
    LEFT JOIN deal_order_extend t4 ON t4.deal_no = t2.deal_no
    LEFT JOIN simu_fund_check_order t5 ON t5.deal_no = t2.deal_no
    WHERE t1.deal_type = '2'
        and (t4.REC_STAT='0' OR t4.REC_STAT is null)
    	<if test="condition.fundType != null">
	    	AND t1.PRODUCT_TYPE = #{condition.fundType,jdbcType=VARCHAR}
    	</if>
		<if test="condition.productClass != null">
	    	AND t1.PRODUCT_CLASS = #{condition.productClass,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.txAcctNo != null">
	    	AND t1.TX_ACCT_NO = #{condition.txAcctNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.custName != null">
	    	AND t1.CUST_NAME = #{condition.custName,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.idNo != null">
	    	AND t1.ID_NO = #{condition.idNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.bankAcct != null">
	    	AND t1.BANK_ACCT = #{condition.bankAcct,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.mBusiCode != null">
	    	AND t2.M_BUSI_CODE = #{condition.mBusiCode,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.fundCode != null">
	    	AND t1.PRODUCT_CODE = #{condition.fundCode,jdbcType=VARCHAR}
    	</if>
    	<if test="appDateStart != null">
	    	AND t1.APP_DTM  <![CDATA[ >= ]]> #{appDateStart,jdbcType=TIMESTAMP}
    	</if>
    	<if test="appDateEnd != null">
	    	AND t1.APP_DTM   <![CDATA[ < ]]> #{appDateEnd,jdbcType=TIMESTAMP}
    	</if>
    	<if test="taTradeDtStart != null">
	    	AND t1.TA_TRADE_DT <![CDATA[ >= ]]> #{taTradeDtStart,jdbcType=VARCHAR}
    	</if>
    	<if test="taTradeDtEnd != null">
	    	AND t1.TA_TRADE_DT <![CDATA[ <= ]]> #{taTradeDtEnd,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.ackDtStart != null">
	    	AND t2.ACK_DT <![CDATA[>=]]> #{condition.ackDtStart,jdbcType=VARCHAR}
    	</if>
        <if test="condition.ackDtEnd != null">
          AND t2.ACK_DT <![CDATA[<=]]> #{condition.ackDtEnd,jdbcType=VARCHAR}
        </if>
    	<if test="condition.dealNo != null">
	    	AND t1.DEAL_NO = #{condition.dealNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.dealDtlNo != null">
	    	AND t2.DEAL_DTL_NO = #{condition.dealDtlNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.pmtDealNo != null">
	    	AND t3.PMT_DEAL_NO = #{condition.pmtDealNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.tPmtDealNo != null">
	    	AND t3.T_PMT_DEAL_NO = #{condition.tPmtDealNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.disCode != null">
	    	AND t1.DIS_CODE = #{condition.disCode,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.txChannel != null">
	    	AND t1.TX_CHANNEL = #{condition.txChannel,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.payStatus != null">
	    	AND t1.PAY_STATUS = #{condition.payStatus,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.dtlPayStatus != null">
	    	AND t3.TX_PMT_FLAG = #{condition.dtlPayStatus,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.pmtCompFlag != null">
	    	AND t3.PMT_COMP_FLAG = #{condition.pmtCompFlag,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.paymentType != null">
	    	AND t1.PAYMENT_TYPE = #{condition.paymentType,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.orderStatus != null">
	    	AND t1.ORDER_STATUS = #{condition.orderStatus,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.txAppFlag != null">
	    	AND t2.TX_APP_FLAG = #{condition.txAppFlag,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.txAckFlag != null">
	    	AND t2.TX_ACK_FLAG = #{condition.txAckFlag,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.notifySubmitFlag != null">
	    	AND t2.NOTIFY_SUBMIT_FLAG = #{condition.notifySubmitFlag,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.redeemDirection != null">
	    	AND t2.REDEEM_DIRECTION = #{condition.redeemDirection,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.pmtCompleteStartDtm !=null">
    		AND t3.pmt_complete_dtm <![CDATA[>=]]> #{condition.pmtCompleteStartDtm,jdbcType=TIMESTAMP}
    	</if>
    	<if test="condition.pmtCompleteEndDtm !=null">
    		AND t3.pmt_complete_dtm <![CDATA[<]]> #{condition.pmtCompleteEndDtm,jdbcType=TIMESTAMP}
    	</if>
    	<if test="condition.zBusiCode !=null">
    		AND t1.Z_BUSI_CODE = #{condition.zBusiCode,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.custRiskLevel !=null">
    		AND t2.CUST_RISK_LEVEL = #{condition.custRiskLevel,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.advanceFlag !=null">
    		AND t1.ADVANCE_FLAG = #{condition.advanceFlag,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.orderFormType !=null">
    		AND t2.ORDER_FORM_TYPE = #{condition.orderFormType,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.tradeDt != null">
	    	AND t5.TRADE_DT = #{condition.tradeDt,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.submitTaDtStart != null">
	    	AND t2.SUBMIT_TA_DT <![CDATA[>=]]> #{condition.submitTaDtStart,jdbcType=VARCHAR}
    	</if>
        <if test="condition.submitTaDtEnd != null">
          AND t2.SUBMIT_TA_DT <![CDATA[<=]]> #{condition.submitTaDtEnd,jdbcType=VARCHAR}
        </if>
    	<if test="condition.cancelOrderSrc !=null">
    		AND T2.CANCEL_ORDER_SRC = #{condition.cancelOrderSrc,jdbcType=VARCHAR}
    	</if>
    	<if test= "condition.fundSubType != null">
    	    and t2.FUND_SUB_TYPE = #{condition.fundSubType,jdbcType=VARCHAR}
    	</if>
    	<if test= "condition.dualentryStatus != null">
    	    and t2.DUALENTRY_STATUS = #{condition.dualentryStatus,jdbcType=VARCHAR}
    	</if>

    	<if test= "condition.callbackStatus != null">
    	    and t2.CALLBACK_STATUS = #{condition.callbackStatus,jdbcType=VARCHAR}
    	</if>
    	<if test= "condition.assetcertificateStatus != null">
    	    and t2.ASSETCERTIFICATE_STATUS = #{condition.assetcertificateStatus,jdbcType=VARCHAR}
    	</if>
        <if test= "condition.highFundInvPlanFlag == '0'.toString()">
          and t2.HIGH_FUND_INV_PLAN_FLAG != '1'
        </if>
        <if test= "condition.highFundInvPlanFlag == '1'.toString()">
          and t2.HIGH_FUND_INV_PLAN_FLAG = '1'
        </if>
        <if test="condition.fundCodeList != null">
            and  t2.FUND_CODE in <foreach collection="condition.fundCodeList" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
        </if>
        <if test="condition.filterFundCodeList != null and condition.filterFundCodeList.size()>0">
          and  t2.FUND_CODE not in
          <foreach collection="condition.filterFundCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
          </foreach>
        </if>
        <if test="condition.taCodeList != null and condition.taCodeList.size()>0">
           and  t2.TA_CODE in
           <foreach collection="condition.taCodeList" index="index" item="item" open="(" separator="," close=")">
             #{item}
           </foreach>
          </if>

    order by t1.APP_DATE desc, t1.APP_TIME desc, t2.DEAL_DTL_NO desc
  </select>

  	<!-- 更新订单明细TA工作日 -->
	<update id="updateSubmitTaDt" parameterType="map" >
	    update HIGH_DEAL_ORDER_DTL
	    set
	    <if test="newSubmitTaDt != null">
			SUBMIT_TA_DT = #{newSubmitTaDt,jdbcType=VARCHAR},
		</if>
		<if test="mBusiCode != null">
			M_BUSI_CODE = #{mBusiCode,jdbcType=CHAR},
		</if>
		UPDATE_DTM = NOW()
	    where DEAL_DTL_NO = #{dealDtlNo,jdbcType=VARCHAR}
	    and SUBMIT_TA_DT = #{oldSubmitTaDt,jdbcType=VARCHAR}
  	</update>

  <select id="countNotNotify" resultType="java.lang.Integer">
	  SELECT COUNT(1)
	  FROM HIGH_DEAL_ORDER_DTL A
	  WHERE
	  <choose>
	        <when test="productChannel == '6'.toString()">
				A.SUBMIT_TA_DT = #{taTradeDt,jdbcType=VARCHAR}
	        </when>
	        <otherwise>
	            A.TA_TRADE_DT = #{taTradeDt,jdbcType=VARCHAR}
	        </otherwise>
	  </choose>
	  AND A.NOTIFY_SUBMIT_FLAG IN ('1', '3')
	  <if test="productChannel != null">
	    	AND A.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
      </if>

  </select>
  	<!-- 中控台-高端强制取消交易订单明细查询 -->
  <resultMap id="ConsoleForceResultMap" type="com.howbuy.tms.high.batch.dao.vo.CondoleHighForceDealOrderVo">
    <id column="DEAL_DTL_NO" jdbcType="VARCHAR" property="dealDtlNo" />
    <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo" />
    <result column="ID_NO" jdbcType="VARCHAR" property="idNo" />
    <result column="CUST_NAME" jdbcType="VARCHAR" property="custName" />
    <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
    <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt" />
    <result column="APP_VOL" jdbcType="DECIMAL" property="appVol" />
    <result column="DISCOUNT_RATE" jdbcType="DECIMAL" property="discountRate" />
    <result column="TX_APP_FLAG" jdbcType="CHAR" property="txAppFlag" />
    <result column="M_BUSI_CODE" jdbcType="CHAR" property="mBusiCode" />
    <result column="ACK_AMT" jdbcType="DECIMAL" property="ackAmt" />
    <result column="ACK_VOL" jdbcType="DECIMAL" property="ackVol" />
    <result column="ACK_DT" jdbcType="VARCHAR" property="ackDt" />
    <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt" />
    <result column="NOTIFY_SUBMIT_FLAG" jdbcType="CHAR" property="notifySubmitFlag" />
    <result column="DIS_CODE" jdbcType="VARCHAR" property="disCode" />
    <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo" />
    <result column="BANK_ACCT" jdbcType="VARCHAR" property="bankAcct" />
    <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode" />
    <result column="TX_CHANNEL" jdbcType="VARCHAR" property="txChannel" />
    <result column="APP_DATE" jdbcType="VARCHAR" property="appDate" />
    <result column="APP_TIME" jdbcType="VARCHAR" property="appTime" />
    <result column="TX_PMT_FLAG" jdbcType="VARCHAR" property="payStatus" />
    <result column="PAYMENT_TYPE" jdbcType="VARCHAR" property="paymentType" />
    <result column="ORDER_STATUS" jdbcType="VARCHAR" property="orderStatus" />
    <result column="MEMO" jdbcType="VARCHAR" property="memo" />
    <result column="NAV" jdbcType="VARCHAR" property="nav" />
    <result column="CP_ACCT_NO" jdbcType="VARCHAR" property="cpAcctNo" />
    <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode" />
    <result column="ADVANCE_FLAG" jdbcType="VARCHAR" property="advanceFlag" />
    <result column="APPOINTMENT_DEAL_NO" jdbcType="VARCHAR" property="appointmentDealNo" />
    <result column="APP_DTM" jdbcType="TIMESTAMP" property="appDtm" />
    <result column="DEAL_TYPE" jdbcType="VARCHAR" property="dealType" />
    <result column="PRODUCT_CLASS" jdbcType="VARCHAR" property="productClass" />
    <result column="FUND_NAME" jdbcType="VARCHAR" property="fundName" />
    <result column="PROTOCOL_TYPE" jdbcType="VARCHAR" property="protocolType" />
    <result column="APPOINTMENT_DISCOUNT" jdbcType="DECIMAL" property="appointmentDiscount" />
    <result column="FUND_TYPE" jdbcType="VARCHAR" property="fundType" />
    <result column="FUND_SUB_TYPE" jdbcType="VARCHAR" property="fundSubType" />
  	<result column="Z_BUSI_CODE" jdbcType="VARCHAR" property="zBusiCode"/>
  </resultMap>
  <select id="selectHighForceDealOrderForConsole" parameterType="map" resultMap="ConsoleForceResultMap">
      select
    	 t1.DEAL_NO,
    	 t1.DEAL_DTL_NO,
    	 t2.TX_ACCT_NO,
    	 t2.CUST_NAME,
    	 t2.ID_NO,
    	 t2.BANK_ACCT,
    	 t1.M_BUSI_CODE,
    	 t2.PRODUCT_CODE,
    	 t1.FUND_CODE,
    	 t1.DIS_CODE,
    	 t2.TX_CHANNEL,
    	 t1.APP_AMT,
    	 t1.APP_VOL,
    	 t1.ACK_AMT,
    	 t1.ACK_VOL,
    	 t2.APP_DATE,
    	 t2.APP_TIME,
    	 t1.TA_TRADE_DT,
    	 t1.DISCOUNT_RATE,
    	 t3.TX_PMT_FLAG,
    	 t2.PAYMENT_TYPE,
    	 t3.PMT_COMP_FLAG,
    	 t1.ACK_DT,
    	 t2.ORDER_STATUS,
    	 t1.TX_APP_FLAG,
    	 t1.NOTIFY_SUBMIT_FLAG,
    	 t1.MEMO,
    	 t1.NAV,
    	 t2.CP_ACCT_NO,
         t2.CHANNEL_CODE,
         t2.ADVANCE_FLAG,
         t4.APPOINTMENT_DEAL_NO,
         t2.APP_DTM,t2.DEAL_TYPE,
         t2.PRODUCT_CLASS,
         t1.FUND_NAME,
         t2.PROTOCOL_TYPE,
         t1.APPOINTMENT_DISCOUNT,
         t1.FUND_TYPE,
         t1.FUND_SUB_TYPE,
         t2.Z_BUSI_CODE
    from HIGH_DEAL_ORDER_DTL t1
	inner join deal_order t2 on t1.deal_no = t2.deal_no
	left join payment_order t3 on t1.deal_no = t3.deal_no
	left join deal_order_extend t4 on t1.deal_no = t4.deal_no
    <where>
    	(t4.REC_STAT='0' OR t4.REC_STAT is null)
        and t2.ORDER_STATUS = '6'
        and t2.PAY_STATUS = '4'
        and t1.CANCEL_ORDER_SRC = '3'
        and t1.PRODUCT_CHANNEL = '3'
    	<if test="condition.txAcctNo != null">
	    	AND t1.TX_ACCT_NO = #{condition.txAcctNo,jdbcType=VARCHAR}
    	</if>
    	<if test="appDateStart != null">
	    	AND  t2.APP_DTM  <![CDATA[ >= ]]> #{appDateStart,jdbcType=TIMESTAMP}
    	</if>
    	<if test="appDateEnd != null">
	    	AND t2.APP_DTM   <![CDATA[ < ]]> #{appDateEnd,jdbcType=TIMESTAMP}
    	</if>
    	<if test="taTradeDtStart != null">
	    	AND t2.TA_TRADE_DT <![CDATA[ >= ]]> #{taTradeDtStart,jdbcType=VARCHAR}
    	</if>
    	<if test="taTradeDtEnd != null">
	    	AND t2.TA_TRADE_DT <![CDATA[ <= ]]> #{taTradeDtEnd,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.dealNo != null">
	    	AND t1.DEAL_NO = #{condition.dealNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.dealDtlNo != null">
	    	AND t1.DEAL_DTL_NO = #{condition.dealDtlNo,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.disCode != null">
	    	AND t2.DIS_CODE = #{condition.disCode,jdbcType=VARCHAR}
    	</if>
    	<if test="condition.txChannel != null">
	    	AND t2.TX_CHANNEL = #{condition.txChannel,jdbcType=VARCHAR}
    	</if>
    </where>
    order by t2.APP_DATE desc, t2.APP_TIME desc, t1.DEAL_DTL_NO desc
  </select>
  <update id="updateSubmitFlagWithForce" parameterType="map" >
  	 update HIGH_DEAL_ORDER_DTL
	    set TX_APP_FLAG = #{highDealOrderDtl.txAppFlag,jdbcType=CHAR},
	    CANCEL_ORDER_SRC = #{highDealOrderDtl.cancelOrderSrc,jdbcType=CHAR},
		UPDATE_DTM = #{highDealOrderDtl.updateDtm,jdbcType=TIMESTAMP}
	    where DEAL_NO = #{dealNo,jdbcType=VARCHAR}
	    and TX_APP_FLAG in('1', '3')
  </update>

  <!-- 根据订单号查询订单明细数量 -->
	<select id="countDealOrderDtl" resultType="int" parameterType="map">
		select count(1)
		from HIGH_DEAL_ORDER_DTL t
		where t.deal_no = #{dealNo,jdbcType=VARCHAR}
		and t.tx_app_flag = '0'
	</select>

  	<!-- 查询未确认的份额合并/迁移订单 -->
	<select id="countUnAckShareMergeDealOrder" resultType="int" parameterType="map">
		select count(1)
  		from HIGH_DEAL_ORDER_DTL
		where m_busi_code in ('1362', '1364')
   			and tx_app_flag = '0'
   			and (tx_ack_flag is null or tx_ack_flag in ('1', '2', '3'))
   			and ta_trade_dt &lt;= #{taTradeDt,jdbcType=VARCHAR}
   			and create_dtm &gt;= #{startDtm,jdbcType=TIMESTAMP}
	</select>
  <!-- 更新订单明细中的通知上报标记 -->
	<update id="updateStateOfNotify" parameterType="map" >
		update HIGH_DEAL_ORDER_DTL
		set
		<if test = "notifySubmitFlag !=null">
		    NOTIFY_SUBMIT_FLAG = #{notifySubmitFlag,jdbcType=CHAR},
		</if>
		<if test="txAppFlag != null">
		TX_APP_FLAG = #{txAppFlag,jdbcType=CHAR},
		</if>
		<if test="calmDtm != null">
		CALM_DTM = #{calmDtm,jdbcType=TIMESTAMP},
		</if>
		<if test="cancelSrc != null">
		 CANCEL_ORDER_SRC = #{cancelSrc, jdbcType=VARCHAR},
		</if>
		UPDATE_DTM = #{now,jdbcType=TIMESTAMP}
		where DEAL_DTL_NO = #{dealDtlNo,jdbcType=VARCHAR}
		<if test="oldUpdateDtm != null">
		and UPDATE_DTM = #{oldUpdateDtm,jdbcType=TIMESTAMP}
		</if>

	</update>

	<update id="updateFaceCancelNoNeedNotifyOtherMergeOrders" parameterType="map" >
		update HIGH_DEAL_ORDER_DTL
		set NOTIFY_SUBMIT_FLAG = '0',
            TX_APP_FLAG = '3',
            REFUND_DT = #{refundDt,jdbcType=VARCHAR},
            CANCEL_ORDER_SRC = #{cancelOrderSrc,jdbcType=VARCHAR},
            UPDATE_DTM = #{now,jdbcType=TIMESTAMP}
		where MAIN_DEAL_ORDER_NO = #{mainDealOrderNo,jdbcType=VARCHAR}
		and TX_APP_FLAG = '0'
	</update>

  <!-- 合规订单信息 -->
  <resultMap id="ComplInfoDealResultMap" type="com.howbuy.tms.high.batch.dao.vo.ComplInfoDealVo" >
    <result column="TX_ACCT_NO" property="txAcctNo" jdbcType="VARCHAR" />
    <result column="DEAL_NO" property="dealNo" jdbcType="VARCHAR" />
    <result column="FUND_CODE" property="fundCode" jdbcType="VARCHAR" />
    <result column="SUBMIT_TA_DT" property="submitTaDt" jdbcType="VARCHAR" />
    <result column="FUND_NAME" property="fundName" jdbcType="VARCHAR" />
    <result column="APP_AMT" property="appAmt" jdbcType="DECIMAL" />
    <result column="M_BUSI_CODE" property="mBusiCode" jdbcType="CHAR" />
    <result column="CALM_DTM" property="calmDtm" jdbcType="TIMESTAMP" />
    <result column="FUND_RISK_LEVEL" property="fundRiskLevel" jdbcType="VARCHAR" />
    <result column="DUALENTRY_STATUS" property="dualentryStatus" jdbcType="VARCHAR" />
    <result column="CALLBACK_STATUS" property="callbackStatus" jdbcType="VARCHAR" />
    <result column="PMT_COMPLETE_DTM" property="pmtCompleteDtm" jdbcType="TIMESTAMP" />
    <result column="APP_DATE" property="appDate" jdbcType="VARCHAR" />
	<result column="CUST_RISK_LEVEL" property="custRiskLevel" jdbcType="VARCHAR" />
	<result column="QUALIFICATION_TYPE" property="qualificationType" jdbcType="VARCHAR" />
  </resultMap>

  <!-- 查询合规订单信息 -->
  <select id="selectComplInfoList" parameterType="map" resultMap="ComplInfoDealResultMap">
    SELECT  T.TX_ACCT_NO,
            T.DEAL_NO,
            T.FUND_CODE,
            T.SUBMIT_TA_DT,
            T.FUND_NAME,
            T.APP_AMT,
            T.M_BUSI_CODE,
            T.CALM_DTM,
			T.CUST_RISK_LEVEL,
			T.QUALIFICATION_TYPE,
            T.FUND_RISK_LEVEL,
            T.DUALENTRY_STATUS,
            T.CALLBACK_STATUS,
            P.PMT_COMPLETE_DTM,
            D.APP_DATE
    FROM HIGH_DEAL_ORDER_DTL T
    INNER JOIN DEAL_ORDER D  ON T.DEAL_NO = D.DEAL_NO
    INNER JOIN DEAL_ORDER_EXTEND E ON t.DEAL_NO = E.DEAL_NO
    INNER JOIN PAYMENT_ORDER P ON T.DEAL_NO = P.Deal_No
    <where>
      e.REC_STAT = '0'
      AND T.M_BUSI_CODE IN ('1122','1120')
      AND P.TX_PMT_FLAG in ('2', '11' , '17')
      AND T.CALLBACK_STATUS = '1' AND T.TX_APP_FLAG = '0'
      <if test="fundCode != null and fundCode !=''">
        AND T.FUND_CODE = #{fundCode, jdbcType=VARCHAR}
      </if>
      <if test="startDt != null and startDt !=''">
        AND D.APP_DATE <![CDATA[>=]]> #{startDt, jdbcType=VARCHAR}
      </if>
      <if test="endDt != null and endDt !=''">
        AND D.APP_DATE <![CDATA[<=]]> #{endDt, jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <!-- 查询所有需要更新通知状态的订单明细记录 付款状态2-付款成功 11-冻结成功 17-冻结支付成功 -->
  <select id="selectNeedRefreshNotifyOrder" parameterType="map" resultMap="BaseResultMap">
       SELECT T.*
		  FROM HIGH_DEAL_ORDER_DTL T
		 INNER JOIN (SELECT * FROM DEAL_ORDER_EXTEND WHERE REC_STAT = '0') D
		    ON T.DEAL_NO = D.DEAL_NO
		 INNER JOIN (SELECT * FROM PAYMENT_ORDER WHERE TX_PMT_FLAG in ('2', '11', '17')) P
		    ON T.DEAL_NO = P.DEAL_NO
		 WHERE T.M_BUSI_CODE IN ('1120', '1122')
		   AND T.NOTIFY_SUBMIT_FLAG = '0'
		   AND T.TX_APP_FLAG = '0'
		   <if test="now != null">
			   AND (T.RETRIEVE_DTM IS NULL OR T.RETRIEVE_DTM &lt;= #{now,jdbcType=TIMESTAMP})
		   </if>
  </select>

  <!-- 更新订单检索时间 -->
  <update id="updateRetrieveDtm" parameterType="map">
    UPDATE HIGH_DEAL_ORDER_DTL T
      SET T.RETRIEVE_DTM = #{retrieveDtm,jdbcType=TIMESTAMP},
          T.UPDATE_DTM = NOW()
    WHERE T.DEAL_DTL_NO = #{dealDtlNo, jdbcType=VARCHAR}
      AND T.UPDATE_DTM = #{oldUpdateDtm,jdbcType=TIMESTAMP}
    <if test="oldRetrieveDtm != null">
      AND T.RETRIEVE_DTM = #{oldRetrieveDtm,jdbcType=TIMESTAMP}
    </if>
  </update>

  <!-- 更新通知状态 -->
  <update id="updateNotifyStatus" parameterType="map">
  	UPDATE HIGH_DEAL_ORDER_DTL T
	   SET <if test="retrieveDtm != null">
	   		   T.RETRIEVE_DTM = #{retrieveDtm,jdbcType=TIMESTAMP},
	   	   </if>
	   	   <if test="notifyFlag != null and notifyFlag != '' ">
	   		   T.NOTIFY_SUBMIT_FLAG = #{notifyFlag, jdbcType=VARCHAR},
	   	   </if>
	   	   <if test="submitTaDt != null and submitTaDt != '' ">
		       T.SUBMIT_TA_DT = #{submitTaDt, jdbcType=VARCHAR},
	   	   </if>
	   	   <if test="assetStatus != null and assetStatus != '' ">
		       T.ASSETCERTIFICATE_STATUS = #{assetStatus, jdbcType=VARCHAR},
	   	   </if>
	       T.UPDATE_DTM = NOW()
	 WHERE T.DEAL_DTL_NO = #{dealDtlNo, jdbcType=VARCHAR}
	   <if test="oldNotifyFlag != null and oldNotifyFlag != '' ">
	   	   AND T.NOTIFY_SUBMIT_FLAG = #{oldNotifyFlag, jdbcType=VARCHAR}
	   </if>
	   <if test="oldSubmitTaDt != null and oldSubmitTaDt != '' ">
		   AND T.SUBMIT_TA_DT = #{oldSubmitTaDt, jdbcType=VARCHAR}
	   </if>
	   <if test="oldAssetStatus != null and oldAssetStatus != '' ">
		   AND T.ASSETCERTIFICATE_STATUS = #{oldAssetStatus, jdbcType=VARCHAR}
	   </if>
	   <if test="oldRetrieveDtm != null">
           AND T.RETRIEVE_DTM = #{oldRetrieveDtm,jdbcType=TIMESTAMP}
       </if>
	   AND T.UPDATE_DTM = #{oldUpdateDtm,jdbcType=TIMESTAMP}
  </update>

  <select id="queryListForTradeLimit" resultMap="BaseResultMap" parameterType="map" >
    select T.*
    FROM HIGH_DEAL_ORDER_DTL T
		INNER JOIN SIMU_FUND_CHECK_ORDER A ON T.DEAL_NO = A.DEAL_NO
		AND A.ACK_IMPORT_DT =  #{tradeDt,jdbcType=VARCHAR}
	    AND A.TA_CODE IN
	    	<foreach collection="taCodeList" item="item" index="index" open="(" close=")" separator=",">
	    		#{item}
	    	</foreach>
    where T.m_Busi_Code in ('1120','1122') and T.Tx_Ack_Flag = '5'
    UNION ALL
    select T.*
      FROM HIGH_DEAL_ORDER_DTL T
      INNER JOIN SIMU_FUND_CHECK_ORDER A ON T.DEAL_NO = A.DEAL_NO
        AND A.ACK_IMPORT_DT =  #{tradeDt,jdbcType=VARCHAR}
	    AND A.TA_CODE IN
	    	<foreach collection="taCodeList" item="item" index="index" open="(" close=")" separator=",">
	    		#{item}
	    	</foreach>
      WHERE T.m_Busi_Code IN ('1144','1134','1127') and T.Tx_Ack_Flag = '4'
    UNION ALL
	SELECT H.* FROM (select MAX(T.DEAL_DTL_NO) DEAL_DTL_NO,T.TX_ACCT_NO,T.FUND_CODE
	    FROM HIGH_DEAL_ORDER_DTL T
	    INNER JOIN SIMU_FUND_CHECK_ORDER A ON T.DEAL_NO = A.DEAL_NO
	    AND A.ACK_IMPORT_DT =  #{tradeDt,jdbcType=VARCHAR}
	      AND A.TA_CODE IN
	        <foreach collection="taCodeList" item="item" index="index" open="(" close=")" separator=",">
	          #{item}
	        </foreach>
	    WHERE T.m_Busi_Code IN ('1124','1142','1145','1135','1128') and T.Tx_Ack_Flag = '4'
	    GROUP BY T.TX_ACCT_NO,T.FUND_CODE) S
    	INNER JOIN HIGH_DEAL_ORDER_DTL H ON S.DEAL_DTL_NO = H.DEAL_DTL_NO
  </select>

  <update id="updateRedeemDirection" parameterType="map">
  	update HIGH_DEAL_ORDER_DTL
    set REDEEM_DIRECTION = #{redeemDirection,jdbcType=CHAR},
    	UPDATE_DTM = NOW()
    where DEAL_NO = #{dealNo,jdbcType=VARCHAR}
  </update>

  <select id="selectNotDualentryAndCallback" parameterType="map" resultMap="BaseResultMap">
     SELECT T.*
		FROM HIGH_DEAL_ORDER_DTL T
 			LEFT JOIN SIMU_FUND_CHECK_ORDER A ON T.DEAL_NO = A.DEAL_NO
	   WHERE ((T.DUALENTRY_STATUS = '1' AND T.DUALENTRY_INTERPOSE_FLAG = '1')
	      	OR (T.CALLBACK_STATUS = '1' AND T.CALLBACK_INTERPOSE_FLAG = '1'))
	      AND T.TX_APP_FLAG = '0'
	      AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}
	      AND (A.SUBMIT_APP_FLAG != '2' OR A.SUBMIT_APP_FLAG IS NULL)
          AND T.TA_CODE IN
          <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
    		#{item}
    	  </foreach>
  </select>

  <select id="selectNotPassCalm" parameterType="map" resultMap="BaseResultMap">
     SELECT T.*
		FROM HIGH_DEAL_ORDER_DTL T
 			LEFT JOIN SIMU_FUND_CHECK_ORDER A ON T.DEAL_NO = A.DEAL_NO
	   WHERE T.CALM_DTM <![CDATA[>]]> #{date,jdbcType=TIMESTAMP}
	      AND T.CALMDTM_INTERPOSE_FLAG = '1'
	      AND T.TX_APP_FLAG = '0'
	      AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}
	      AND (A.SUBMIT_APP_FLAG != '2' OR A.SUBMIT_APP_FLAG IS NULL)
	      AND T.TA_CODE IN
          <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
    		#{item}
    	  </foreach>
  </select>

  <select id="selectInvalidAssetcertificate" parameterType="map" resultMap="BaseResultMap">
     SELECT T.*
		FROM HIGH_DEAL_ORDER_DTL T
 			LEFT JOIN SIMU_FUND_CHECK_ORDER A ON T.DEAL_NO = A.DEAL_NO
	   WHERE T.ASSETCERTIFICATE_STATUS = '0'
	      AND T.ASSET_INTERPOSE_FLAG = '1'
	      AND T.TX_APP_FLAG = '0'
	      AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}
	      AND (A.SUBMIT_APP_FLAG != '2' OR A.SUBMIT_APP_FLAG IS NULL)
	      AND T.TA_CODE IN
          <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
    		#{item}
    	  </foreach>
  </select>

  <select id="countNotNeedNotify" parameterType="map" resultType="java.lang.Integer">
     SELECT count(1)
		FROM HIGH_DEAL_ORDER_DTL T, PAYMENT_ORDER P
	   WHERE T.NOTIFY_SUBMIT_FLAG = '0'
	      AND T.TX_APP_FLAG = '0'
	      AND P.TX_PMT_FLAG = '2'
	      AND (T.DUALENTRY_STATUS != '1' OR T.DUALENTRY_INTERPOSE_FLAG != '1')
	      AND (T.CALLBACK_STATUS != '1' OR T.CALLBACK_INTERPOSE_FLAG != '1')
	      AND (T.ASSETCERTIFICATE_STATUS = '1' OR T.ASSET_INTERPOSE_FLAG != '1')
	      AND (T.CALM_DTM > #{date,jdbcType=TIMESTAMP} OR T.CALMDTM_INTERPOSE_FLAG != '1')
	      AND T.M_BUSI_CODE IN ('1120','1122')
	      AND T.DEAL_NO = P.DEAL_NO
	      AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}
	      AND T.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
  </select>

  <select id="selectNotNeedNotify" parameterType="map" resultMap="BaseResultMap">
     SELECT T.*
		FROM HIGH_DEAL_ORDER_DTL T
		INNER JOIN PAYMENT_ORDER P ON T.DEAL_NO = P.DEAL_NO AND P.TX_PMT_FLAG = '2'
	   WHERE T.NOTIFY_SUBMIT_FLAG = '0'
	      AND T.TX_APP_FLAG = '0'
	      AND (T.DUALENTRY_STATUS != '1' OR T.DUALENTRY_INTERPOSE_FLAG != '1')
	      AND T.M_BUSI_CODE IN ('1120','1122')
	      AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}
	      AND T.TA_CODE IN
          <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
    		#{item}
    	  </foreach>
  </select>

  <select id="countNotNotifyOrReNotify" parameterType="map" resultType="java.lang.Integer">
     SELECT count(1)
		FROM HIGH_DEAL_ORDER_DTL T
	   WHERE T.NOTIFY_SUBMIT_FLAG in ('1','3')
	      AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}
	      AND T.TA_CODE IN
          <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
    		#{item}
    	  </foreach>
  </select>

  <select id="queryNotNotifyOrReNotify" parameterType="map" resultType="java.lang.String">
     SELECT T.TA_CODE
		FROM HIGH_DEAL_ORDER_DTL T
	   WHERE T.NOTIFY_SUBMIT_FLAG in ('1','3')
	      AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}
	      AND T.TA_CODE IN
          <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
    		#{item}
    	  </foreach>
  </select>

  <select id="selectUnNotifyOrReNotify" parameterType="map" resultMap="BaseResultMap">
     SELECT <include refid="com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
		FROM HIGH_DEAL_ORDER_DTL T
	   WHERE T.NOTIFY_SUBMIT_FLAG in ('1','3')
	      AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}
	      AND T.TA_CODE IN
          <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
    		#{item}
    	  </foreach>
  </select>

  <!-- 更新干预标识 -->
  <update id="updateInterposeFlag" parameterType="map">

      update HIGH_DEAL_ORDER_DTL T
      <set>
          <if test="dualentryInterposeFlag != null">
             T.DUALENTRY_INTERPOSE_FLAG = #{dualentryInterposeFlag,jdbcType=VARCHAR},
          </if>

          <if test="callbackInterposeFlag">
             T.CALLBACK_INTERPOSE_FLAG = #{callbackInterposeFlag,jdbcType=VARCHAR},
          </if>

          <if test="calmdtmInterposeFlag != null">
              T.CALMDTM_INTERPOSE_FLAG = #{calmdtmInterposeFlag,jdbcType=VARCHAR},
          </if>

          <if test="assetInterposeFlag != null">
             T.ASSET_INTERPOSE_FLAG = #{assetInterposeFlag,jdbcType=VARCHAR},
          </if>

           <if test="updateDtm != null">
             T.UPDATE_DTM = #{updateDtm,jdbcType=TIMESTAMP},
          </if>
      </set>
      where T.DEAL_DTL_NO = #{dealDtlNo, jdbcType=VARCHAR}
  </update>
  <resultMap id="HighDealDtlInterposeResultMap" type="com.howbuy.tms.high.batch.dao.vo.HighDealDtlInterposeVo">
    <id column="DEAL_DTL_NO" jdbcType="VARCHAR" property="dealDtlNo" />
    <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo" />
    <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo" />
    <result column="CUST_NAME" jdbcType="VARCHAR" property="custName" />
    <result column="ID_NO" jdbcType="VARCHAR" property="idNo" />
    <result column="ID_TYPE" jdbcType="VARCHAR" property="idType" />
    <result column="INVST_TYPE" jdbcType="VARCHAR" property="invstType" />
    <result column="M_BUSI_CODE" jdbcType="VARCHAR" property="mBusiCode" />
    <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
    <result column="FUND_NAME" jdbcType="VARCHAR" property="fundName" />
    <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt" />
    <result column="APP_VOL" jdbcType="DECIMAL" property="appVol" />
    <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt" />
    <result column="CALM_DTM" jdbcType="VARCHAR" property="calmDtm" />
    <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt" />
  	<result column="DUALENTRY_STATUS" jdbcType="VARCHAR" property="dualentryStatus"/>
    <result column="DUALENTRY_INTERPOSE_FLAG" jdbcType="VARCHAR" property="dualentryInterposeFlag"/>
    <result column="CALLBACK_STATUS" jdbcType="VARCHAR" property="callbackStatus"/>
    <result column="CALLBACK_INTERPOSE_FLAG" jdbcType="VARCHAR" property="callbackInterposeFlag"/>
    <result column="CALMDTM_INTERPOSE_FLAG" jdbcType="VARCHAR" property="calmdtmInterposeFlag"/>
    <result column="ASSETCERTIFICATE_STATUS" jdbcType="VARCHAR" property="assetcertificateStatus"/>
    <result column="ASSET_INTERPOSE_FLAG" jdbcType="VARCHAR" property="assetInterposeFlag"/>
  	<result column="TA_CODE" jdbcType="VARCHAR" property="taCode"/>
    <result column="PRODUCT_CHANNEL" jdbcType="VARCHAR" property="productChannel"/>
    <result column="NOTIFY_SUBMIT_FLAG" property="notifySubmitFlag" jdbcType="CHAR" />
    <result column="PMT_DT" property="pmtDt" jdbcType="VARCHAR" />
    <result column="TX_PMT_FLAG" property="txPmtFlag" jdbcType="VARCHAR" />
    <result column="MERGE_SUBMIT_FLAG" jdbcType="VARCHAR" property="mergeSubmitFlag" />
    <result column="MAIN_DEAL_ORDER_NO" jdbcType="VARCHAR" property="mainDealOrderNo" />

  </resultMap>
  <select id="selectHighDealDtlInterposeVo" parameterType="map" resultMap="HighDealDtlInterposeResultMap">
      SELECT
       t1.DEAL_NO,
       t1.DEAL_DTL_NO,
       t2.TX_ACCT_NO,
       t2.CUST_NAME,
       t2.ID_NO,
       t2.ID_TYPE,
       t2.INVST_TYPE,
       t1.M_BUSI_CODE,
       t1.FUND_CODE,
       t1.FUND_NAME,
       t1.APP_AMT,
       t1.APP_VOL,
       t1.TA_TRADE_DT,
       t1.CALM_DTM,
       t1.SUBMIT_TA_DT,
       T1.DUALENTRY_STATUS,
       T1.DUALENTRY_INTERPOSE_FLAG,
       T1.CALLBACK_STATUS,
       T1.CALLBACK_INTERPOSE_FLAG,
       T1.CALMDTM_INTERPOSE_FLAG,
       T1.ASSETCERTIFICATE_STATUS,
       T1.ASSET_INTERPOSE_FLAG,
       T2.ORDER_STATUS,
       T3.TX_PMT_FLAG,
       T4.SUBMIT_APP_FLAG,
       T1.TA_CODE,
       T1.PRODUCT_CHANNEL,
       T1.NOTIFY_SUBMIT_FLAG,
       T3.PMT_DT,
       t1.MERGE_SUBMIT_FLAG,
       t1.MAIN_DEAL_ORDER_NO
    FROM HIGH_DEAL_ORDER_DTL t1
     INNER JOIN DEAL_ORDER T2 ON T1.DEAL_NO = T2.DEAL_NO AND T2.ORDER_STATUS = '1'
     LEFT JOIN PAYMENT_ORDER t3 ON T1.DEAL_NO = T3.DEAL_NO
     LEFT JOIN SIMU_FUND_CHECK_ORDER T4 ON T1.DEAL_NO = T4.DEAL_NO
    <where>
        T1.PRODUCT_CHANNEL IN ('6','7')
        AND (T4.SUBMIT_APP_FLAG IS NULL OR T4.SUBMIT_APP_FLAG != '2')
        <if test="condition.interposeType == '1'.toString()">
          AND T1.SUBMIT_TA_DT = #{condition.taTradeDt,jdbcType=VARCHAR}
          AND T1.M_BUSI_CODE IN ('1120','1122')
        </if>
        <if test="condition.interposeType == '2'.toString()">
          AND T1.SUBMIT_TA_DT <![CDATA[ >= ]]> #{condition.taTradeDt,jdbcType=VARCHAR}
        </if>

       <if test="condition.interposeType == '3'.toString()">
          and t3.TX_PMT_FLAG  in ('9', '11')
       </if>

      <if test="condition.pmtDt != null">
        AND T3.PMT_DT <![CDATA[ >= ]]> #{condition.pmtDt,jdbcType=VARCHAR}
      </if>

        <if test="condition.txAcctNo != null">
          AND t1.TX_ACCT_NO = #{condition.txAcctNo,jdbcType=VARCHAR}
        </if>
        <if test="condition.taTradeDtStart != null">
          AND t1.TA_TRADE_DT <![CDATA[ >= ]]> #{condition.taTradeDtStart,jdbcType=VARCHAR}
        </if>
        <if test="condition.taTradeDtEnd != null">
          AND t1.TA_TRADE_DT <![CDATA[ <= ]]> #{condition.taTradeDtEnd,jdbcType=VARCHAR}
        </if>
        <if test="condition.submitTaDtStart != null">
          AND t1.SUBMIT_TA_DT <![CDATA[ >= ]]> #{condition.submitTaDtStart,jdbcType=VARCHAR}
        </if>
        <if test="condition.submitTaDtEnd != null">
          AND t1.SUBMIT_TA_DT <![CDATA[ <= ]]> #{condition.submitTaDtEnd,jdbcType=VARCHAR}
        </if>
        <if test="condition.dealNo != null">
          AND t1.DEAL_NO = #{condition.dealNo,jdbcType=VARCHAR}
        </if>
        <if test="condition.fundCode != null">
          AND t1.FUND_CODE = #{condition.fundCode,jdbcType=VARCHAR}
        </if>
        <if test="condition.mBusiCode != null">
          AND t1.M_BUSI_CODE = #{condition.mBusiCode,jdbcType=VARCHAR}
        </if>
      <if test="condition.taCodeList != null and condition.taCodeList.size()" >
         and t1.ta_Code in
         <foreach item="item" index="index" collection="condition.taCodeList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
        <if test='condition.mergeSubmitFlag == "0"'>
          AND (t1.MERGE_SUBMIT_FLAG != '1' or t1.MERGE_SUBMIT_FLAG is null)
        </if>
        <if test='condition.mergeSubmitFlag == "1"'>
          AND t1.MERGE_SUBMIT_FLAG = '1'
        </if>
      </where>
      order by T1.TA_TRADE_DT,T1.SUBMIT_TA_DT desc
  </select>

   <select id="selectAllNeedSyncSubmitDtDealList" parameterType="map" resultMap="BaseResultMap">

        SELECT  T.* FROM HIGH_DEAL_ORDER_DTL T
            LEFT JOIN DEAL_ORDER_EXTEND d ON t.DEAL_NO = d.DEAL_NO
        WHERE
           (d.REC_STAT = '0' OR d.Rec_Stat IS NULL)
           AND t.FUND_CODE = #{productCode, jdbcType=VARCHAR}
           AND t.SUBMIT_TA_DT = #{submitTaDt, jdbcType=VARCHAR}
           AND t.NOTIFY_SUBMIT_FLAG <![CDATA[<>]]> '2'
           AND t.M_BUSI_CODE = #{mBusiCode, jdbcType=VARCHAR}
           AND t.TX_APP_FLAG = '0'
   </select>

   <update id="updateSyncSubmitTaDt">
       UPDATE HIGH_DEAL_ORDER_DTL T
         SET T.SUBMIT_TA_DT = #{newSubmitTaDt, jdbcType=VARCHAR},
             T.UPDATE_DTM = #{newUpdateDtm,jdbcType=TIMESTAMP}
       WHERE
           T.DEAL_DTL_NO = #{dealDtlNo, jdbcType=VARCHAR}
           AND T.TX_APP_FLAG = '0'
           <if test="oldSubmitTaDt != null">
               AND T.SUBMIT_TA_DT = #{oldSubmitTaDt, jdbcType=VARCHAR}
           </if>

   </update>

   <update id="execSyncSubmitTaDtBatch" parameterType="map">
       UPDATE HIGH_DEAL_ORDER_DTL T
         SET T.SUBMIT_TA_DT = #{newSubmitTaDt, jdbcType=VARCHAR},
             T.UPDATE_DTM = #{newUpdateDtm,jdbcType=TIMESTAMP}
       WHERE T.TX_APP_FLAG = '0'
           <if test="dealNoList != null and  dealNoList.size >0">
           and  t.DEAL_NO in
		    <foreach collection="dealNoList" index="index" item="item" open="(" close=")" separator=",">
	             #{item,jdbcType=VARCHAR}
	        </foreach>
           </if>
           <if test="oldSubmitTaDt != null">
               AND T.SUBMIT_TA_DT = #{oldSubmitTaDt, jdbcType=VARCHAR}
           </if>
   </update>

  <update id="updateInterposeFlagByDealNo" parameterType="map">
      update HIGH_DEAL_ORDER_DTL T
      <set>
          <if test="dualentryInterposeFlag != null">
             T.DUALENTRY_INTERPOSE_FLAG = #{dualentryInterposeFlag,jdbcType=VARCHAR},
          </if>
          <if test="callbackInterposeFlag">
             T.CALLBACK_INTERPOSE_FLAG = #{callbackInterposeFlag,jdbcType=VARCHAR},
          </if>
          <if test="calmdtmInterposeFlag != null">
              T.CALMDTM_INTERPOSE_FLAG = #{calmdtmInterposeFlag,jdbcType=VARCHAR},
          </if>
          <if test="assetInterposeFlag != null">
             T.ASSET_INTERPOSE_FLAG = #{assetInterposeFlag,jdbcType=VARCHAR},
          </if>
          <if test="updateDtm != null">
             T.UPDATE_DTM = #{updateDtm,jdbcType=TIMESTAMP},
          </if>
          <if test="newSubmitTaDt != null">
             T.SUBMIT_TA_DT = #{newSubmitTaDt,jdbcType=TIMESTAMP},
          </if>
          <if test="submitTaDtInterposeFlag != null">
             T.SUBMITTADT_INTERPOSE_FLAG = #{submitTaDtInterposeFlag,jdbcType=TIMESTAMP},
          </if>
          <if test="retrieveDtm != null">
            T.RETRIEVE_DTM = #{retrieveDtm,jdbcType=TIMESTAMP},
          </if>
      </set>
      where T.DEAL_NO = #{dealNo, jdbcType=VARCHAR}
       	  <if test="oldSubmitTaDt != null">
             AND T.SUBMIT_TA_DT = #{oldSubmitTaDt,jdbcType=TIMESTAMP}
          </if>
  </update>

  <update id="updateComplInfo" parameterType="com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo" >
    update HIGH_DEAL_ORDER_DTL
    <set >
      <if test="updateDtm != null" >
          UPDATE_DTM = #{updateDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="dualentryStatus != null">
          DUALENTRY_STATUS = #{dualentryStatus, jdbcType=VARCHAR} ,
      </if>
      <if test="dualentryFinishDtm != null">
          DUALENTRY_FINISH_DTM =#{dualentryFinishDtm, jdbcType=TIMESTAMP} ,
      </if>
      <if test="callbackStatus != null">
          CALLBACK_STATUS = #{callbackStatus, jdbcType=VARCHAR},
      </if>
      <if test="callbackFinishDtm != null">
          CALLBACK_FINISH_DTM = #{callbackFinishDtm, jdbcType=TIMESTAMP},
      </if>
    </set>
    where DEAL_DTL_NO = #{dealDtlNo, jdbcType=VARCHAR}
  </update>

  <update id="updateMergeComplInfo" parameterType="com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo" >
    update HIGH_DEAL_ORDER_DTL
    <set >
      <if test="updateDtm != null" >
        UPDATE_DTM = #{updateDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="dualentryStatus != null">
        DUALENTRY_STATUS = #{dualentryStatus, jdbcType=VARCHAR} ,
      </if>
      <if test="dualentryFinishDtm != null">
        DUALENTRY_FINISH_DTM =#{dualentryFinishDtm, jdbcType=TIMESTAMP} ,
      </if>
      <if test="callbackStatus != null">
        CALLBACK_STATUS = #{callbackStatus, jdbcType=VARCHAR},
      </if>
      <if test="callbackFinishDtm != null">
        CALLBACK_FINISH_DTM = #{callbackFinishDtm, jdbcType=TIMESTAMP},
      </if>
    </set>
    where MAIN_DEAL_ORDER_NO = #{mainDealOrderNo, jdbcType=VARCHAR}
  </update>

  <update id="updateRefundDtByDealDtlNo" parameterType="map">
    update high_deal_order_dtl t
     set t.refund_dt = #{tradeDt, jdbcType=VARCHAR}
   where t.deal_dtl_no = #{dealDtlNo, jdbcType=VARCHAR}
  </update>

  <resultMap id="HighExpireRedeemMap" type="com.howbuy.tms.high.batch.dao.vo.HighExpireRedeemReportVo">
  	<result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo" />
    <result column="TA_CODE" jdbcType="VARCHAR" property="taCode" />
    <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
    <result column="FUND_NAME" jdbcType="VARCHAR" property="fundName" />
    <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo" />
    <result column="CUST_NAME" jdbcType="VARCHAR" property="custName" />
    <result column="ID_NO" jdbcType="VARCHAR" property="idNo" />
    <result column="TOTAL_VOL" jdbcType="DECIMAL" property="totalVol" />
    <result column="ACK_VOL" jdbcType="DECIMAL" property="ackVol" />
    <result column="IS_REDEEM_EXPIRE" jdbcType="VARCHAR" property="isRedeemExpire" />
    <result column="PRE_EXPIRE_DATE" jdbcType="VARCHAR" property="preExpireDate" />
    <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt" />
    <result column="APP_DTM" jdbcType="TIMESTAMP" property="appDtm" />
  </resultMap>
  <select id="selectHighExpireRedeemReportVo" parameterType="map" resultMap="HighExpireRedeemMap">
      SELECT T.DEAL_NO,
       T.TA_CODE,
       T.FUND_CODE,
       T.FUND_NAME,
       A.CUST_NAME,
       T.TX_ACCT_NO,
       A.ID_NO,
       B.TOTAL_VOL,
       T.ACK_VOL,
       T.IS_REDEEM_EXPIRE,
       T.PRE_EXPIRE_DATE,
       T.SUBMIT_TA_DT,
       A.APP_DTM
	  FROM HIGH_DEAL_ORDER_DTL T
	  INNER JOIN DEAL_ORDER A ON T.DEAL_NO = A.DEAL_NO
	  LEFT JOIN (SELECT T.TX_ACCT_NO, T.PRODUCT_CODE, SUM(T.BALANCE_VOL) AS TOTAL_VOL
	               FROM CUST_BOOKS T
	              GROUP BY T.TX_ACCT_NO, T.PRODUCT_CODE) B ON T.TX_ACCT_NO = B.TX_ACCT_NO AND T.FUND_CODE = B.PRODUCT_CODE
	  <where>
		    T.M_BUSI_CODE IN ('1120', '1122')
		   AND T.TX_ACK_FLAG in ('3','4')
		   AND B.TOTAL_VOL > 0
		   <if test="txAcctNo != null">
	          AND T.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
	       </if>
		   <if test="fundCode != null">
	          AND T.FUND_CODE = #{fundCode,jdbcType=VARCHAR}
	       </if>
	       <if test="fundName != null">
	          AND T.FUND_NAME like concat(concat('%',#{fundName,jdbcType=VARCHAR}),'%')
	       </if>
		   <if test="taCode != null">
	          AND T.TA_CODE = #{taCode,jdbcType=VARCHAR}
	       </if>
		   <if test="isRedeemExpire != null">
	          AND T.IS_REDEEM_EXPIRE =  #{isRedeemExpire,jdbcType=VARCHAR}
	       </if>
	       <if test="preExpireStartDate != null">
	          AND T.PRE_EXPIRE_DATE <![CDATA[ >= ]]> #{preExpireStartDate,jdbcType=VARCHAR}
	       </if>
	       <if test="preExpireEndDate != null">
	          AND T.PRE_EXPIRE_DATE <![CDATA[ <= ]]> #{preExpireEndDate,jdbcType=VARCHAR}
	       </if>
       </where>
      order by T.FUND_CODE
  </select>

  <update id="updateExpireInfo" parameterType="map">
    update high_deal_order_dtl t
     set t.IS_REDEEM_EXPIRE = #{isRedeemExpire, jdbcType=VARCHAR},
      t.pre_expire_date = #{preExpireDate, jdbcType=VARCHAR}
   where t.deal_no = #{dealNo, jdbcType=VARCHAR}
  </update>

  <select id="countNotSubmit" parameterType="map" resultType="java.lang.Integer">
     SELECT COUNT(1)
	  FROM HIGH_DEAL_ORDER_DTL T
	  LEFT JOIN SIMU_FUND_CHECK_ORDER A ON T.DEAL_NO = A.DEAL_NO
	 WHERE (A.SUBMIT_APP_FLAG IS NULL OR A.SUBMIT_APP_FLAG in ('1','3') OR (A.SUBMIT_APP_FLAG = '2' AND A.CANCEL_ORDER_SRC = '3'))
	   AND
	   (
	    (T.TX_APP_FLAG IN ('2','3') AND T.NOTIFY_SUBMIT_FLAG IN ('1', '2', '3'))
	   OR
	    T.TX_APP_FLAG = '0'
	   )
	   AND T.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
	   AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}
	   AND T.TA_CODE = #{taCode,jdbcType=VARCHAR}
	   AND T.M_BUSI_CODE NOT IN ('1366','1365')
  </select>

  <resultMap id="HighDealSubmitResultMap" type="com.howbuy.tms.high.batch.dao.vo.HighDealSubmitDtlVo">
    <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo" />
    <result column="DEAL_DTL_NO" jdbcType="VARCHAR" property="dealDtlNo" />
    <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo" />
    <result column="CUST_NAME" jdbcType="VARCHAR" property="custName" />
    <result column="BANK_ACCT" jdbcType="VARCHAR" property="bankAcct" />
    <result column="ID_NO" jdbcType="VARCHAR" property="idNo" />
    <result column="ID_TYPE" jdbcType="VARCHAR" property="idType" />
    <result column="INVST_TYPE" jdbcType="VARCHAR" property="invstType" />
    <result column="M_BUSI_CODE" jdbcType="CHAR" property="mBusiCode" />
    <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
    <result column="FUND_NAME" jdbcType="VARCHAR" property="fundName" />
    <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt" />
    <result column="APP_VOL" jdbcType="DECIMAL" property="appVol" />
    <result column="APP_DATE" jdbcType="VARCHAR" property="appDate" />
    <result column="APP_TIME" jdbcType="VARCHAR" property="appTime" />
    <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt" />
    <result column="TX_APP_FLAG" jdbcType="VARCHAR" property="txAppFlag" />
    <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt" />
    <result column="DUALENTRY_STATUS" jdbcType="VARCHAR" property="dualentryStatus"/>
    <result column="DUALENTRY_FINISH_DTM" jdbcType="TIMESTAMP" property="dualentryFinishDtm"/>
    <result column="DUALENTRY_INTERPOSE_FLAG" jdbcType="VARCHAR" property="dualentryInterposeFlag"/>
    <result column="CALLBACK_STATUS" jdbcType="VARCHAR" property="callbackStatus"/>
    <result column="CALLBACK_FINISH_DTM" jdbcType="TIMESTAMP" property="callbackFinishDtm"/>
    <result column="CALLBACK_INTERPOSE_FLAG" jdbcType="VARCHAR" property="callbackInterposeFlag"/>
    <result column="CALMDTM_INTERPOSE_FLAG" jdbcType="VARCHAR" property="calmdtmInterposeFlag"/>
    <result column="ASSETCERTIFICATE_STATUS" jdbcType="VARCHAR" property="assetcertificateStatus"/>
    <result column="ASSET_INTERPOSE_FLAG" jdbcType="VARCHAR" property="assetInterposeFlag"/>
    <result column="TA_CODE" jdbcType="VARCHAR" property="taCode" />
    <result column="PRODUCT_CHANNEL" jdbcType="VARCHAR" property="productChannel" />
    <result column="NOTIFY_SUBMIT_FLAG" jdbcType="CHAR" property="notifySubmitFlag" />
    <result column="SUBMIT_APP_FLAG" jdbcType="CHAR" property="submitAppFlag" />
    <result column="RET_DESC" jdbcType="VARCHAR" property="retDesc" />
    <result column="SUBMIT_DEAL_NO" jdbcType="VARCHAR" property="submitDealNo" />
  </resultMap>
  <select id="selectNotSubmitDtl" parameterType="map" resultMap="HighDealSubmitResultMap">
     SELECT T.DEAL_NO,
       T.DEAL_DTL_NO,
       T.TX_ACCT_NO,
       B.CUST_NAME,
       B.ID_NO,
       B.ID_TYPE,
       B.BANK_ACCT,
       B.INVST_TYPE,
       T.M_BUSI_CODE,
       T.FUND_CODE,
       T.FUND_NAME,
       T.APP_AMT,
       T.APP_VOL,
       B.APP_DATE,
       B.APP_TIME,
       T.TA_TRADE_DT,
       T.CALM_DTM,
       T.TX_APP_FLAG,
       T.SUBMIT_TA_DT,
       T.DUALENTRY_STATUS,
       T.DUALENTRY_FINISH_DTM,
       T.DUALENTRY_INTERPOSE_FLAG,
       T.CALLBACK_STATUS,
       T.CALLBACK_INTERPOSE_FLAG,
       T.CALLBACK_FINISH_DTM,
       T.CALMDTM_INTERPOSE_FLAG,
       T.ASSETCERTIFICATE_STATUS,
       T.ASSET_INTERPOSE_FLAG,
       T.TA_CODE,
       T.PRODUCT_CHANNEL,
       T.NOTIFY_SUBMIT_FLAG,
       A.SUBMIT_APP_FLAG,
       A.RET_DESC,
       A.SUBMIT_DEAL_NO
	  FROM HIGH_DEAL_ORDER_DTL T
	  LEFT JOIN SIMU_FUND_CHECK_ORDER A ON T.DEAL_NO = A.DEAL_NO
	  LEFT JOIN DEAL_ORDER B ON T.DEAL_NO = B.DEAL_NO
	 WHERE (A.SUBMIT_APP_FLAG IS NULL OR A.SUBMIT_APP_FLAG in ('1','3') OR (A.SUBMIT_APP_FLAG = '2' AND A.CANCEL_ORDER_SRC = '3'))
	   AND
	   (
	    (T.TX_APP_FLAG IN ('2','3') AND T.NOTIFY_SUBMIT_FLAG IN ('1', '2', '3'))
	   OR
	    T.TX_APP_FLAG = '0'
	   )
	   AND T.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
	   AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}
	   AND T.TA_CODE = #{taCode,jdbcType=VARCHAR}
	   AND T.PRODUCT_CHANNEL in ('6','7')
	   AND T.M_BUSI_CODE NOT IN ('1366','1365')
  </select>

  <select id="countPurchase" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo">
     SELECT ifnull(COUNT(DISTINCT T.TX_ACCT_NO),0) AS purPersonCount,
			ifnull(COUNT(1),0) as purOrderCount,
       SUM(ifnull(T.APP_AMT,0)) as purAmtCount,
       SUM(ifnull(T.APP_AMT,0) - ifnull(T.FEE,0)) as purNetAmtCount,
       SUM(ifnull(T.FEE,0)) as purFeeCount,
			ifnull(COUNT(DISTINCT T.TX_ACCT_NO),0) AS purPersonCount
	  FROM HIGH_DEAL_ORDER_DTL T
	  LEFT JOIN PAYMENT_ORDER P ON T.DEAL_NO = P.DEAL_NO
	 WHERE T.TX_APP_FLAG = '0'
	   AND T.M_BUSI_CODE in ('1122','1120')
	   AND P.TX_PMT_FLAG in ('2', '11', '17')
	   AND T.FUND_CODE = #{fundCode,jdbcType=VARCHAR}
	   AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}

  </select>

  <select id="countPurchasePeoples" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo">
	   SELECT ifnull(COUNT(DISTINCT T.TX_ACCT_NO),0) AS purPersonCount
	  FROM HIGH_DEAL_ORDER_DTL T
	  LEFT JOIN PAYMENT_ORDER P ON T.DEAL_NO = P.DEAL_NO
	 WHERE T.TX_APP_FLAG = '0'
	   AND T.M_BUSI_CODE = '1122'
	   AND P.TX_PMT_FLAG = '2'
	   AND T.FUND_CODE = #{fundCode,jdbcType=VARCHAR}
	   AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}
  </select>

  <select id="countRedeemPeoples" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo">
	    SELECT ifnull(COUNT(DISTINCT T.TX_ACCT_NO),0) as redeemPersonCount
	       FROM HIGH_DEAL_ORDER_DTL T
	    WHERE T.TX_APP_FLAG = '0'
	      AND T.M_BUSI_CODE = '1124'
	      AND T.FUND_CODE = #{fundCode,jdbcType=VARCHAR}
	      AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}
  </select>


  <select id="selectPurchase" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.AppointmentDetailVo">
	  SELECT P.CUST_NAME as custName,
	       T.M_BUSI_CODE as businessType,
	       CONCAT(T.FUND_CODE, '-',T.FUND_NAME)  as productName,
	  ifnull(T.APP_AMT,0) as appAmt,
	  ifnull(T.APP_AMT,0) - ifnull(T.FEE,0) as appNetAmt,
	  ifnull(T.FEE,0) as fee,
	       T.SUBMIT_TA_DT as submitTaDate,
	       T.TX_ACCT_NO as txAcctNo
	  FROM HIGH_DEAL_ORDER_DTL T
	  LEFT JOIN PAYMENT_ORDER P ON T.DEAL_NO = P.DEAL_NO
	 WHERE T.TX_APP_FLAG = '0'
	   AND T.M_BUSI_CODE in('1122', '1120')
	   AND P.TX_PMT_FLAG in ('2' , '11', '17')
	   AND T.FUND_CODE = #{fundCode,jdbcType=VARCHAR}
	   AND T.SUBMIT_TA_DT in
	   <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
	    		#{item}
	    	</foreach>
  </select>

  <select id="countRedeem" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo">
	 SELECT ifnull(COUNT(1),0) as redeemOrderCount, SUM(ifnull(T.APP_VOL,0)) as redeemVolCount,
			ifnull(COUNT(DISTINCT T.TX_ACCT_NO),0) as redeemPersonCount
	  FROM HIGH_DEAL_ORDER_DTL T
	 WHERE T.TX_APP_FLAG = '0'
	   AND T.M_BUSI_CODE = '1124'
	   AND T.FUND_CODE = #{fundCode,jdbcType=VARCHAR}
	   AND T.SUBMIT_TA_DT = #{submitTaDt,jdbcType=VARCHAR}
  </select>

  <select id="selectRedeem" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.AppointmentDetailVo">
	  SELECT A.CUST_NAME AS custName,
	       T.M_BUSI_CODE AS businessType,
	       CONCAT(T.FUND_CODE, '-',T.FUND_NAME)  as productName,
	       ifnull(T.APP_VOL,0) AS appVol,
	       T.SUBMIT_TA_DT as submitTaDate,
	       T.TX_ACCT_NO as txAcctNo
		  FROM HIGH_DEAL_ORDER_DTL T
		    INNER JOIN DEAL_ORDER A ON T.DEAL_NO = A.DEAL_NO
		 WHERE T.TX_APP_FLAG = '0'
		   AND T.M_BUSI_CODE = '1124'
		   AND T.FUND_CODE = #{fundCode,jdbcType=VARCHAR}
		   AND T.SUBMIT_TA_DT in
		   <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
		    		#{item}
		    	</foreach>
	</select>

  <select id="selectNotDualentryForReport" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.HighTradeReportCheckVo">
       SELECT T.DEAL_NO as dealNo,
	       T.FUND_CODE as fundCode,
	       B.APP_DATE as appDt,
           B.APP_TIME as appTime,
           B.TX_ACCT_NO as txAcctNo,
           B.CUST_NAME as custName,
           B.INVST_TYPE as investType,
           B.ID_TYPE as idType,
           B.ID_NO as idNo,
	       T.M_BUSI_CODE as mBusiCode,
	       T.FUND_NAME as fundName,
	       T.APP_AMT as appAmt,
	       T.FEE as fee,
	       T.TA_CODE as taInfo,
	       T.SUBMIT_TA_DT as submitTaDt,
	       T.DUALENTRY_STATUS as dualEntryStatus,
	       T.DUALENTRY_INTERPOSE_FLAG as dualEntryFlag,
	       T.DUALENTRY_FINISH_DTM as dualEntryDtm,
	       p.pay_deadline_dtm as payDeadlineDtm
		FROM HIGH_DEAL_ORDER_DTL T
			INNER JOIN PAYMENT_ORDER P ON T.DEAL_NO = P.DEAL_NO
 			LEFT JOIN SIMU_FUND_CHECK_ORDER A ON T.DEAL_NO = A.DEAL_NO
 			INNER JOIN DEAL_ORDER B ON T.DEAL_NO = B.DEAL_NO
	   WHERE T.DUALENTRY_STATUS = '1'
	   	  AND T.TX_APP_FLAG = '0'
	   	  AND T.DUALENTRY_INTERPOSE_FLAG = '1'
	      AND T.M_BUSI_CODE IN ('1120','1122')
	      AND T.PRODUCT_CHANNEL IN ('6','7')
          and P.TX_PMT_FLAG in ('2', '11', '17')
	      AND (A.SUBMIT_APP_FLAG != '2' OR A.SUBMIT_APP_FLAG IS NULL)
          <if test="fundCodeList != null and fundCodeList.size() > 0">
            AND T.FUND_CODE IN
            <foreach collection="fundCodeList" index="index" item="item" open="(" close=")" separator=",">
              #{item,jdbcType=VARCHAR}
            </foreach>
          </if>
	      <if test="txAcctNo != null and txAcctNo != '' ">
	    	AND T.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
    	  </if>
    	  <if test="submitTaStartDt != null and submitTaStartDt != '' ">
	    	AND T.SUBMIT_TA_DT <![CDATA[ >= ]]> #{submitTaStartDt,jdbcType=VARCHAR}
    	  </if>
    	  <if test="submitTaEndDt != null and submitTaEndDt != '' ">
	    	AND T.SUBMIT_TA_DT <![CDATA[ <= ]]> #{submitTaEndDt,jdbcType=VARCHAR}
    	  </if>
    	  <if test="payDeadLineDt != null and payDeadLineDt != '' ">
	    	AND DATE_FORMAT(P.PAY_DEADLINE_DTM, '%Y%m%d') = #{payDeadLineDt,jdbcType=VARCHAR}
    	  </if>
          <if test="payDeadLineEndDt != null and payDeadLineEndDt != '' ">
            AND DATE_FORMAT(P.PAY_DEADLINE_DTM, '%Y%m%d')  <![CDATA[ <= ]]> #{payDeadLineEndDt,jdbcType=VARCHAR}
          </if>
    	  <if test="taCode != null and taCode != '' ">
	    	AND T.TA_CODE = #{taCode,jdbcType=VARCHAR}
    	  </if>
  </select>

  <select id="selectNotCallbackForReport" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.HighTradeReportCheckVo">
     SELECT T.DEAL_NO as dealNo,
	       T.FUND_CODE as fundCode,
	       B.APP_DATE as appDt,
           B.APP_TIME as appTime,
           B.TX_ACCT_NO as txAcctNo,
           B.CUST_NAME as custName,
           B.INVST_TYPE as investType,
           B.ID_TYPE as idType,
           B.ID_NO as idNo,
	       T.M_BUSI_CODE as mBusiCode,
	       T.FUND_NAME as fundName,
	       T.APP_AMT as appAmt,
	       T.FEE as fee,
	       T.TA_CODE as taInfo,
	       T.SUBMIT_TA_DT as submitTaDt,
	       T.CALLBACK_STATUS as callBackStatus,
	       T.CALLBACK_INTERPOSE_FLAG as callBackFlag,
	       T.CALLBACK_FINISH_DTM as callBackDtm,
	       p.pay_deadline_dtm as payDeadlineDtm
		FROM HIGH_DEAL_ORDER_DTL T
			INNER JOIN PAYMENT_ORDER P ON T.DEAL_NO = P.DEAL_NO
 			LEFT JOIN SIMU_FUND_CHECK_ORDER A ON T.DEAL_NO = A.DEAL_NO
 			INNER JOIN DEAL_ORDER B ON T.DEAL_NO = B.DEAL_NO
	   WHERE T.CALLBACK_STATUS = '1' AND T.CALLBACK_INTERPOSE_FLAG = '1'
	      AND T.M_BUSI_CODE IN ('1120','1122')
	      AND T.PRODUCT_CHANNEL IN ('6','7')
	      AND T.TX_APP_FLAG = '0'
	      AND (A.SUBMIT_APP_FLAG != '2' OR A.SUBMIT_APP_FLAG IS NULL)
          <if test="fundCodeList != null and fundCodeList.size() > 0">
            AND T.FUND_CODE IN
            <foreach collection="fundCodeList" index="index" item="item" open="(" close=")" separator=",">
              #{item,jdbcType=VARCHAR}
            </foreach>
          </if>
	      <if test="txAcctNo != null and txAcctNo != '' ">
	    	AND T.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
    	  </if>
    	  <if test="submitTaStartDt != null and submitTaStartDt != '' ">
	    	AND T.SUBMIT_TA_DT <![CDATA[ >= ]]> #{submitTaStartDt,jdbcType=VARCHAR}
    	  </if>
    	  <if test="submitTaEndDt != null and submitTaEndDt != '' ">
	    	AND T.SUBMIT_TA_DT <![CDATA[ <= ]]> #{submitTaEndDt,jdbcType=VARCHAR}
    	  </if>
    	  <if test="payDeadLineDt != null and payDeadLineDt != '' ">
	    	AND DATE_FORMAT(P.PAY_DEADLINE_DTM, '%Y%m%d') = #{payDeadLineDt,jdbcType=VARCHAR}
    	  </if>
    	  <if test="taCode != null and taCode != '' ">
	    	AND T.TA_CODE = #{taCode,jdbcType=VARCHAR}
    	  </if>
  </select>

  <select id="selectNotPassCalmForReport" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.HighTradeReportCheckVo">
     SELECT T.DEAL_NO as dealNo,
	     T.FUND_CODE as fundCode,
	     B.APP_DATE as appDt,
         B.APP_TIME as appTime,
         B.TX_ACCT_NO as txAcctNo,
         B.CUST_NAME as custName,
         B.INVST_TYPE as investType,
         B.ID_TYPE as idType,
         B.ID_NO as idNo,
         T.M_BUSI_CODE as mBusiCode,
         T.FUND_NAME as fundName,
         T.APP_AMT as appAmt,
         T.FEE as fee,
         T.TA_CODE as taInfo,
         T.SUBMIT_TA_DT as submitTaDt,
         T.CALM_DTM as calmDtm,
         T.CALMDTM_INTERPOSE_FLAG as calmFlag,
	     p.pay_deadline_dtm as payDeadlineDtm
     FROM HIGH_DEAL_ORDER_DTL T
        INNER JOIN PAYMENT_ORDER P ON T.DEAL_NO = P.DEAL_NO
        LEFT JOIN SIMU_FUND_CHECK_ORDER A ON T.DEAL_NO = A.DEAL_NO
        INNER JOIN DEAL_ORDER B ON T.DEAL_NO = B.DEAL_NO
	   WHERE T.CALM_DTM <![CDATA[>]]> #{date,jdbcType=TIMESTAMP}
	      AND T.CALMDTM_INTERPOSE_FLAG = '1'
	      AND T.TX_APP_FLAG = '0'
	      AND T.M_BUSI_CODE IN ('1120','1122')
	      AND T.PRODUCT_CHANNEL IN ('6','7')
	      AND (A.SUBMIT_APP_FLAG != '2' OR A.SUBMIT_APP_FLAG IS NULL)
          <if test="fundCodeList != null and fundCodeList.size() > 0">
            AND T.FUND_CODE IN
            <foreach collection="fundCodeList" index="index" item="item" open="(" close=")" separator=",">
              #{item,jdbcType=VARCHAR}
            </foreach>
          </if>
	      <if test="txAcctNo != null and txAcctNo != '' ">
	    	AND T.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
    	  </if>
    	  <if test="submitTaStartDt != null and submitTaStartDt != '' ">
	    	AND T.SUBMIT_TA_DT <![CDATA[ >= ]]> #{submitTaStartDt,jdbcType=VARCHAR}
    	  </if>
    	  <if test="submitTaEndDt != null and submitTaEndDt != '' ">
	    	AND T.SUBMIT_TA_DT <![CDATA[ <= ]]> #{submitTaEndDt,jdbcType=VARCHAR}
    	  </if>
    	  <if test="payDeadLineDt != null and payDeadLineDt != '' ">
	    	AND  DATE_FORMAT(P.PAY_DEADLINE_DTM, '%Y%m%d') = #{payDeadLineDt,jdbcType=VARCHAR}
    	  </if>
    	  <if test="taCode != null and taCode != '' ">
	    	AND T.TA_CODE = #{taCode,jdbcType=VARCHAR}
    	  </if>
  </select>

  <select id="selectInvalidAssetcertificateForReport" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.HighTradeReportCheckVo">
     SELECT T.DEAL_NO as dealNo,
	       T.FUND_CODE as fundCode,
	       B.APP_DATE as appDt,
           B.APP_TIME as appTime,
           B.TX_ACCT_NO as txAcctNo,
           B.CUST_NAME as custName,
           B.INVST_TYPE as investType,
           B.ID_TYPE as idType,
           B.ID_NO as idNo,
	       T.M_BUSI_CODE as mBusiCode,
	       T.FUND_NAME as fundName,
	       T.APP_AMT as appAmt,
	       T.FEE as fee,
	       T.TA_CODE as taInfo,
	       T.SUBMIT_TA_DT as submitTaDt,
	       T.ASSETCERTIFICATE_STATUS as assetStatus,
	       T.ASSET_INTERPOSE_FLAG as assetFlag,
	       p.pay_deadline_dtm as payDeadlineDtm
		FROM HIGH_DEAL_ORDER_DTL T
			INNER JOIN PAYMENT_ORDER P ON T.DEAL_NO = P.DEAL_NO
 			LEFT JOIN SIMU_FUND_CHECK_ORDER A ON T.DEAL_NO = A.DEAL_NO
 			INNER JOIN DEAL_ORDER B ON T.DEAL_NO = B.DEAL_NO
	   WHERE T.ASSETCERTIFICATE_STATUS = '0'
	      AND T.ASSET_INTERPOSE_FLAG = '1'
	      AND T.TX_APP_FLAG = '0'
	      AND T.M_BUSI_CODE IN ('1120','1122')
	      AND T.PRODUCT_CHANNEL IN ('6','7')
          and P.TX_PMT_FLAG in ('2', '11', '17')
	      AND (A.SUBMIT_APP_FLAG != '2' OR A.SUBMIT_APP_FLAG IS NULL)
          <if test="fundCodeList != null and fundCodeList.size() > 0">
            AND T.FUND_CODE IN
            <foreach collection="fundCodeList" index="index" item="item" open="(" close=")" separator=",">
              #{item,jdbcType=VARCHAR}
            </foreach>
          </if>
	      <if test="txAcctNo != null and txAcctNo != '' ">
	    	AND T.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
    	  </if>
    	  <if test="submitTaStartDt != null and submitTaStartDt != '' ">
	    	AND T.SUBMIT_TA_DT <![CDATA[ >= ]]> #{submitTaStartDt,jdbcType=VARCHAR}
    	  </if>
    	  <if test="submitTaEndDt != null and submitTaEndDt != '' ">
	    	AND T.SUBMIT_TA_DT <![CDATA[ <= ]]> #{submitTaEndDt,jdbcType=VARCHAR}
    	  </if>
    	  <if test="payDeadLineDt != null and payDeadLineDt != '' ">
	    	AND DATE_FORMAT(P.PAY_DEADLINE_DTM, '%Y%m%d') = #{payDeadLineDt,jdbcType=VARCHAR}
    	  </if>
          <if test="payDeadLineEndDt != null and payDeadLineEndDt != '' ">
            AND DATE_FORMAT(P.PAY_DEADLINE_DTM, '%Y%m%d') <![CDATA[ <= ]]> #{payDeadLineEndDt,jdbcType=VARCHAR}
          </if>
    	  <if test="taCode != null and taCode != '' ">
	    	AND T.TA_CODE = #{taCode,jdbcType=VARCHAR}
    	  </if>

  </select>

  <select id="selectPartRedeemReport" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.HighRedeemVo">
     SELECT P.SUBMIT_TA_DT as submitTaDt,
       P.TX_ACCT_NO   as txAcctNo,
       P.CUST_NAME    as custName,
       P.INVST_TYPE   as investType,
       P.ID_TYPE      as idType,
       P.ID_NO        as idNo,
       P.TA_CODE      as taCode,
       P.FUND_CODE    as fundCode,
       P.FUND_NAME    as fundName,
       P.APP_VOL      as appVol,
       Q.BALANCE_VOL  AS balanceVol
    FROM (SELECT T.SUBMIT_TA_DT,
	               T.TX_ACCT_NO,
	               A.CUST_NAME,
	               A.INVST_TYPE,
	               A.ID_TYPE,
	               A.ID_NO,
	               T.TA_CODE,
	               T.FUND_CODE,
	               T.FUND_NAME,
	               T.PRODUCT_CHANNEL,
	               SUM(T.APP_VOL) AS APP_VOL
	          FROM HIGH_DEAL_ORDER_DTL T
	         INNER JOIN DEAL_ORDER A ON T.DEAL_NO = A.DEAL_NO
	         WHERE T.M_BUSI_CODE = '1124'
	           AND T.TX_APP_FLAG = '0'
	           AND T.PRODUCT_CHANNEL IN ('6', '7')
	         GROUP BY T.SUBMIT_TA_DT,
	                  T.TX_ACCT_NO,
	                  A.CUST_NAME,
	                  A.INVST_TYPE,
	                  A.ID_TYPE,
	                  A.ID_NO,
	                  T.TA_CODE,
	                  T.FUND_CODE,
	                  T.FUND_NAME,
	                  T.PRODUCT_CHANNEL
	         ORDER BY T.SUBMIT_TA_DT) P
	 INNER JOIN (SELECT TX_ACCT_NO, PRODUCT_CODE, SUM(BALANCE_VOL) AS BALANCE_VOL
	               FROM (SELECT T.TX_ACCT_NO,
	                            T.PRODUCT_CODE,
	                            SUM((ifnull(T.BALANCE_VOL, 0) + ifnull(T.UNCONFIRMED_VOL, 0) +
	  							ifnull(T.JUST_FRZN_VOL, 0))) AS BALANCE_VOL
	                       FROM CUST_BOOKS T
	                      WHERE T.PRODUCT_CHANNEL IN ('6', '7')
	                      GROUP BY T.TX_ACCT_NO, T.PRODUCT_CODE
	                     UNION ALL
	                     SELECT T.TX_ACCT_NO, T.PRODUCT_CODE, SUM(T.APP_VOL) as BALANCE_VOL
	                       FROM CUST_BOOKS_DTL T
	                      WHERE T.CHANGE_BUSI_CODE IN ('AB024', 'AB324')
	                        AND T.PRODUCT_CHANNEL IN ('6', '7')
	                      GROUP BY T.TX_ACCT_NO, T.PRODUCT_CODE) hh
	               GROUP BY TX_ACCT_NO, PRODUCT_CODE) Q ON P.TX_ACCT_NO = Q.TX_ACCT_NO
	                                      AND P.FUND_CODE = Q.PRODUCT_CODE
	   WHERE 1=1
	   	  <if test="taCode != null and taCode != '' ">
	    	AND P.TA_CODE = #{taCode,jdbcType=VARCHAR}
    	  </if>
	      <if test="fundCode != null and fundCode != '' ">
	    	AND P.FUND_CODE = #{fundCode,jdbcType=VARCHAR}
    	  </if>
    	  <if test="productChannel != null and productChannel != '' ">
	    	AND P.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
    	  </if>
	      <if test="txAcctNo != null and txAcctNo != '' ">
	    	AND P.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
    	  </if>
    	  <if test="submitTaStartDt != null and submitTaStartDt != '' ">
	    	AND P.SUBMIT_TA_DT <![CDATA[ >= ]]> #{submitTaStartDt,jdbcType=VARCHAR}
    	  </if>
    	  <if test="submitTaEndDt != null and submitTaEndDt != '' ">
	    	AND P.SUBMIT_TA_DT <![CDATA[ <= ]]> #{submitTaEndDt,jdbcType=VARCHAR}
    	  </if>
  </select>

  <select id="selectPartRedeemReportList" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.HighRedeemVo">
    SELECT P.SUBMIT_TA_DT as submitTaDt,
    P.TX_ACCT_NO   as txAcctNo,
    P.CUST_NAME    as custName,
    P.INVST_TYPE   as investType,
    P.ID_TYPE      as idType,
    P.ID_NO        as idNo,
    P.TA_CODE      as taCode,
    P.FUND_CODE    as fundCode,
    P.FUND_NAME    as fundName,
    P.APP_VOL      as appVol,
    Q.BALANCE_VOL  AS balanceVol
    FROM (SELECT T.SUBMIT_TA_DT,
    T.TX_ACCT_NO,
    A.CUST_NAME,
    A.INVST_TYPE,
    A.ID_TYPE,
    A.ID_NO,
    T.TA_CODE,
    T.FUND_CODE,
    T.FUND_NAME,
    T.PRODUCT_CHANNEL,
    SUM(T.APP_VOL) AS APP_VOL
    FROM HIGH_DEAL_ORDER_DTL T
    INNER JOIN DEAL_ORDER A ON T.DEAL_NO = A.DEAL_NO
    WHERE T.M_BUSI_CODE = '1124'
    AND T.TX_APP_FLAG = '0'
    AND T.PRODUCT_CHANNEL IN ('6', '7')
    <if test="disCode != null and disCode != '' ">
      AND T.DIS_CODE = #{disCode,jdbcType=VARCHAR}
    </if>
    GROUP BY T.SUBMIT_TA_DT,
    T.TX_ACCT_NO,
    A.CUST_NAME,
    A.INVST_TYPE,
    A.ID_TYPE,
    A.ID_NO,
    T.TA_CODE,
    T.FUND_CODE,
    T.FUND_NAME,
    T.PRODUCT_CHANNEL
    ORDER BY T.SUBMIT_TA_DT) P
    INNER JOIN (SELECT TX_ACCT_NO, PRODUCT_CODE, SUM(BALANCE_VOL) AS BALANCE_VOL
    FROM (SELECT T.TX_ACCT_NO,
    T.PRODUCT_CODE,
    SUM((T.BALANCE_VOL + T.UNCONFIRMED_VOL +
    T.JUST_FRZN_VOL)) AS BALANCE_VOL
    FROM CUST_BOOKS T
    WHERE T.PRODUCT_CHANNEL IN ('6', '7')
    <if test="disCode != null and disCode != '' ">
      AND T.DIS_CODE = #{disCode,jdbcType=VARCHAR}
    </if>
    GROUP BY T.TX_ACCT_NO, T.PRODUCT_CODE
    UNION ALL
    SELECT T.TX_ACCT_NO, T.PRODUCT_CODE, SUM(T.APP_VOL) as BALANCE_VOL
    FROM CUST_BOOKS_DTL T
    WHERE T.CHANGE_BUSI_CODE IN ('AB024', 'AB324')
    AND T.PRODUCT_CHANNEL IN ('6', '7')
    <if test="disCode != null and disCode != '' ">
      AND T.DIS_CODE = #{disCode,jdbcType=VARCHAR}
    </if>
    GROUP BY T.TX_ACCT_NO, T.PRODUCT_CODE) H
    GROUP BY TX_ACCT_NO, PRODUCT_CODE) Q ON P.TX_ACCT_NO = Q.TX_ACCT_NO
    AND P.FUND_CODE = Q.PRODUCT_CODE
    WHERE 1=1
    <if test="taCodeList != null and taCodeList.size()>0 ">
      AND P.TA_CODE in
      <foreach collection="taCodeList" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
    <if test="fundCodeList != null and fundCodeList.size()>0 ">
    AND P.FUND_CODE in
    <foreach collection="fundCodeList" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
    </if>
    <if test="productChannel != null and productChannel != '' ">
      AND P.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
    </if>
    <if test="txAcctNo != null and txAcctNo != '' ">
      AND P.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
    </if>
    <if test="submitTaStartDt != null and submitTaStartDt != '' ">
      AND P.SUBMIT_TA_DT <![CDATA[ >= ]]> #{submitTaStartDt,jdbcType=VARCHAR}
    </if>
    <if test="submitTaEndDt != null and submitTaEndDt != '' ">
      AND P.SUBMIT_TA_DT <![CDATA[ <= ]]> #{submitTaEndDt,jdbcType=VARCHAR}
    </if>
  </select>



  <select id="selectLargeRedeemReport" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.HighRedeemVo">
     SELECT P.SUBMIT_TA_DT as submitTaDt,
       P.TA_CODE      as taCode,
       P.FUND_CODE    as fundCode,
       P.APP_VOL      as appVol,
       Q.BALANCE_VOL  AS balanceVol
	  FROM (SELECT T.SUBMIT_TA_DT,
	               T.TA_CODE,
	               T.FUND_CODE,
	               T.PRODUCT_CHANNEL,
	               SUM(T.APP_VOL) AS APP_VOL
	          FROM HIGH_DEAL_ORDER_DTL T
	         WHERE T.M_BUSI_CODE = '1124'
	           AND T.TX_APP_FLAG = '0'
	           AND T.PRODUCT_CHANNEL IN ('6', '7')
	         GROUP BY T.SUBMIT_TA_DT, T.TA_CODE, T.FUND_CODE, T.PRODUCT_CHANNEL
	         ORDER BY T.SUBMIT_TA_DT DESC) P
	 INNER JOIN (SELECT T.PRODUCT_CODE, SUM(T.BALANCE_VOL) as BALANCE_VOL
	               FROM CUST_BOOKS T
	              WHERE T.PRODUCT_CHANNEL IN ('6', '7')
	              GROUP BY T.PRODUCT_CODE) Q ON P.FUND_CODE = Q.PRODUCT_CODE
	   WHERE 1=1
	   	  <if test="taCode != null and taCode != '' ">
	    	AND P.TA_CODE = #{taCode,jdbcType=VARCHAR}
    	  </if>
	      <if test="fundCode != null and fundCode != '' ">
	    	AND P.FUND_CODE = #{fundCode,jdbcType=VARCHAR}
    	  </if>
    	  <if test="productChannel != null and productChannel != '' ">
	    	AND P.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
    	  </if>
    	  <if test="submitTaStartDt != null and submitTaStartDt != '' ">
	    	AND P.SUBMIT_TA_DT <![CDATA[ >= ]]> #{submitTaStartDt,jdbcType=VARCHAR}
    	  </if>
    	  <if test="submitTaEndDt != null and submitTaEndDt != '' ">
	    	AND P.SUBMIT_TA_DT <![CDATA[ <= ]]> #{submitTaEndDt,jdbcType=VARCHAR}
    	  </if>
  </select>

  <select id="selectLargeRedeemReportList" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.HighRedeemVo">
    SELECT P.SUBMIT_TA_DT as submitTaDt,
    P.TA_CODE      as taCode,
    P.FUND_CODE    as fundCode,
    P.FUND_NAME    as fundName,
    P.APP_VOL      as appVol,
    Q.BALANCE_VOL  AS balanceVol
    FROM (SELECT T.SUBMIT_TA_DT,
    T.TA_CODE,
    T.FUND_CODE,
    T.FUND_NAME,
    T.PRODUCT_CHANNEL,
    SUM(T.APP_VOL) AS APP_VOL
    FROM HIGH_DEAL_ORDER_DTL T
    WHERE T.M_BUSI_CODE = '1124'
    AND T.TX_APP_FLAG = '0'
    AND T.PRODUCT_CHANNEL IN ('6', '7')
    GROUP BY T.SUBMIT_TA_DT, T.TA_CODE, T.FUND_CODE, T.FUND_NAME, T.PRODUCT_CHANNEL
    ORDER BY T.SUBMIT_TA_DT DESC) P
    INNER JOIN (SELECT T.PRODUCT_CODE, SUM(T.BALANCE_VOL) as BALANCE_VOL
    FROM CUST_BOOKS T
    WHERE T.PRODUCT_CHANNEL IN ('6', '7')
    GROUP BY T.PRODUCT_CODE) Q ON P.FUND_CODE = Q.PRODUCT_CODE
    WHERE 1=1
    <if test="taCodeList != null and taCodeList.size()> 0">
      AND P.TA_CODE in
      <foreach collection="taCodeList" item="item" separator="," open="(" close=")">
           #{item}
      </foreach>
    </if>
    <if test="fundCodeList != null and fundCodeList.size()>0 ">
      AND P.FUND_CODE in
      <foreach collection="fundCodeList" item="item" separator="," open="(" close=")">
         #{item}
    </foreach>
    </if>
    <if test="productChannel != null and productChannel != '' ">
      AND P.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
    </if>
    <if test="submitTaStartDt != null and submitTaStartDt != '' ">
      AND P.SUBMIT_TA_DT <![CDATA[ >= ]]> #{submitTaStartDt,jdbcType=VARCHAR}
    </if>
    <if test="submitTaEndDt != null and submitTaEndDt != '' ">
      AND P.SUBMIT_TA_DT <![CDATA[ <= ]]> #{submitTaEndDt,jdbcType=VARCHAR}
    </if>
  </select>

  <!-- 查询合规订单信息 -->
  <select id="selectByFundCodeAndSubmitTaDt" parameterType="map" resultMap="ConsoleResultMap">
    SELECT distinct T.FUND_CODE, T.TX_ACCT_NO, B.CUST_NAME,T.FIRST_BUY_FLAG
    FROM HIGH_DEAL_ORDER_DTL T
 			LEFT JOIN SIMU_FUND_CHECK_ORDER A ON T.DEAL_NO = A.DEAL_NO
 			INNER JOIN DEAL_ORDER B ON T.DEAL_NO = B.DEAL_NO
	   WHERE T.ASSETCERTIFICATE_STATUS = '0'
	      AND T.ASSET_INTERPOSE_FLAG = '1'
	      AND T.TX_APP_FLAG = '0'
	      AND T.PRODUCT_CHANNEL in ('6','7')
	      AND T.M_BUSI_CODE IN ('1120','1122')
	      AND (A.SUBMIT_APP_FLAG != '2' OR A.SUBMIT_APP_FLAG IS NULL)
	      AND B.INVST_TYPE = '1'
      <if test="fundCode != null and fundCode !=''">
        AND T.FUND_CODE = #{fundCode, jdbcType=VARCHAR}
      </if>
      <if test="submitStartDt != null and submitStartDt !=''">
        AND T.SUBMIT_TA_DT <![CDATA[>=]]> #{submitStartDt, jdbcType=VARCHAR}
      </if>
      <if test="submitEndDt != null and submitEndDt !=''">
        AND T.SUBMIT_TA_DT <![CDATA[<=]]> #{submitEndDt, jdbcType=VARCHAR}
      </if>
  </select>

  <select id="selectByDealNoListAndTaCodeList" parameterType="map" resultType="java.lang.String">
    SELECT T.DEAL_NO
    FROM HIGH_DEAL_ORDER_DTL T
	WHERE T.TA_CODE IN
	<foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
  		#{item}
  	</foreach>
  	AND T.DEAL_NO IN
  	<foreach collection="dealNos" item="item" index="index" open="(" close=")" separator=",">
  		#{item}
  	</foreach>
  </select>

  <select id="getTaByDealNoListAndTaCodeList" parameterType="map" resultType="java.lang.String">
    SELECT T.TA_CODE
    FROM HIGH_DEAL_ORDER_DTL T
	WHERE T.TA_CODE IN
	<foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
  		#{item}
  	</foreach>
  	AND T.DEAL_NO IN
  	<foreach collection="dealNos" item="item" index="index" open="(" close=")" separator=",">
  		#{item}
  	</foreach>
  </select>

  <select id="selectByDealNoList" parameterType="map" resultMap="BaseResultMap">
    SELECT <include refid="com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
    FROM HIGH_DEAL_ORDER_DTL T
	WHERE T.DEAL_NO IN
  	<foreach collection="dealNos" item="item" index="index" open="(" close=")" separator=",">
  		#{item}
  	</foreach>
  </select>

  <select id="selectRepurchaseDeals" parameterType="map" resultMap="BaseResultMap">
       select H.DEAL_DTL_NO,
              H.DEAL_NO,
              H.FUND_CODE,
              H.FUND_NAME,
              H.FUND_TYPE,
              H.FUND_SUB_TYPE,
              H.FUND_SHARE_CLASS,
              H.APP_AMT,
              H.APP_VOL,
              H.REDEEM_DIRECTION,
              H.DISCOUNT_RATE,
              H.TX_APP_FLAG,
              H.RISK_FLAG,
              H.LARGE_REDM_FLAG,
              H.ALLOW_DT,
              H.FUND_DIV_MODE,
              H.M_BUSI_CODE,
              H.FEE,
              H.ACK_AMT,
              H.ACK_VOL,
              H.ACK_DT,
              H.TX_ACK_FLAG,
              H.TA_TRADE_DT,
              H.CANCEL_ORDER_SRC,
              H.NOTIFY_SUBMIT_FLAG,
              H.MEMO,
              H.UPDATE_DTM,
              H.CREATE_DTM,
              H.DIS_CODE,
              H.OUTLET_CODE,
              H.TA_CODE,
              H.TX_ACCT_NO,
              H.NAV,
              H.PRODUCT_CHANNEL,
              H.APPOINTMENT_DISCOUNT,
              H.ESITMATE_FEE,
              H.FIRST_BUY_FLAG,
              H.ESIGNATURE_FLAG,
              H.ECONTRACT_FLAG,
              H.UNUSUAL_TRANS_TYPE,
              H.OPEN_END_TIME,
              H.PRODUCT_CLASS,
              H.ADVANCE_AMT,
              H.LIMIT_TYPE,
              H.SUPPORT_ADVANCE_FLAG,
              H.CALM_TIME,
              H.INTEREST,
              H.ACHIEVEMENT_PAY,
              H.ACHIEVEMENT_COMPEN,
              H.VOL_BY_INTEREST,
              H.RECUPERATE_FEE,
              H.CALM_DTM,
              H.CUST_RISK_LEVEL,
              H.FUND_RISK_LEVEL,
              H.ORDER_FORM_TYPE,
              H.SUBMIT_TA_DT,
              H.APPOINTMENT_DEALNO_TYPE,
              H.DISCOUNT_MODEL,
              H.FEE_CAL_MODE,
              D.CP_ACCT_NO,
              H.PROTOCOL_NO,
              H.PROTOCOL_TYPE,
              H.BANK_ACCT,
              H.BANK_CODE,
              H.DUALENTRY_STATUS,
              H.DUALENTRY_FINISH_DTM,
              H.DUALENTRY_INTERPOSE_FLAG,
              H.CALLBACK_STATUS,
              H.CALLBACK_FINISH_DTM,
              H.CALLBACK_INTERPOSE_FLAG,
              H.CALMDTM_INTERPOSE_FLAG,
              H.ASSETCERTIFICATE_STATUS,
              H.ASSET_INTERPOSE_FLAG,
              H.RETRIEVE_DTM,
              H.Refund_Dt,
              H.SUBMITTADT_INTERPOSE_FLAG,
              H.REPURCHASE_PROTOCOL_NO
         from HIGH_DEAL_ORDER_DTL H
              INNER JOIN (SELECT DEAL_NO, CP_ACCT_NO FROM  DEAL_ORDER WHERE DEAL_TYPE = '2' ) D ON h.DEAL_NO = d.Deal_No
       where REPURCHASE_PROTOCOL_NO = #{repurchaseProtocolNo, jdbcType=VARCHAR}
  </select>

  <select id="selectRepurchaseDealsByNos" parameterType="map" resultMap="BaseResultMap">
    select <include refid="com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper.Base_Column_List"/>
    from HIGH_DEAL_ORDER_DTL
    where
    1=1
    <if test="repurchaseProtocolNoList != null and repurchaseProtocolNoList.size() > 0" >
      and REPURCHASE_PROTOCOL_NO in
      <foreach collection="repurchaseProtocolNoList" item="item" separator="," open="(" close=")">
           #{item}
      </foreach>
    </if>
  </select>

  <select id="selectByDealDtlNoList" parameterType="map" resultMap="BaseResultMap">
    SELECT <include refid="com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
    FROM HIGH_DEAL_ORDER_DTL T
	WHERE T.DEAL_DTL_NO IN
  	<foreach collection="dealDtlNos" item="item" index="index" open="(" close=")" separator=",">
  		#{item}
  	</foreach>
  </select>

  <select id="selectDtlChangeCardByDealDtlNoList" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.HighDealDtlChangeCardVo">
	    select a.deal_no as dealNo,
	       a.tx_acct_no as custNo,
	       b.cust_name as custName,
	       a.m_busi_code as busiCode,
	       a.fund_code as fundCode,
	       a.fund_name as fundName,
	       a.bank_acct as outBankAcct,
	       a.protocol_type as outProtocolType,
	       a.protocol_no as outProtocolNo,
	       b.bank_acct as saveBankAcct,
	       b.protocol_type as saveProtocolType,
	       b.protocol_no as saveProtocolNo,
	       a.tx_app_flag as dealDtlAppStat,
		  IF(a.tx_app_flag = '0',
		  IF(a.tx_ack_flag = '4', '3',
		  IF(a.tx_ack_flag = '5', '4', '2')),
		  '2') AS result
	  from high_deal_order_dtl a
	 inner join deal_order b on a.deal_no = b.deal_no
	 where 1 = 1
	 <if test="dealDtlNos != null and dealDtlNos.size()>0 ">
      AND a.DEAL_DTL_NO in
      <foreach collection="dealDtlNos" item="item" separator="," open="(" close=")">
         #{item}
    </foreach>
    </if>
  </select>
  <select id="selectCustInvestDeals" resultType="com.howbuy.tms.high.batch.dao.vo.CustInvestDealVo" parameterType="map">
    select
         t.deal_no dealNo,
         t.tx_acct_no txAcctNo,
         d.submit_ta_dt taTradeDt,
         t.deal_type dealType,
         d.m_busi_code mBusiCode,
         d.deal_dtl_no dealDtlNo,
         d.ack_amt ackAmt,
         d.ack_vol ackVol ,
         d.fund_code fundCode,
         d.fund_name fundName
    from deal_order t
    inner join high_deal_order_dtl d
    on t.deal_no = d.deal_no
    where
    t.tx_acct_no = #{conditonVo.txAcctNo, jdbcType=VARCHAR}
    <if test="conditonVo.startDt != null and conditonVo.startDt != ''">
      and  d.submit_ta_dt <![CDATA[>=]]> #{conditonVo.startDt,jdbcType=VARCHAR}
    </if>
    <if test="conditonVo.endDt != null and conditonVo.endDt != ''">
      and d.submit_ta_dt <![CDATA[<=]]> #{conditonVo.endDt, jdbcType=VARCHAR}
    </if>
    and d.m_busi_code in ('1120', '1122', '1134', '1127')
    and  t.order_status in ('2', '3')
    <if test="conditonVo.dealDtlNos != null and conditonVo.dealDtlNos.size()>0 ">
      AND d.DEAL_DTL_NO in
      <foreach collection="conditonVo.dealDtlNos" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>

  </select>

  <select id="selectRedeemOnWayDeal" parameterType="map" resultMap="BaseResultMap">
    SELECT t.* FROM HIGH_DEAL_ORDER_DTL H
      INNER JOIN DEAL_ORDER t ON t.DEAL_NO = h.DEAL_NO
      WHERE
         t.TX_ACCT_NO IN
        <foreach collection="txAcctNoList" item="item" open="(" close=")" separator=",">
             #{item}
        </foreach>
      AND h.SUBMIT_TA_DT >= #{taTradeDt, jdbcType=VARCHAR}
  </select>

  <resultMap id="UnSumitDealBySubmitMap"
             type="com.howbuy.tms.high.batch.dao.vo.UnSubmitHighDealVo">
    <result column="FUND_CODE" jdbcType="CHAR" property="fundCode" />
    <result column="APP_DTM" jdbcType="CHAR" property="appDtm" />
    <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt" />
    <result column="M_BUSI_CODE" jdbcType="VARCHAR" property="mBusiCode" />
    <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo" />
    <result column="SUBMITTADT_INTERPOSE_FLAG" jdbcType="VARCHAR" property="submitTaDtInterposeFlag" />

  </resultMap>
    <select id="selectUnSumitDealBySubmitDt"
            resultMap="UnSumitDealBySubmitMap" resultType="map">
        select t1.fund_code,t2.app_dtm,t1.m_busi_code
        from HIGH_DEAL_ORDER_DTL t1
           INNER JOIN DEAL_ORDER t2
        on t1.deal_no = t2.deal_no
        where fund_code = #{productCode, jdbcType=VARCHAR}
          and SUBMIT_TA_DT= #{submitTaDt, jdbcType=VARCHAR}
          and M_BUSI_CODE = #{mBusiCode, jdbcType=VARCHAR}

    </select>
  <select id="selectUnSumitDealByTacodes" resultMap="UnSumitDealBySubmitMap" resultType="map">
     select t1.fund_code,t2.app_dtm,t1.m_busi_code, t1.submit_Ta_Dt, t1.deal_no, t1.SUBMITTADT_INTERPOSE_FLAG
        from HIGH_DEAL_ORDER_DTL t1
           INNER JOIN DEAL_ORDER t2
        on t1.deal_no = t2.deal_no
        where
          t1.SUBMIT_TA_DT >= #{submitTaDt, jdbcType=VARCHAR}
          <if test="taCodes != null and taCodes.size() > 0">
            and t1.ta_code in
            <foreach collection="taCodes" item="item" separator="," open="(" close=")">
                    #{item, jdbcType=VARCHAR}
               </foreach>
          </if>
          and t1.M_BUSI_CODE in ('1120', '1122', '1124')
          AND t1.NOTIFY_SUBMIT_FLAG <![CDATA[<>]]> '2'
          AND t1.TX_APP_FLAG = '0'

  </select>

  <resultMap id="UnPayDealMap"
             type="com.howbuy.tms.high.batch.dao.vo.UnPmtDtHighDealVo">
    <result column="FUND_CODE" jdbcType="CHAR" property="fundCode" />
    <result column="APP_DTM" jdbcType="CHAR" property="appDtm" />
    <result column="PMT_DT" jdbcType="VARCHAR" property="pmtDt" />
    <result column="M_BUSI_CODE" jdbcType="VARCHAR" property="mBusiCode" />
    <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo" />
    <result column="APPOINT_ID" jdbcType="VARCHAR" property="appointId" />
    <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt" />
  </resultMap>

    <select id="selectUnPayByTacodes" resultMap="UnPayDealMap">
      select t1.fund_code,t2.app_dtm,t1.m_busi_code, t3.PMT_DT, t1.deal_no, t1.APPOINT_ID, t1.SUBMIT_TA_DT, t1.TA_CODE
      from HIGH_DEAL_ORDER_DTL t1
      INNER JOIN DEAL_ORDER t2  on t1.deal_no = t2.deal_no
      inner join PAYMENT_ORDER t3 on t1.deal_no = t3.deal_no
      where
      t3.PMT_DT >= #{pmtDt, jdbcType=VARCHAR}
      <if test="taCodes != null and taCodes.size() > 0">
        and t1.ta_code in
        <foreach collection="taCodes" item="item" separator="," open="(" close=")">
          #{item, jdbcType=VARCHAR}
        </foreach>
      </if>
      and t1.M_BUSI_CODE in ('1120', '1122')
      AND t3.tx_pmt_flag  in ('9', '10', '11')
      and t3.PAYMENT_TYPE = '06'
      AND t1.TX_APP_FLAG = '0'
    </select>


    <update id="updateByDealDtlNoByCxgDealNo" parameterType="map">
		update HIGH_DEAL_ORDER_DTL
		set CXG_DEAL_NO = #{cxgDealNo,jdbcType=VARCHAR}
		where DEAL_DTL_NO = #{orderDtlNo,jdbcType=VARCHAR}
	</update>

  <select id="queryCustSubsAmtInfo" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.SubsAmtInfoVo">
	  select a.tx_acct_no as txAcctNo,
	  product_code as fundCode,
	  DIS_CODE as disCode,
	  ta_code as taCode,
	  subs_amt as subsAmt,
	  subs_flag as subsFlag,
	  have_no_trade_over_account as haveNoTradeOverAccount,
	  a.balance_vol as balanceVol
	  from (select tx_acct_no,
	  product_code,
	  min(DIS_CODE) DIS_CODE,
	  min(ta_code) ta_code,
	  sum(balance_vol) balance_vol
	  from cust_books
	  where product_code in
	  <foreach collection="fundCodes" item="item" separator="," open="(" close=")">
		  #{item, jdbcType=VARCHAR}
	  </foreach>
                 group by tx_acct_no, product_code) a
          left join (select t.tx_acct_no,
                            t.fund_code,
                            max(ifnull(t.subs_amt, 0)) subs_amt,
					  SUM(CASE t.tx_ack_flag
					  WHEN '2' THEN 1
					  ELSE 0
					  END) AS subs_flag,

					  SUM(CASE t.m_busi_code
					  WHEN '1133' THEN 1
					  WHEN '1134' THEN 1
					  WHEN '1135' THEN 1
					  ELSE 0
					  END) AS have_no_trade_over_account
                       from High_Deal_Order_Dtl t
                      where t.fund_code in
                      <foreach collection="fundCodes" item="item" separator="," open="(" close=")">
                          #{item, jdbcType=VARCHAR}
                      </foreach>
                      and (t.tx_ack_flag in ('0', '3', '4') or
                            (t.tx_ack_flag = '2' and t.m_busi_code = '1120'))
                      group by t.tx_acct_no, t.fund_code) b
            on a.tx_acct_no = b.tx_acct_no
           and a.product_code = b.fund_code
           where (a.balance_vol > 0 or b.tx_acct_no is not null)
  </select>

  <update id="updateSubsAmt" parameterType="map">
        update HIGH_DEAL_ORDER_DTL
		set subs_amt = #{subsAmt}
		where tx_acct_no = #{txAcctNo}
		and fund_code = #{fundCode}
		and m_busi_code in ('1120', '1122')
		and tx_ack_flag in ('0', '3', '4')
  </update>

	<update id="updateAllSubsAmt" parameterType="map">
		update HIGH_DEAL_ORDER_DTL
		set subs_amt = #{subsAmt}
		where tx_acct_no = #{txAcctNo}
		  and fund_code = #{fundCode}
		  and m_busi_code in ('1120', '1122','1134','1135')
	</update>

  <select id="selectCustRedeemOrderDtl" parameterType="com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo" resultMap="BaseResultMap">
    select t.DEAL_DTL_NO, t.DEAL_NO, t.FUND_CODE, t.FUND_NAME, t.TX_ACCT_NO, o.CP_ACCT_NO,
      t.ACK_DT, t.APP_VOL, t.FEE, t.ACK_AMT, t.ACK_VOL, t.SUBMIT_TA_DT, t.ORIGIN_SERIAL_NO
    from HIGH_DEAL_ORDER_DTL t
    inner join DEAL_ORDER o
    on t.deal_no = o.deal_no
    where t.tx_acct_no = #{txAcctNo, jdbcType=VARCHAR}
    and o.cp_acct_no = #{cpAcctNo, jdbcType=VARCHAR}
    and t.fund_code = #{fundCode, jdbcType=VARCHAR}
    and t.ack_dt = #{ackDt, jdbcType=VARCHAR}
    and t.m_busi_code = '1124'
    order by t.create_dtm asc
  </select>

  <select id="countDealOrderMergeSubmitNum" parameterType="map" resultType="int">
    select count(1) from high_deal_order_dtl where MAIN_DEAL_ORDER_NO = #{mainDealNo, jdbcType=VARCHAR}
  </select>

    <select id="selectOrdersByMainDealNo" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
        from high_deal_order_dtl
        where MAIN_DEAL_ORDER_NO = #{mainDealNo, jdbcType=VARCHAR}
    </select>

  <update id="updateBuyOrderJoinDt" parameterType="com.howbuy.tms.high.batch.dao.vo.SelectRedeemSplitSubCustBookVo">
      update HIGH_DEAL_ORDER_DTL t
         set t.join_dt = t.submit_ta_dt
       where t.m_busi_code in ('1120', '1122')
         and t.tx_ack_flag in ('0', '3', '4')
         and t.fund_code in
        <foreach collection="fundCodes" item="item" separator="," open="(" close=")">
          #{item, jdbcType=VARCHAR}
        </foreach>
         and t.join_dt is null
  </update>

  <resultMap id="OtcHighDealOrderDtlResultMap" type="com.howbuy.tms.high.batch.dao.vo.OtcHighDealOrderDtlVo">
    <id column="DEAL_DTL_NO" jdbcType="VARCHAR" property="dealDtlNo" />
    <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo" />
    <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
    <result column="FUND_NAME" jdbcType="VARCHAR" property="fundName" />
    <result column="FUND_TYPE" jdbcType="VARCHAR" property="fundType" />
    <result column="FUND_SUB_TYPE" jdbcType="VARCHAR" property="fundSubType" />
    <result column="FUND_SHARE_CLASS" jdbcType="CHAR" property="fundShareClass" />
    <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt" />
    <result column="APP_VOL" jdbcType="DECIMAL" property="appVol" />
    <result column="REDEEM_DIRECTION" jdbcType="CHAR" property="redeemDirection" />
    <result column="DISCOUNT_RATE" jdbcType="DECIMAL" property="discountRate" />
    <result column="TX_APP_FLAG" jdbcType="CHAR" property="txAppFlag" />
    <result column="RISK_FLAG" jdbcType="CHAR" property="riskFlag" />
    <result column="LARGE_REDM_FLAG" jdbcType="CHAR" property="largeRedmFlag" />
    <result column="ALLOW_DT" jdbcType="VARCHAR" property="allowDt" />
    <result column="FUND_DIV_MODE" jdbcType="CHAR" property="fundDivMode" />
    <result column="M_BUSI_CODE" jdbcType="CHAR" property="mBusiCode" />
    <result column="FEE" jdbcType="DECIMAL" property="fee" />
    <result column="ACK_AMT" jdbcType="DECIMAL" property="ackAmt" />
    <result column="ACK_VOL" jdbcType="DECIMAL" property="ackVol" />
    <result column="ACK_DT" jdbcType="VARCHAR" property="ackDt" />
    <result column="TX_ACK_FLAG" jdbcType="CHAR" property="txAckFlag" />
    <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt" />
    <result column="CANCEL_ORDER_SRC" jdbcType="CHAR" property="cancelOrderSrc" />
    <result column="NOTIFY_SUBMIT_FLAG" jdbcType="CHAR" property="notifySubmitFlag" />
    <result column="MEMO" jdbcType="VARCHAR" property="memo" />
    <result column="DIS_CODE" jdbcType="VARCHAR" property="disCode" />
    <result column="OUTLET_CODE" jdbcType="VARCHAR" property="outletCode" />
    <result column="TA_CODE" jdbcType="VARCHAR" property="taCode" />
    <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo" />
    <result column="NAV" jdbcType="DECIMAL" property="nav" />
    <result column="PRODUCT_CHANNEL" jdbcType="VARCHAR" property="productChannel" />
    <result column="CONTRACT_NO" jdbcType="VARCHAR" property="contractNo" />
    <result column="TX_COMP_FLAG" jdbcType="VARCHAR" property="txCompFlag" />
    <result column="TX_PMT_FLAG" jdbcType="VARCHAR" property="txPmtFlag" />
    <result column="PMT_COMP_FLAG" jdbcType="VARCHAR" property="pmtCompFlag" />
    <result column="APP_DATE" jdbcType="VARCHAR" property="appDate" />
    <result column="APP_TIME" jdbcType="VARCHAR" property="appTime" />
    <result column="TRANSACTOR_NAME" jdbcType="VARCHAR" property="transactorName" />
    <result column="RET_CODE" jdbcType="VARCHAR" property="pmtRetCode" />
    <result column="RET_DESC" jdbcType="VARCHAR" property="pmtRetDesc" />
    <result column="ORDER_STATUS" jdbcType="VARCHAR" property="orderStatus" />
    <result column="SUBMIT_APP_FLAG" jdbcType="VARCHAR" property="submitAppFlag" />
    <result column="MERGE_SUBMIT_FLAG" jdbcType="VARCHAR" property="mergeSubmitFlag" />
    <result column="MAIN_DEAL_ORDER_NO" jdbcType="VARCHAR" property="mainDealOrderNo" />
  </resultMap>

  <select id="selectHighDealOrderDtlForOtc" resultMap="OtcHighDealOrderDtlResultMap" parameterType="map" >
    select d.*,
           t.order_status,
           p.tx_pmt_flag,
           p.pmt_comp_flag,
           t.app_date,
           t.app_time,
           p.ret_code,
           p.ret_desc,
           e.transactor_name,
           f.contract_no,
           f.tx_comp_flag,
           f.submit_app_flag
      from high_deal_order_dtl d
      LEFT JOIN deal_order t
        ON d.deal_no = t.deal_no
      LEFT JOIN deal_order_extend e
        ON d.deal_no = e.deal_no
      LEFT JOIN payment_order p
        ON d.deal_no = p.deal_no
      LEFT JOIN simu_fund_check_order f
        ON d.deal_dtl_no = f.deal_dtl_no
     where d.dis_code = #{disCode,jdbcType=VARCHAR}
       and d.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
    <if test="dealNo != null">
      and d.deal_no = #{dealNo,jdbcType=VARCHAR}
    </if>
    <if test="externalDealNo != null">
      and t.external_deal_no = #{externalDealNo,jdbcType=VARCHAR}
    </if>
  </select>

  <!-- 渠道是 柜台(渠道机构))和机构 排除  TA生成的订单
  TX_APP_FLAG（0申请成功）
  NOTIFY_SUBMIT_FLAG（0无需通知）
  TX_ACK_FLAG（4 确认成功)

  COUNTER("1", "柜台"),
  WEBSITE("2", "网站"),
  TELEPHONE("3", "电话"),
  WAP("4", "Wap"),
  APP("5", "App"),
  INST("6", "机构"),
  TA("7", "TA");
  -->
  <select id="selectHighDealOrderDtlForOtcAll" resultMap="OtcHighDealOrderDtlResultMap" parameterType="map" >
   		select d.*,
               t.order_status,
               p.tx_pmt_flag,
               p.pmt_comp_flag,
               t.app_date,
               t.app_time,
               p.ret_code,
               p.ret_desc,
               e.transactor_name,
               f.contract_no,
               f.tx_comp_flag,
               f.submit_app_flag
          from high_deal_order_dtl d
          left join deal_order t
            on d.deal_no = t.deal_no
          LEFT JOIN deal_order_extend e
            ON d.deal_no = e.deal_no
          left join payment_order p
            on d.deal_no = p.deal_no
          LEFT JOIN simu_fund_check_order f
            ON d.deal_dtl_no = f.deal_dtl_no
         where d.ta_trade_dt =  #{taTradeDt, jdbcType = VARCHAR}
           and d.dis_code =  #{disCode, jdbcType = VARCHAR}
           and t.TX_CHANNEL in('1', '6')
    <if test="mBusiCode != null and mBusiCode != ''">
            and d.m_busi_code = #{mBusiCode, jdbcType = VARCHAR}
          </if>
          <if test="taCode != null and taCode != ''">
            and d.ta_code = #{taCode, jdbcType = VARCHAR}
          </if>
  </select>

  <select id="countNotPaySuccessSubOrders" parameterType="map" resultType="int">
    select count(1)
      from high_deal_order_dtl t
      left join payment_order p
        on t.deal_no = p.deal_no
     where t.MAIN_DEAL_ORDER_NO = #{mainDealNo, jdbcType=VARCHAR}
       and p.tx_pmt_flag not in ('0', '2', '11', '17')
  </select>


  <resultMap id="HighFundArrivalProofRecordMap" type="com.howbuy.tms.high.batch.dao.vo.HighFundArrivalProofRecordVo">
    <id column="DEAL_NO" jdbcType="VARCHAR" property="dealNo" />
    <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo" />
    <result column="CUST_NAME" jdbcType="VARCHAR" property="custName" />
    <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt" />
    <result column="PMT_COMPLETE_DTM" jdbcType="TIMESTAMP" property="pmtCompleteDtm" />
    <result column="APPOINT_ID" jdbcType="VARCHAR" property="appointId" />
    <result column="PAY_DEADLINE_DTM" jdbcType="TIMESTAMP" property="payDeadlineDtm" />
    <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
    <result column="FUND_NAME" jdbcType="VARCHAR" property="fundName" />
    <result column="NET_APP_AMT" jdbcType="DECIMAL" property="appAmt" />
    <result column="FEE" jdbcType="DECIMAL" property="fee" />
  </resultMap>

  <select id="selectLimitedCooperativeProductOrders" resultMap="HighFundArrivalProofRecordMap" parameterType="map">
    select t.DEAL_NO,t.TX_ACCT_NO,d.CUST_NAME,t.TA_TRADE_DT,p.PMT_COMPLETE_DTM,t.APPOINT_ID,p.PAY_DEADLINE_DTM,
    t.FUND_CODE,t.FUND_NAME,t.NET_APP_AMT,t.FEE
      from high_deal_order_dtl t
      left join payment_order p
        on t.deal_no = p.deal_no
      left join DEAL_ORDER d
        on t.deal_no = d.deal_no
	  LEFT JOIN high_fund_arrival_proof m
	  on m.deal_no=t.deal_no
     where t.TA_TRADE_DT <![CDATA[ >= ]]> #{tradeDtStart, jdbcType=VARCHAR}
       and t.TA_TRADE_DT <![CDATA[ <= ]]> #{tradeDtEnd, jdbcType=VARCHAR}
       and t.fund_code in
      <foreach collection="productIds" item="item" separator="," open="(" close=")">
        #{item, jdbcType=VARCHAR}
      </foreach>
      and p.tx_pmt_flag = '2'
      and d.PAYMENT_TYPE = '01'
	  and d.order_status  in ('1','2','3')
      and t.M_BUSI_CODE in ('1120','1122')
	  and m.deal_no is null
  </select>

  <select id="selectPeDivideCallAmt" resultType="java.math.BigDecimal"  parameterType="map">
    select ifnull(SUM(ack_amt), 0) as amt from
    (
      select
	  ifnull(SUM(t3.NET_APP_AMT), 0) as ack_amt
      from DEAL_ORDER t1
      left join high_deal_order_dtl t3 on t1.deal_no = t3.deal_no
      where t1.ORDER_STATUS = '1'
      and t1.PAY_STATUS = '4'
      and t3.m_busi_code in ('1120', '1122')
      and t1.PRODUCT_CODE = #{fundCode, jdbcType=VARCHAR}
      and t1.TX_ACCT_NO = #{txAcctNo, jdbcType=VARCHAR}
	  <if test="taTradeDt != null ">
        and t3.TA_TRADE_DT <![CDATA[ <= ]]> #{taTradeDt, jdbcType=VARCHAR}
      </if>
      UNION ALL
      select
	  ifnull(SUM(t3.ACK_AMT-ifnull(t3.FEE,0)), 0) as ack_amt
      from DEAL_ORDER t1
      left join high_deal_order_dtl t3 on t1.deal_no = t3.deal_no
      where t1.ORDER_STATUS in ('2','3')
      and t1.PAY_STATUS = '4'
      and t3.m_busi_code in ('1120', '1122')
      and t1.PRODUCT_CODE = #{fundCode, jdbcType=VARCHAR}
      and t1.TX_ACCT_NO = #{txAcctNo, jdbcType=VARCHAR}
	  <if test="taTradeDt != null ">
		  and t3.TA_TRADE_DT <![CDATA[ <= ]]> #{taTradeDt, jdbcType=VARCHAR}
	  </if>
    ) a
  </select>

  <select id="countPurchaseForNotAppoint" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo">
    select a.SUBMIT_TA_DT as submitTaDate,ifnull(COUNT(DISTINCT a.TX_ACCT_NO),0) AS purPersonCount,
	  	   ifnull(COUNT(1),0) as purOrderCount,
           SUM(ifnull(a.APP_AMT,0)) as purAmtCount,
           SUM(ifnull(a.APP_AMT,0) - ifnull(a.FEE,0)) as purNetAmtCount,
           SUM(ifnull(a.FEE,0)) as purFeeCount
    from HIGH_DEAL_ORDER_DTL a
    LEFT JOIN PAYMENT_ORDER b ON a.DEAL_NO = b.DEAL_NO
    WHERE a.M_BUSI_CODE in ('1120','1122')
    and a.TX_APP_FLAG = '0'
    and b.TX_PMT_FLAG in ('2', '11', '17')
    <if test="fundCode != null and fundCode != ''">
      and a.FUND_CODE = #{fundCode, jdbcType = VARCHAR}
    </if>
    <if test="submitTaDateBegin != null and submitTaDateBegin != ''">
      and a.SUBMIT_TA_DT <![CDATA[ >= ]]> #{submitTaDateBegin, jdbcType = VARCHAR}
    </if>
    <if test="submitTaDateEnd != null and submitTaDateEnd != ''">
      and a.SUBMIT_TA_DT <![CDATA[ <= ]]> #{submitTaDateEnd, jdbcType = VARCHAR}
    </if>
    group by a.SUBMIT_TA_DT
  </select>

  <!--查询非预约的购买数据Dtl-->
  <select id="countPurchaseForNotAppointDtl" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.AppointmentDetailVo">
    select
      a.M_BUSI_CODE as businessType,
	  CONCAT(a.FUND_CODE, '-', a.FUND_NAME)  as productName,
	  ifnull(a.APP_AMT,0) as appAmt,
	  ifnull(a.APP_AMT,0) - ifnull(a.FEE,0) as appNetAmt,
	  ifnull(a.FEE,0) as fee,
      a.SUBMIT_TA_DT as submitTaDate,
      a.TX_ACCT_NO as txAcctNo
    from HIGH_DEAL_ORDER_DTL a
    LEFT JOIN PAYMENT_ORDER b ON a.DEAL_NO = b.DEAL_NO
    WHERE a.M_BUSI_CODE in ('1120','1122')
      and a.TX_APP_FLAG = '0'
      and b.TX_PMT_FLAG in ('2', '11', '17')
      <if test="fundCode != null and fundCode != ''">
        and a.FUND_CODE = #{fundCode, jdbcType = VARCHAR}
      </if>
      <if test="submitTaDateBegin != null and submitTaDateBegin != ''">
        and a.SUBMIT_TA_DT <![CDATA[ >= ]]> #{submitTaDateBegin, jdbcType = VARCHAR}
      </if>
      <if test="submitTaDateEnd != null and submitTaDateEnd != ''">
        and a.SUBMIT_TA_DT <![CDATA[ <= ]]> #{submitTaDateEnd, jdbcType = VARCHAR}
      </if>
  </select>

  <select id="countRedeemForNotAppoint" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo">
    SELECT T.SUBMIT_TA_DT as submitTaDate,ifnull(COUNT(1),0) as redeemOrderCount, SUM(ifnull(T.APP_VOL,0)) as redeemVolCount,
	  ifnull(COUNT(DISTINCT T.TX_ACCT_NO),0) as redeemPersonCount
    FROM HIGH_DEAL_ORDER_DTL T
    WHERE T.TX_APP_FLAG = '0'
     AND T.M_BUSI_CODE = '1124'
     <if test="fundCode != null and fundCode != ''">
      and T.FUND_CODE = #{fundCode, jdbcType = VARCHAR}
     </if>
     <if test="submitTaDateBegin != null and submitTaDateBegin != ''">
      and T.SUBMIT_TA_DT <![CDATA[ >= ]]> #{submitTaDateBegin, jdbcType = VARCHAR}
     </if>
     <if test="submitTaDateEnd != null and submitTaDateEnd != ''">
      and T.SUBMIT_TA_DT <![CDATA[ <= ]]> #{submitTaDateEnd, jdbcType = VARCHAR}
     </if>
    group by T.SUBMIT_TA_DT
  </select>

  <!--查询非预约的赎回数据Dtl-->
  <select id="countRedeemForNotAppointDtl" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.AppointmentDetailVo">
    SELECT
      A.CUST_NAME AS custName,
      T.M_BUSI_CODE AS businessType,
	  CONCAT(T.FUND_CODE, '-',T.FUND_NAME)  as productName,
	  ifnull(T.APP_VOL,0) AS appVol,
      T.SUBMIT_TA_DT as submitTaDate,
      T.TX_ACCT_NO as txAcctNo
    FROM HIGH_DEAL_ORDER_DTL T
    INNER JOIN DEAL_ORDER A ON T.DEAL_NO = A.DEAL_NO
    WHERE T.TX_APP_FLAG = '0'
      AND T.M_BUSI_CODE = '1124'
      <if test="fundCode != null and fundCode != ''">
        and T.FUND_CODE = #{fundCode, jdbcType = VARCHAR}
      </if>
      <if test="submitTaDateBegin != null and submitTaDateBegin != ''">
        and T.SUBMIT_TA_DT <![CDATA[ >= ]]> #{submitTaDateBegin, jdbcType = VARCHAR}
      </if>
      <if test="submitTaDateEnd != null and submitTaDateEnd != ''">
        and T.SUBMIT_TA_DT <![CDATA[ <= ]]> #{submitTaDateEnd, jdbcType = VARCHAR}
      </if>
  </select>

  <select id="getNotCallBackForReport" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.HighTradeReportCheckVo">
    SELECT T.DEAL_NO as dealNo,
    T.FUND_CODE as fundCode,
    B.APP_DATE as appDt,
    B.APP_TIME as appTime,
    B.TX_ACCT_NO as txAcctNo,
    B.CUST_NAME as custName,
    B.INVST_TYPE as investType,
    B.ID_TYPE as idType,
    B.ID_NO as idNo,
    T.M_BUSI_CODE as mBusiCode,
    T.FUND_NAME as fundName,
    T.APP_AMT as appAmt,
    T.FEE as fee,
    T.TA_CODE as taInfo,
    T.SUBMIT_TA_DT as submitTaDt,
    T.CALLBACK_STATUS as callBackStatus,
    T.CALLBACK_INTERPOSE_FLAG as callBackFlag,
    T.CALLBACK_FINISH_DTM as callBackDtm
    FROM HIGH_DEAL_ORDER_DTL T
    INNER JOIN DEAL_ORDER B ON T.DEAL_NO = B.DEAL_NO
    WHERE T.CALLBACK_STATUS = '1'
    AND T.TX_APP_FLAG = '0'
    AND T.CALLBACK_INTERPOSE_FLAG = '1'
    AND T.M_BUSI_CODE IN ('1120','1122')
    AND T.PRODUCT_CHANNEL IN ('6','7')
    AND T.SUBMIT_TA_DT = #{submitTaDate,jdbcType=VARCHAR}

  </select>


  <resultMap id="HighAppointInfoVoMap" type="com.howbuy.tms.high.batch.dao.vo.HighAppointInfoVo">
    <result column="PAYMENT_TYPE" property="paymentType"/>
    <result column="APP_DATE" property="appDate"/>
    <result column="APP_TIME" property="appTime"/>
    <result column="DEAL_NO" property="dealNo"/>
    <result column="DEAL_DTL_NO" property="dealDtlNo"/>
  </resultMap>
  <select id="getAppointInfoByDealDtlNo" parameterType="map" resultMap="HighAppointInfoVoMap">
    select b.PAYMENT_TYPE,b.APP_DATE,b.APP_TIME,b.DEAL_NO,a.DEAL_DTL_NO
    from HIGH_DEAL_ORDER_DTL a
    inner join DEAL_ORDER b on a.DEAL_NO = b.DEAL_NO
    where a.DEAL_DTL_NO =#{dealDtlNo,jdbcType=VARCHAR}
  </select>

  <update id="updatePayOutStatusAndDtByOSerialNo" parameterType="map">
      update HIGH_DEAL_ORDER_DTL t
      set t.PAY_OUT_STATUS = #{orderPayStatus,jdbcType=VARCHAR},
          t.PAY_OUT_DT = #{payDt,jdbcType=VARCHAR}
      where t.m_busi_code in
      <foreach collection="mBusiCodeList" index="index" item="item" open="(" separator="," close=")" >
        #{item,jdbcType=VARCHAR}
      </foreach>
      <if test="ackDt != null">
        and t.ack_dt >= #{ackDt,jdbcType=VARCHAR}
      </if>
      <if test="refundDt != null">
        and t.refund_dt >= #{refundDt,jdbcType=VARCHAR}
      </if>
      <if test="txAppFlagList != null and txAppFlagList.size() > 0">
        and TX_APP_FLAG in
        <foreach collection="txAppFlagList" item="item" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="dealDtlNo != null">
        and t.DEAL_DTL_NO = #{dealDtlNo,jdbcType=VARCHAR}
      </if>
      <if test="orgSerialNo != null">
        and t.ORIGIN_SERIAL_NO = #{orgSerialNo,jdbcType=VARCHAR}
      </if>
      and (t.CONTINUANCE_FLAG is null or t.CONTINUANCE_FLAG != '1')
      and (t.STAGE_FLAG is null or t.STAGE_FLAG != '1')
    </update>



  <!-- 根据确认日期查询 -->
  <select id="selectByAckDt" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
    from high_deal_order_dtl
    where ack_dt = #{ackDt,jdbcType=VARCHAR}
  </select>

  <!--在途交易客户-->
  <select id="selectOnWays" parameterType="map" resultMap="BaseResultMap">
    select t1.tx_acct_no, t1.product_code as fund_code
    from deal_order t1
	  left join DEAL_ORDER_EXTEND t2 on  t1.deal_no = t2.deal_no
	  left join HIGH_DEAL_ORDER_DTL t3 on t1.deal_no = t3.deal_no
    where
	  t1.product_code in
    <foreach item="item" index="index" collection="fundCodes" open="(" separator="," close=")">
      #{item}
    </foreach>
    and t1.deal_type = '2'
    and t1.order_status = '1'
    and t1.pay_status = '4'
    and t3.m_busi_code in ('1120', '1122')
    AND (t2.rec_stat = '0' or t2.rec_stat is null)
    group by t1.tx_acct_no, t1.product_code
  </select>


  <resultMap id="HighDealDtlAndCheckOrderResultMap" type="com.howbuy.tms.high.batch.dao.po.order.HighDealDtlAndCheckOrderPo">
    <id column="DEAL_DTL_NO" property="dealDtlNo" jdbcType="VARCHAR" />
    <result column="DEAL_NO" property="dealNo" jdbcType="VARCHAR" />
    <result column="ORIGIN_SERIAL_NO" jdbcType="VARCHAR" property="originSerialNo" />
    <result column="FUND_CODE" property="fundCode" jdbcType="VARCHAR" />
    <result column="FUND_NAME" property="fundName" jdbcType="VARCHAR" />
    <result column="ACK_DT" property="ackDt" jdbcType="VARCHAR" />
    <result column="APP_VOL" property="appVol" jdbcType="DECIMAL" />
    <result column="ACK_VOL" property="ackVol" jdbcType="DECIMAL" />
    <result column="ACK_AMT" property="ackAmt" jdbcType="DECIMAL" />
    <result column="NAV" property="nav" jdbcType="DECIMAL" />
    <result column="PAY_OUT_STATUS" property="payOutStatus" jdbcType="VARCHAR" />
    <result column="PAY_OUT_DT" property="payOutDt" jdbcType="VARCHAR" />
    <result column="TX_ACCT_NO" property="txAcctNo" jdbcType="VARCHAR" />
    <result column="CP_ACCT_NO" property="cpAcctNo" jdbcType="VARCHAR" />
    <result column="FEE" property="fee" jdbcType="DECIMAL" />
    <result column="JOIN_DT" property="joinDt" jdbcType="VARCHAR" />
    <result column="SUBMIT_DEAL_NO" property="submitDealNo" jdbcType="VARCHAR" />
  </resultMap>

  <!--获取订单明细及私募对账订单-->
  <select id="selectHighDealDtlAndCheckOrder" parameterType="map" resultMap="HighDealDtlAndCheckOrderResultMap">
    SELECT
      t.deal_no           ,
      t.deal_dtl_no       ,
      t.origin_serial_no  ,
      t.fund_code         ,
      t.fund_name         ,
      t.ack_dt            ,
      t.app_vol           ,
      t.ack_vol           ,
      t.ack_amt           ,
      t.nav               ,
      t.pay_out_status    ,
      t.pay_out_dt        ,
      t.tx_acct_no        ,
      t.cp_acct_no        ,
      t.fee               ,
      t.join_dt           ,
      s.submit_deal_no
    FROM HIGH_DEAL_ORDER_DTL t
    INNER JOIN SIMU_FUND_CHECK_ORDER s on t.deal_dtl_no = s.deal_dtl_no
    WHERE 1=1
      <if test="ackDt != null">
        and t.ack_dt = #{ackDt,jdbcType=VARCHAR}
      </if>
      <if test="continuanceFlag != null">
        and t.continuance_flag = #{continuanceFlag,jdbcType=VARCHAR}
      </if>
      <if test="taCode != null">
        and t.TA_CODE = #{taCode,jdbcType=VARCHAR}
      </if>
      <if test="dealDtlNos != null and dealDtlNos.size() > 0">
        and t.DEAL_DTL_NO IN
        <foreach collection="dealDtlNos" item="item" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
  </select>

  <update id="updatePayOutStatusAndDtForSplit" parameterType="map">
    update HIGH_REDEEM_SPLIT_DTL t
    set t.PAY_OUT_STATUS = #{orderPayStatus,jdbcType=VARCHAR},
    t.PAY_OUT_DT = #{payDt,jdbcType=VARCHAR}
    where t.ORIGIN_SERIAL_NO = #{orgSerialNo,jdbcType=VARCHAR}
  </update>


  <resultMap id="HighOnWayAmtResultMap" type="com.howbuy.tms.high.batch.dao.vo.HighOnWayAmtInfoVo">
    <result column="TX_ACCT_NO" property="txAcctNo" jdbcType="VARCHAR" />
    <result column="PRODUCT_CODE" property="productCode" jdbcType="VARCHAR" />
    <result column="UNCONFIRMED_AMT" property="unconfirmedAmt" jdbcType="DECIMAL" />
  </resultMap>
  <select id="queryOnWayAmtInfo" resultMap="HighOnWayAmtResultMap" parameterType="map">
    select a.TX_ACCT_NO,a.FUND_CODE as PRODUCT_CODE,sum(a.NET_APP_AMT) as UNCONFIRMED_AMT from HIGH_DEAL_ORDER_DTL a
    inner join DEAL_ORDER b on a.DEAL_NO=b.DEAL_NO
      where a.TX_ACCT_NO =#{txAcctNo,jdbcType=VARCHAR}
    and a.FUND_CODE =#{productCode,jdbcType=VARCHAR}
    and a.M_BUSI_CODE in ('1120','1122')
    and b.deal_type = '2'
    and b.order_status = '1'
    and b.pay_status = '4'
    group by a.TX_ACCT_NO,a.FUND_CODE
    </select>
	<select id="queryAllOnWayAmtInfo" resultMap="HighOnWayAmtResultMap" parameterType="map">
		select a.TX_ACCT_NO,a.FUND_CODE as PRODUCT_CODE,sum(a.NET_APP_AMT) as UNCONFIRMED_AMT
		from HIGH_DEAL_ORDER_DTL a
		inner join DEAL_ORDER b on a.DEAL_NO=b.DEAL_NO
		where a.TX_ACCT_NO =#{txAcctNo,jdbcType=VARCHAR}
		  and a.FUND_CODE =#{productCode,jdbcType=VARCHAR}
		  and a.M_BUSI_CODE in ('1120','1122')
		  and b.deal_type = '2'
		  and b.order_status = '1'
		group by a.TX_ACCT_NO,a.FUND_CODE
	</select>
  <update id="updateStageFlag" parameterType="map">
    update HIGH_DEAL_ORDER_DTL t
    set t.STAGE_FLAG = '1'
    where  t.FUND_CODE in
    <foreach item="item" index="index" collection="fundCodes" open="(" separator="," close=")">
      #{item}
    </foreach>
    and t.ACK_DT = #{ackDt,jdbcType=VARCHAR}
  </update>

  <select id="getAllRedeemSplitOrder"  parameterType="map" resultMap="HighDealDtlAndCheckOrderResultMap">
    SELECT
    t.deal_no           ,
    t.deal_dtl_no       ,
    t.origin_serial_no  ,
    t.fund_code         ,
    t.fund_name         ,
    t.ack_dt            ,
    t.app_vol           ,
    t.ack_vol           ,
    t.ack_amt           ,
    t.nav               ,
    t.pay_out_status    ,
    t.pay_out_dt        ,
    t.tx_acct_no        ,
    t.cp_acct_no        ,
    t.fee               ,
    t.join_dt           ,
    s.submit_deal_no
    FROM HIGH_DEAL_ORDER_DTL t
    INNER JOIN SIMU_FUND_CHECK_ORDER s on t.deal_dtl_no = s.deal_dtl_no
    WHERE t.ack_dt = #{tradeDt,jdbcType=VARCHAR}
    and t.TA_CODE = #{taCode,jdbcType=VARCHAR}
    and t.M_BUSI_CODE = '1124'
    and (t.continuance_flag = '1' or t.STAGE_FLAG='1')
  </select>
  <resultMap id="SpecialProductOrderResultMap"
             type="com.howbuy.tms.high.batch.dao.po.order.SpecialProductOrderPo"
             extends="com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper.BaseResultMap">
    <result column="TX_CHANNEL" jdbcType="VARCHAR" property="txChannel" />
    <result column="INVST_TYPE" jdbcType="VARCHAR" property="invstType" />
    <result column="contract_version" jdbcType="VARCHAR" property="contractVersion" />
  </resultMap>

  <select id="getSpecialProductOrder" resultMap="SpecialProductOrderResultMap" parameterType="map">
    SELECT T.DEAL_NO,
    T.CREATE_DTM,
    T.FUND_CODE,
    T.TX_ACCT_NO,
    T.PRODUCT_CHANNEL,
    T.contract_version,
    T.TA_CODE,
    s.TX_CHANNEL,
    s.INVST_TYPE
    FROM HIGH_DEAL_ORDER_DTL T
    LEFT JOIN DEAL_ORDER_EXTEND E
    ON t.DEAL_NO = e.DEAL_NO
    LEFT JOIN SIMU_FUND_CHECK_ORDER S
    ON s.DEAL_NO = t.Deal_No
    WHERE (e.REC_STAT = '0' OR e.REC_STAT IS NULL)
    AND s.SUBMIT_APP_FLAG = '2'
    and t.m_busi_code in ('1120','1122')
    and (t.main_deal_order_no is null or t.main_deal_order_no = t.deal_no)
    AND t.DEAL_NO = #{dealNo,jdbcType=VARCHAR}
  </select>
	<select id="selectRedeemDealOrder" resultMap="com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper.BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
		from HIGH_DEAL_ORDER_DTL o
		where o.ACK_DT=#{tradeDt,jdbcType=VARCHAR}
		and FUND_TYPE='11' and FUND_SUB_TYPE='C' and M_BUSI_CODE='1142'
	</select>
	<select id="updateCxgDealNoByOriginSerialNo" parameterType="map">
		update HIGH_DEAL_ORDER_DTL t
		set t.CXG_DEAL_NO =  #{cxgOrderNo,jdbcType=VARCHAR}
		where t.ORIGIN_SERIAL_NO= #{busiDealNo,jdbcType=VARCHAR}
	</select>
	<select id="updateCxgDealNoByDealNo" parameterType="map">
		update HIGH_DEAL_ORDER_DTL t
		set t.CXG_DEAL_NO =  #{cxgOrderNo,jdbcType=VARCHAR}
		where t.DEAL_NO= #{dealNo,jdbcType=VARCHAR}
	</select>

	<select id="queryOwnershipRightTransferOrder" resultMap="com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper.BaseResultMap" parameterType="com.howbuy.tms.high.batch.dao.vo.QueryOwnershipRightTransferVo">
		select
		<include refid="com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper.Base_Column_List" />
		from HIGH_DEAL_ORDER_DTL o
		where
		1=1
		<if test="txAcctNo != null">
			and o.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
		</if>
		<if test="fundCode != null">
			and o.FUND_CODE = #{fundCode,jdbcType=VARCHAR}
		</if>
		<if test="dealDtlNoList != null and dealDtlNoList.size() > 0">
			and o.DEAL_DTL_NO IN
			<foreach collection="dealDtlNoList" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="filterDealDtlNoList != null and filterDealDtlNoList.size() > 0">
			and o.DEAL_DTL_NO not IN
			<foreach collection="filterDealDtlNoList" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="mBusinessCodeList != null and mBusinessCodeList.size() > 0">
			and o.M_BUSI_CODE IN
			<foreach collection="mBusinessCodeList" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="fundType != null">
			and o.FUND_TYPE = #{fundType,jdbcType=VARCHAR}
		</if>
		<if test="fundSubType != null">
			and o.FUND_SUB_TYPE = #{fundSubType,jdbcType=VARCHAR}
		</if>
		<if test="endDtm != null">
			and o.ACK_DT <![CDATA[ <= ]]> #{endDtm,jdbcType=VARCHAR}
		</if>
		<if test="beginDtm != null">
			and o.ACK_DT <![CDATA[ >= ]]> #{beginDtm,jdbcType=VARCHAR}
		</if>

		<if test="checkFlag != null and checkFlag ==0">
			and o.IS_NO_TRADE_TRANSFER !='1'
			and o.TRANSFER_PRICE is null
		</if>
		<if test="checkFlag != null and checkFlag ==2">
			and (o.IS_NO_TRADE_TRANSFER ='1'
			or o.TRANSFER_PRICE is not null)
		</if>
	</select>

  <select id="selectMergeSubmitOrderList" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.MergeSubmitOrderVo">
	  select sum(t1.APP_AMT) as appAmt,
	  sum(t1.APP_VOL) as appVol,
	  GROUP_CONCAT(t1.ORDER_STATUS ORDER BY t1.DEAL_NO SEPARATOR ',') AS orderStatus,
	  GROUP_CONCAT(t1.PAY_STATUS ORDER BY t1.DEAL_NO SEPARATOR ',') AS payStatus,
	  t2.MAIN_DEAL_ORDER_NO as mainDealOrderNo
	  from DEAL_ORDER t1
	  inner join HIGH_DEAL_ORDER_DTL t2
	  on t1.deal_no = t2.deal_no
	  where t2.main_deal_order_no in
	  <if test="mainDealNos != null and mainDealNos.size() > 0 ">
		  <foreach collection="mainDealNos" index="index" item="mainDealNo" open="(" separator="," close=")">
			  #{mainDealNo}
		  </foreach>
	  </if>
	  group by t2.main_deal_order_no
    </select>

	<!-- 自定义ResultMap -->
	<resultMap id="AppAckFlagResultMap" type="com.howbuy.tms.high.batch.dao.vo.AppACKFlagVo">
		<result column="TX_APP_FLAG" jdbcType="CHAR" property="txAppFlag" />
		<result column="TX_ACK_FLAG" jdbcType="CHAR" property="txAckFlag" />
	</resultMap>

	<!-- 根据订单号统计订单号下对应订单明细各种交易状态、确认状�?-->
	<select id="selectAppACKFlag" resultMap="AppAckFlagResultMap">
		SELECT TX_APP_FLAG,TX_ACK_FLAG
		FROM HIGH_DEAL_ORDER_DTL
		WHERE deal_no = #{dealNo,jdbcType=VARCHAR}
	</select>

	<select id="queryDealList" parameterType="map" resultType="com.howbuy.tms.high.batch.dao.vo.HighDealReportVo">
		select
		t1.SUBMIT_TA_DT as submitTaDt,
		t1.M_BUSI_CODE as mBusiCode,
		t1.FUND_CODE as fundCode,
		t1.FUND_NAME as fundName,
		t1.TX_ACCT_NO as txAcctNo,
		t1.APP_AMT as appAmt,
		t1.NET_APP_AMT as appNetAmt,
		t1.APP_VOL as appVol,
		t1.FEE as fee,
		t2.PAY_STATUS as payStatus,
		t1.TA_CODE as taCode,
		t2.CUST_NAME as custName
		from HIGH_DEAL_ORDER_DTL t1
		left join DEAL_ORDER t2 on t1.DEAL_NO=t2.DEAL_NO
		where t1.SUBMIT_TA_DT=#{submitTaDt,jdbcType=VARCHAR}
		and t1.FUND_CODE=#{fundCode,jdbcType=VARCHAR}
		and t1.M_BUSI_CODE=#{mBusiCode,jdbcType=VARCHAR}
		and t2.ORDER_STATUS in ('1','2','3')
	</select>
</mapper>