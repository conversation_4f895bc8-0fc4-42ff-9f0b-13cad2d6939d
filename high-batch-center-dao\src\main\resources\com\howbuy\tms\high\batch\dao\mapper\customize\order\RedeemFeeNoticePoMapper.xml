<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.tms.high.batch.dao.mapper.customize.order.RedeemFeeNoticePoMapper">
    <resultMap id="queryRedeemFeeNoticeMap" type="com.howbuy.tms.high.batch.dao.vo.RedeemNoticeOrderVo">
        <result column="data_dt" jdbcType="VARCHAR" property="dataDt"/>
        <result column="deal_no" jdbcType="VARCHAR" property="dealNo"/>
        <result column="tx_acct_no" jdbcType="VARCHAR" property="txAcctNo"/>
        <result column="cust_name" jdbcType="VARCHAR" property="custName"/>
        <result column="m_busi_code" jdbcType="VARCHAR" property="mBusiCode"/>
        <result column="fund_code" jdbcType="VARCHAR" property="fundCode"/>
        <result column="fund_name" jdbcType="VARCHAR" property="fundName"/>
        <result column="is_cycle_product" jdbcType="CHAR" property="isCycleProduct"/>
        <result column="apply_vol" jdbcType="DECIMAL" property="applyVol"/>
        <result column="trade_dt" jdbcType="VARCHAR" property="tradeDt"/>
        <result column="trade_tm" jdbcType="VARCHAR" property="tradeTm"/>
        <result column="estimate_data_source" jdbcType="VARCHAR" property="estimateDataSource"/>
        <result column="is_multi_card" jdbcType="CHAR" property="isMultiCard"/>
        <result column="current_period_open_dt" jdbcType="VARCHAR" property="currentPeriodOpenDt"/>
        <result column="current_period_detail" jdbcType="VARCHAR" property="currentPeriodDetail"/>
        <result column="next_period_open_dt" jdbcType="VARCHAR" property="nextPeriodOpenDt"/>
        <result column="next_period_detail" jdbcType="VARCHAR" property="nextPeriodDetail"/>
        <result column="notice_status" jdbcType="VARCHAR" property="noticeStatus"/>
        <result column="submit_app_flag" jdbcType="VARCHAR" property="submitAppFlag"/>
    </resultMap>
    <select id="queryRedeemFeeNotice" parameterType="map" resultMap="queryRedeemFeeNoticeMap">
        select t1.deal_no,
               t1.tx_acct_no,
               t1.cust_name,
               t1.m_busi_code,
               t1.fund_code,
               t1.fund_name,
               t1.is_cycle_product,
               t1.apply_vol,
               t1.trade_dt,
               t1.trade_tm,
               t1.estimate_data_source,
               t1.is_multi_card,
               t1.current_period_open_dt,
               t1.current_period_detail,
               t1.next_period_open_dt,
               t1.next_period_detail,
               t1.notice_status,
               t1.data_dt,
               t2.submit_app_flag
        from redeem_fee_notice t1
                 left join simu_fund_check_order t2 on t1.deal_no = t2.deal_no
        where t1.is_delete = '0'
          and t1.notice_status != '0'
        <if test="param.submitAppFlagList != null and param.submitAppFlagList.size() > 0">
            and (t2.submit_app_flag IN
            <foreach collection="param.submitAppFlagList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <bind name="status" value="param.needQueryWithOutSubmitAppFlag.toString()"/>
            <if test='status == "1"'>
                or t2.submit_app_flag is null
            </if>)
        </if>
        <if test="param.queryStartDt != null">
            AND t1.data_dt <![CDATA[ >= ]]> #{param.queryStartDt,jdbcType=VARCHAR}
        </if>
        <if test="param.queryEndDt != null">
            AND t1.data_dt <![CDATA[ <= ]]> #{param.queryEndDt,jdbcType=VARCHAR}
        </if>
        <if test="param.fundCodeList != null and param.fundCodeList.size() > 0">
            AND t1.fund_code IN
            <foreach collection="param.fundCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>