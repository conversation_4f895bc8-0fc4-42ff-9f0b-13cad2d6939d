/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 * <p>
 * Copyright (c) 2016, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 *Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.piggyfrzpay;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.constant.OutReturnCodes;
import com.howbuy.tms.common.enums.TxPmtFlagEnum;
import com.howbuy.tms.common.enums.busi.PiggyPayWayEnum;
import com.howbuy.tms.common.enums.database.PaymentTypeEnum;
import com.howbuy.tms.common.outerservice.ftxonline.piggypay.PiggyPayContext;
import com.howbuy.tms.common.outerservice.ftxonline.piggypay.PiggyPayOuterService;
import com.howbuy.tms.common.outerservice.ftxonline.piggypay.PiggyPayResult;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.batch.dao.po.order.DealOrderPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.dao.po.order.PaymentOrderPo;
import com.howbuy.tms.high.batch.service.business.pmtprocess.ProcessPmtResultService;
import com.howbuy.tms.high.batch.service.logic.ComplianceSubmitCheckLogicService;
import com.howbuy.tms.high.batch.service.logic.bean.ComplianceSubmitCheckResultDto;
import com.howbuy.tms.high.batch.service.repository.DealOrderRepository;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.repository.PaymentOrderRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 *
 * @description:(储蓄罐冻结支付)
 * @reason:
 * <AUTHOR>
 * @date 2018年5月18日 下午4:23:48
 * @since JDK 1.6
 */
@Service("DoPiggyFrzPayService")
public class DoPiggyFrzPayService {
    Logger logger = LogManager.getLogger(DoPiggyFrzPayService.class);

    @Autowired
    private PiggyPayOuterService piggyPayOuterService;

    @Autowired
    private PaymentOrderRepository paymentOrderRepository;

    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;

    @Autowired
    private DealOrderRepository dealOrderRepository;

    @Autowired
    private ProcessPmtResultService processPmtResultService;

    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;

    @Autowired
    private ComplianceSubmitCheckLogicService complianceSubmitCheckLogicService;

    private static final String DAILY_TIME = "150000";
    private static final String DAILY_PRE_TIME = "145900";

    /**
     * process:(储蓄罐冻结异步支付处理)
     * @param pmtDealNo
     * <AUTHOR>
     * @date 2018年5月18日 下午1:39:36
     */
    public void process(String pmtDealNo) {
        PaymentOrderPo paymentOrder = paymentOrderRepository.getByPmtDealNo(pmtDealNo);

        if (paymentOrder == null) {
            logger.error("DoAsyncPaymentService|process|paymentOrder is not exist, pmtDealNo:{} ", pmtDealNo);
            return;
        }
        if (!TxPmtFlagEnum.PIGGY_FRZ_SUCC.getKey().equals(paymentOrder.getTxPmtFlag())) {
            logger.error("DoAsyncPaymentService|process|paymentOrder txPmtFlag error, pmtDealNo:{}, txPmtFlag:{}", pmtDealNo, paymentOrder.getTxPmtFlag());
            return;
        }

        List<HighDealOrderDtlPo> highDealOrderDtlList = highDealOrderDtlRepository.getByDealNo(paymentOrder.getDealNo());
        if (CollectionUtils.isEmpty(highDealOrderDtlList)) {
            logger.error("DoAsyncPaymentService|process|highDealOrderDtlPo is not exist, dealNo:{} ", paymentOrder.getDealNo());
            return;
        }
        HighDealOrderDtlPo highDealOrderDtl = highDealOrderDtlList.get(0);


        // 群济私募产品，在支付之前需要调用群济查询客户信息接口，同步客户账户信息
        DealOrderPo dealOrder = dealOrderRepository.getByDealNo(paymentOrder.getDealNo());

        Date currDate = new Date();

        // 合规校验,如果校验不通过,先不发起冻结支付,不然影响客户收益
        ComplianceSubmitCheckResultDto complianceSubmitCheckResultDto = complianceSubmitCheckLogicService.complianceSubmitCheck(highDealOrderDtlList, currDate);
        logger.info("合规校验结果:{}", JSON.toJSONString(complianceSubmitCheckResultDto));
        if (!complianceSubmitCheckResultDto.checkAllPass()) {
            logger.info("DoPiggyFrzPayService-冻结支付,合规校验不通过,不能发起冻结支付,pmtDealNo={}", pmtDealNo);
            return;
        }

        // 根据支付方式发起支付，更新支付标识和对账标识
        TxPmtFlagEnum paymentStatus = doPayment(highDealOrderDtl, paymentOrder, dealOrder, currDate);
        if (paymentStatus == null) {
            return;
        }
        logger.info("DoAsyncPaymentService|process|pmtDealNo:{}, paymentStatus:{}", pmtDealNo, paymentStatus);

        // 处理支付结果
        processPmtResultService.process(dealOrder, paymentOrder, highDealOrderDtl, paymentStatus.getKey(), true);
    }


    /**
     * doPayment:支付流程
     *
     * @param highDealOrderDtlPo
     *            高端订单明细
     * @param pmtOrder
     *            支付明细订单
     * @param order
     *            交易主订单
     * @return TxPmtFlagEnum 交易支付标记
     * <AUTHOR>
     * @date 2016年9月21日 下午2:19:21
     */
    public TxPmtFlagEnum doPayment(HighDealOrderDtlPo highDealOrderDtlPo, PaymentOrderPo pmtOrder, DealOrderPo order, Date currDate) {

        if (PaymentTypeEnum.PIGGY.getCode().equals(pmtOrder.getPaymentType())) {

            PiggyPayResult rslt = doPiggeFrzPayment(pmtOrder, order, currDate);
            // 回填储蓄罐支付信息
            pmtOrder.settPmtDealNo(rslt.getTransNo());
            pmtOrder.setRetCode(rslt.getErrorCode());
            pmtOrder.setRetDesc(rslt.getErrorMsg());

            String taTradeDt = queryTradeDayOuterService.getWorkDay(currDate);
            pmtOrder.setPmtCheckDt(taTradeDt);

            if (TxPmtFlagEnum.SUCCESSFUL.equals(rslt.getPaymentStatus())) {

                // 根据储蓄罐返回的实际支付日期，设置payment_order支付完成日期
                String appDt = DateUtils.formatToString(currDate, DateUtils.YYYYMMDD);
                String appTm = DateUtils.formatToString(currDate, DateUtils.HHMMSS);
                if (appTm.compareTo(DAILY_TIME) >= 0) {
                    if (rslt.getTaTradeDt() != null) {
                        if (rslt.getTaTradeDt().compareTo(appDt) > 0) {
                            pmtOrder.setPmtCompleteDtm(currDate);
                        } else {
                            pmtOrder.setPmtCompleteDtm(DateUtils.formatToDate(appDt + DAILY_PRE_TIME,
                                    DateUtils.YYYYMMDDHHMMSS));
                        }
                    }
                }

                // 设置payment_order对账日期
                if (rslt.getTaTradeDt() != null) {
                    pmtOrder.setPmtCheckDt(rslt.getTaTradeDt());
                }

                return TxPmtFlagEnum.PIGGY_FRZ_PAY_SUCC;
            } else if (TxPmtFlagEnum.FAILED.equals(rslt.getPaymentStatus())) {
                return TxPmtFlagEnum.PIGGY_FRZ_PAY_FAIL;
            } else {
                return TxPmtFlagEnum.PIGGY_FRZ_PAYING;
            }


        }
        return null;
    }


    /**
     *
     * doPiggePayment:做储蓄罐支付
     *
     * @param pmtOrder
     *            支付订单
     * @param order
     *            交易主订单
     * @return
     * @return PiggyPayResult
     * <AUTHOR>
     * @date 2016年12月7日 上午10:25:34
     */
    public PiggyPayResult doPiggeFrzPayment(PaymentOrderPo pmtOrder, DealOrderPo order, Date currDate) {

        paymentOrderRepository.updateIfPmtModeIsPiggyFrzPay(pmtOrder.getPmtDealNo());

        // 创建储蓄罐支付上下文
        PiggyPayContext ctx = new PiggyPayContext();

        String appDt = DateUtils.formatToString(currDate, DateUtils.YYYYMMDD);
        String appTm = DateUtils.formatToString(currDate, DateUtils.HHMMSS);

        // 判断时间是否大于15点，若大于，则取值为145900
        if (appTm.compareTo(DAILY_TIME) >= 0) {
            appTm = DAILY_PRE_TIME;
        }

        ctx.setAppAmt(pmtOrder.getPmtAmt());
        ctx.setAppDt(appDt);
        ctx.setAppTm(appTm);
        ctx.setContractNo(pmtOrder.getPmtDealNo());
        ctx.setCustBankId(pmtOrder.getCpAcctNo());
        ctx.setCustNo(pmtOrder.getTxAcctNo());
        ctx.setFundAttr(order.getProductName());// 基金简称
        ctx.setFundCode(order.getProductCode());
        ctx.setOutletCode(order.getOutletCode()); // 网点号
        ctx.setTradeChan(order.getTxChannel());
        ctx.setDisCode(order.getDisCode());
        ctx.setProductChannel(pmtOrder.getProductChannel());
        ctx.setPaymentWay(PiggyPayWayEnum.FRZ_PAY.getCode());// 冻结支付
        ctx.setDealNo(pmtOrder.gettPmtDealNo());// 储蓄罐订单号
        ctx.setTradeDayFlag("1"); // 交易日标志，若请求上送该值，储蓄罐则会应答实际交易日

        // 执行支付行为
        PiggyPayResult rslt = piggyPayOuterService.pay(ctx);
        // 并发异常需要将支付订单状态改成未支付
        if (OutReturnCodes.DUBBO_PIGGY_CONCURRENT_ERROR.equals(rslt.getErrorCode())) {
            // 将支付订单从支付中改成未支付
            paymentOrderRepository.updatePayingToFrzSucc(pmtOrder.getPmtDealNo());

            // 并发异常，让MQ消息重新被消费
            throw new BusinessException(ExceptionCodes.BATCH_CENTER_CONCURRENT_ERROR, "并发异常");
        }

        if (!rslt.isCallSuccess()) {
            throw new BusinessException(ExceptionCodes.BATCH_CENTER_ACCESS_DUBBO_INTERFACE_FAILED, "储蓄罐支付失败");
        }
        // 更新订单明细上的储蓄罐订单号
        if (StringUtils.isNotEmpty(rslt.getOutPayNo())) {
            highDealOrderDtlRepository.updateCxgDealNoByDealNo(order.getDealNo(), rslt.getOutPayNo());
        }
        return rslt;

    }


}
