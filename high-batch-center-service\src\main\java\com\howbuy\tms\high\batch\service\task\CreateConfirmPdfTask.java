package com.howbuy.tms.high.batch.service.task;

import com.howbuy.tms.high.batch.service.business.volconfirmbook.VolConfirmBookService;
import com.howbuy.tms.high.batch.service.common.utils.AbstractHowbuyBaseTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @Description:创建份额确认书
 * @Author: yun.lu
 * Date: 2025/4/9 18:01
 */
@Data
@AllArgsConstructor
public class CreateConfirmPdfTask extends AbstractHowbuyBaseTask {
    private static Logger logger = LogManager.getLogger(CreateConfirmPdfTask.class);
    public VolConfirmBookService volConfirmBookService;

    @Override
    protected void callTask() {
        volConfirmBookService.execute();
    }
}
