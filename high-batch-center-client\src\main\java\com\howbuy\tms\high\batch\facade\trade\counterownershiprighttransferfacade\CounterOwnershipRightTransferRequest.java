package com.howbuy.tms.high.batch.facade.trade.counterownershiprighttransferfacade;

import com.howbuy.tms.common.client.BaseRequest;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.high.batch.facade.trade.counterownershiprighttransferfacade.bean.CounterOwnershipRightTransferBean;
import lombok.Data;

/**
 * @Description:股权份额转让请求入参
 * @Author: yun.lu
 * Date: 2023/5/19 14:47
 */
@Data
public class CounterOwnershipRightTransferRequest extends BaseRequest {

    public CounterOwnershipRightTransferRequest(){
        this.setTxCode(TxCodes.COUNTER_OWNERSHIP);
    }


    /**
     * 入参
     */
    private CounterOwnershipRightTransferBean counterOwnershipRightTransferBean;

}
