---
description: 高端批处理中心项目级别规范指南 (High-Batch-Center Project Guidelines)
globs:
alwaysApply: true
---
# 高端批处理中心项目级别规范指南 (High-Batch-Center Project Guidelines)

## 项目概述 (Project Overview)
高端批处理中心(high-batch-center)是负责处理好买金融高端基金产品批量交易处理的核心系统，包含批量处理、日终处理、对账、确认等功能。项目采用微服务架构，基于Spring Boot + Dubbo实现。

High-Batch-Center is the core system responsible for processing batch transactions of Howbuy Financial's high-end fund products, including batch processing, end-of-day processing, reconciliation, and confirmation functions. The project adopts a microservice architecture based on Spring Boot + Dubbo.

项目配置 (Project Configuration):
  编程语言 (Programming Language): Java
  JDK版本 (JDK Version): 1.8
  框架 (Framework): Spring Boot 2.3.12, Spring Cloud Hoxton.SR12
  数据库 (Database): MySQL 8.0
  缓存 (Cache): Redis
  数据库操作 (Database Operation): Mybatis
  数据库连接池 (Database Connection Pool): Druid
  消息中间件 (Message Middleware): RocketMQ
  分布式服务 (Distributed Service): Dubbo 3.2.12
  注册中心 (Registry Center): ZooKeeper 3.4.13

### 模块划分 (Module Structure)
- **high-batch-center-client**: 包含Dubbo接口定义和出入参定义 (Contains Dubbo interface definitions and request/response parameters)
  - 基础包名 (Base Package): `com.howbuy.tms.high.batch.facade`
  - 接口定义 (Interface Definition): `com.howbuy.tms.high.batch.facade.trade`和`com.howbuy.tms.high.batch.facade.query`
  - 交易类接口 (Transaction Interfaces): `com.howbuy.tms.high.batch.facade.trade.*`
  - 查询类接口 (Query Interfaces): `com.howbuy.tms.high.batch.facade.query.*`
  - 枚举类 (Enums): `com.howbuy.tms.high.batch.enums`
  - 接口入参 (Request Parameters): `com.howbuy.tms.high.batch.facade.*.request`或`com.howbuy.tms.high.batch.facade.*.bean`
  - 接口出参 (Response Parameters): `com.howbuy.tms.high.batch.facade.*.response`
  - 公共类 (Common Classes): `com.howbuy.tms.high.batch.facade.common`

- **high-batch-center-service**: 包含业务逻辑实现 (Contains business logic implementation)
  - 基础包名 (Base Package): `com.howbuy.tms.high.batch.service`
  - Dubbo接口实现 (Dubbo Interface Implementation): `com.howbuy.tms.high.batch.service.facade`
  - 业务实现 (Business Implementation): `com.howbuy.tms.high.batch.service.service`
  - 数据访问层 (Data Access Layer): `com.howbuy.tms.high.batch.service.repository`
  - 业务处理 (Business Processing): `com.howbuy.tms.high.batch.service.business`
  - 切面处理 (Aspect Processing): `com.howbuy.tms.high.batch.service.aspect`
  - 配置处理 (Configuration): `com.howbuy.tms.high.batch.service.config`
  - 定时任务 (Scheduled Tasks): `com.howbuy.tms.high.batch.service.job`
  - 文件处理 (File Processing): `com.howbuy.tms.high.batch.service.file`
  - 公共工具 (Common Utilities): `com.howbuy.tms.high.batch.service.common`

- **high-batch-center-dao**: 包含数据库操作和ORM相关配置 (Contains database operations and ORM configurations)
  - 基础包名 (Base Package): `com.howbuy.tms.high.batch.dao`
  - 数据库操作 (Database Operations): `com.howbuy.tms.high.batch.dao.mapper`
  - 数据库实体 (Database Entities): `com.howbuy.tms.high.batch.dao.po`
  - 视图对象 (View Objects): `com.howbuy.tms.high.batch.dao.vo`

- **high-batch-center-remote**: 批处理服务远程调用模块 (Remote invocation module for batch processing services)
  - 基础包名 (Base Package): `com.howbuy.tms.high.batch.remote`
  - 启动类 (Bootstrap Class): `com.howbuy.tms.high.batch.remote.main.HighBatchCenterApplication`
  - 配置目录 (Configuration Directory): `src/main/resources/`

## 命名规范 (Naming Conventions)

### 通用命名规则 (General Naming Rules)
- **类名 (Class Names)**: 使用PascalCase（首字母大写的驼峰命名法），如`CounterEndService`、`HighDealOrderRepository`
  (Use PascalCase, e.g., `CounterEndService`, `HighDealOrderRepository`)
- **方法名和变量名 (Method and Variable Names)**: 使用camelCase（首字母小写的驼峰命名法），如`queryByTradeDt`、`dealAppNo`
  (Use camelCase, e.g., `queryByTradeDt`, `dealAppNo`)
- **常量 (Constants)**: 使用大写字母和下划线分隔，如`MAX_RETRY_COUNT`、`DEFAULT_TIMEOUT`
  (Use uppercase letters with underscores, e.g., `MAX_RETRY_COUNT`, `DEFAULT_TIMEOUT`)
- **包名 (Package Names)**: 全小写，使用点分隔，如`com.howbuy.tms.high.batch.service`
  (Use lowercase with dots, e.g., `com.howbuy.tms.high.batch.service`)

### 特定组件命名规则 (Specific Component Naming Rules)

#### 接口和实体类 (Interfaces and Entity Classes)
- **Dubbo接口 (Dubbo Interfaces)**: 以`Facade`结尾，如`CounterEndFacade`、`QueryHighWorkdayFacade`
  (End with `Facade`, e.g., `CounterEndFacade`, `QueryHighWorkdayFacade`)
- **接口入参 (Request Parameters)**: 接口名+`Request`，如`CounterEndRequest`、`QueryHighWorkdayRequest`
  (Interface name + `Request`, e.g., `CounterEndRequest`, `QueryHighWorkdayRequest`)
- **接口出参 (Response Parameters)**: 接口名+`Response`，如`CounterEndResponse`、`QueryHighWorkdayResponse`
  (Interface name + `Response`, e.g., `CounterEndResponse`, `QueryHighWorkdayResponse`)
- **数据库实体 (Database Entities)**: 表名+`Po`/`PO`，如`HighDealOrderDtlPo`、`CustBooksPo`
  (Table name + `Po`/`PO`, e.g., `HighDealOrderDtlPo`, `CustBooksPo`)
- **业务对象 (Business Objects)**: 业务功能+`BO`，如`CounterPurchaseOrderBean`、`TradeInfoBO`
  (Business function + `BO`, e.g., `CounterPurchaseOrderBean`, `TradeInfoBO`)
- **视图对象 (View Objects)**: 业务功能+`VO`，如`QueryCounterOrderVo`、`CustFundVolVo`
  (Business function + `VO`, e.g., `QueryCounterOrderVo`, `CustFundVolVo`)

#### 服务层组件 (Service Layer Components)
- **Dubbo实现类 (Dubbo Implementations)**: 接口名+`FacadeService`或`FacadeImpl`，如`CounterEndFacadeService`、`QueryHighWorkdayFacadeImpl`
  (Interface name + `FacadeService` or `FacadeImpl`, e.g., `CounterEndFacadeService`, `QueryHighWorkdayFacadeImpl`)
- **Service类 (Service Classes)**: 业务功能+`Service`，如`AckProcessService`、`SuppleSubsService`
  (Business function + `Service`, e.g., `AckProcessService`, `SuppleSubsService`)
- **Repository类 (Repository Classes)**: 表名+`Repository`，如`DealOrderRepository`、`CustBooksRepository`
  (Table name + `Repository`, e.g., `DealOrderRepository`, `CustBooksRepository`)
- **Mapper接口 (Mapper Interfaces)**: 表名+`Mapper`或`PoMapper`，如`HighDealOrderDtlPoMapper`、`CustBooksPoMapper`
  (Table name + `Mapper` or `PoMapper`, e.g., `HighDealOrderDtlPoMapper`, `CustBooksPoMapper`)

## 接口定义规范 (Interface Definition Standards)

### Dubbo接口定义 (Dubbo Interface Definition)
1. 接口必须继承`BaseFacade<Request, Response>` (Interfaces must extend `BaseFacade<Request, Response>`)
2. 接口必须使用标准注释，包含以下内容 (Interfaces must use standard comments including):
   - 版权声明 (Copyright statement)
   - 接口描述 (`@description`)
   - 作者 (`@author`)
   - 日期 (`@date`)
   - JDK版本 (`@since JDK 1.x`)

```java
/**
 *Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.high.batch.facade.trade.example;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * @description:(示例接口)
 * <AUTHOR>
 * @date 2023年3月30日 下午4:16:20
 * @since JDK 1.8
 */
public interface ExampleFacade extends BaseFacade<ExampleRequest, ExampleResponse> {

}
```

### 入参/出参定义规范 (Request/Response Definition Standards)
1. 请求类必须继承`BaseRequest`、`BatchBaseRequest`或其他合适的基类 (Request classes must extend `BaseRequest`, `BatchBaseRequest`, or other appropriate base classes)
2. 响应类必须继承`BaseResponse`、`BatchBaseResponse`或其他合适的基类 (Response classes must extend `BaseResponse`, `BatchBaseResponse`, or other appropriate base classes)
3. 所有字段必须有注释说明用途 (All fields must have comments explaining their purpose)
4. 使用标准的getter/setter方法或Lombok注解(`@Getter`、`@Setter`、`@Data`) (Use standard getter/setter methods or Lombok annotations)
5. 字段命名遵循Java驼峰命名法 (Field naming follows Java camelCase convention)
6. 敏感字段应使用合适的序列化/反序列化策略 (Sensitive fields should use appropriate serialization/deserialization strategies)

```java
/**
 * @description:(示例请求)
 * <AUTHOR>
 * @date 2023年3月30日 下午4:16:49
 * @since JDK 1.8
 */
public class ExampleRequest extends BatchBaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 交易日期
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易日期", isRequired = false, max = 8)
    private String tradeDt;

    /**
     * TA代码列表
     */
    private List<String> taCodes;

    public ExampleRequest() {
        super.setTxCode(TxCodes.EXAMPLE_TX_CODE);
    }

    // getter和setter方法
}
```

## 接口实现规范 (Interface Implementation Standards)

### Dubbo接口实现类 (Dubbo Interface Implementation Classes)
1. 实现类必须位于`com.howbuy.tms.high.batch.service.facade`包下，子包结构应与接口包保持一致 (Implementation classes must be in the `com.howbuy.tms.high.batch.service.facade` package, with subpackage structure matching the interface package)
2. 实现类命名为接口名+`FacadeService`或`FacadeImpl` (Implementation classes should be named as interface name + `FacadeService` or `FacadeImpl`)
3. 使用`@DubboService`注解暴露服务 (Use `@DubboService` annotation to expose the service)
4. 使用`@Service`注解将服务注册到Spring容器 (Use `@Service` annotation to register the service to the Spring container)
5. 通过`@Autowired`或`@Resource`注入所需的Service类 (Inject required Service classes via `@Autowired` or `@Resource`)
6. 方法实现应简洁，主要负责参数校验和Service层的调用，不包含具体业务逻辑 (Method implementations should be concise, mainly responsible for parameter validation and Service layer invocation, not containing specific business logic)
7. 继承`AbstractService<Request, Response>`基类 (Extend the `AbstractService<Request, Response>` base class)

```java
/**
 * @description:(示例接口实现)
 * <AUTHOR>
 * @date 2023年3月30日 下午4:16:49
 * @since JDK 1.8
 */
@DubboService
@Service("exampleFacade")
public class ExampleFacadeService extends AbstractService<ExampleRequest, ExampleResponse> implements ExampleFacade {
    @Autowired
    private ExampleService exampleService;

    @Override
    public ExampleResponse process(ExampleRequest request) {
        // 参数校验

        // 调用业务服务处理
        ExampleResponse response = exampleService.process(request);
        response.setReturnCode(ExceptionCodes.SUCCESS);
        return response;
    }
}
```

## 服务层调用规范 (Service Layer Invocation Standards)

### Service层 (Service Layer)
1. Service类应位于`com.howbuy.tms.high.batch.service.service`或`com.howbuy.tms.high.batch.service.business`包下 (Service classes should be in the `com.howbuy.tms.high.batch.service.service` or `com.howbuy.tms.high.batch.service.business` package)
2. 使用`@Service`注解将服务注册到Spring容器 (Use `@Service` annotation to register the service to the Spring container)
3. 使用`@Slf4j`或标准Logger对象进行日志记录 (Use `@Slf4j` or standard Logger objects for logging)
4. 通过`@Autowired`或`@Resource`注入Repository类和其他Service类 (Inject Repository classes and other Service classes via `@Autowired` or `@Resource`)
5. 实现具体业务逻辑，不直接操作数据库 (Implement specific business logic, not directly operating the database)
6. 方法应有完整的注释 (Methods should have complete comments)

```java
/**
 * @description: 示例服务
 * <AUTHOR>
 * @date 2023年3月30日 下午4:16:49
 * @since JDK 1.8
 */
@Service
@Slf4j
public class ExampleService {

    @Autowired
    private ExampleRepository exampleRepository;

    /**
     * @description: 业务处理方法
     * @param request 请求参数
     * @return 处理结果
     * <AUTHOR>
     * @date 2023年3月30日 下午4:16:49
     * @since JDK 1.8
     */
    public ExampleResponse process(ExampleRequest request) {
        log.info("处理示例请求, 参数: {}", request);
        // 业务逻辑实现
        return new ExampleResponse();
    }
}
```

### Repository层 (Repository Layer)
1. Repository类应位于`com.howbuy.tms.high.batch.service.repository`包下 (Repository classes should be in the `com.howbuy.tms.high.batch.service.repository` package)
2. 使用`@Repository`注解将仓库注册到Spring容器 (Use `@Repository` annotation to register the repository to the Spring container)
3. 使用`@Transactional`注解控制事务 (Use `@Transactional` annotation to control transactions)
4. 通过`@Autowired`或`@Resource`注入Mapper接口 (Inject Mapper interfaces via `@Autowired` or `@Resource`)
5. 实现数据库操作逻辑，不包含业务逻辑 (Implement database operation logic, not containing business logic)
6. 方法应有完整的注释 (Methods should have complete comments)

```java
/**
 * @description: 示例数据访问层
 * <AUTHOR>
 * @date 2023年3月30日 下午4:16:49
 * @since JDK 1.8
 */
@Repository
public class ExampleRepository {
    @Autowired
    private ExamplePoMapper examplePoMapper;

    /**
     * @description: 查询数据
     * @param tradeDt 交易日期
     * @param taCode TA代码
     * @return 数据列表
     * <AUTHOR>
     * @date 2023年3月30日 下午4:16:49
     * @since JDK 1.8
     */
    public List<ExamplePo> queryByTradeDate(String tradeDt, String taCode) {
        return examplePoMapper.selectByTradeDate(tradeDt, taCode);
    }

    /**
     * @description: 保存数据
     * @param entity 数据实体
     * @return 影响行数
     * <AUTHOR>
     * @date 2023年3月30日 下午4:16:49
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class)
    public int save(ExamplePo entity) {
        return examplePoMapper.insert(entity);
    }
}
```

## 事务管理规范 (Transaction Management Standards)

### 事务注解使用原则 (Transaction Annotation Usage Principles)
1. 数据修改操作使用`@Transactional(rollbackFor = Exception.class)` (Use `@Transactional(rollbackFor = Exception.class)` for data modification operations)
2. 查询操作不需要添加事务注解 (Query operations do not need transaction annotations)
3. 复杂业务场景可在Service层添加事务控制 (Complex business scenarios can add transaction control at the Service layer)
4. 明确指定回滚异常类型，通常为`Exception.class` (Clearly specify the rollback exception type, usually `Exception.class`)

### 事务传播行为说明 (Transaction Propagation Behavior Description)
- `REQUIRED`: 如果当前存在事务，则加入该事务；如果当前没有事务，则创建一个新的事务 (If a transaction exists, join it; if not, create a new one)
- `SUPPORTS`: 如果当前存在事务，则加入该事务；如果当前没有事务，则以非事务方式执行 (If a transaction exists, join it; if not, execute non-transactionally)
- `MANDATORY`: 如果当前存在事务，则加入该事务；如果当前没有事务，则抛出异常 (If a transaction exists, join it; if not, throw an exception)
- `REQUIRES_NEW`: 创建一个新的事务，如果当前存在事务，则挂起当前事务 (Create a new transaction, suspending the current one if it exists)
- `NOT_SUPPORTED`: 以非事务方式执行，如果当前存在事务，则挂起当前事务 (Execute non-transactionally, suspending the current transaction if it exists)
- `NEVER`: 以非事务方式执行，如果当前存在事务，则抛出异常 (Execute non-transactionally, throwing an exception if a transaction exists)
- `NESTED`: 如果当前存在事务，则创建一个事务作为当前事务的嵌套事务来执行；如果当前没有事务，则等价于`REQUIRED` (If a transaction exists, create a nested transaction; if not, equivalent to `REQUIRED`)

## 注释规范 (Comment Standards)

### 类注释 (Class Comments)
```java
/**
 *Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

/**
 * @description: 类功能描述 (Class function description)
 * <AUTHOR>
 * @date 2023年3月30日 下午4:16:49
 * @since JDK 1.8
 */
```

### 方法注释 (Method Comments)
```java
/**
 * @description: 方法功能描述 (Method function description)
 * @param paramName 参数说明 (Parameter description)
 * @return 返回值说明 (Return value description)
 * <AUTHOR>
 * @date 2023年3月30日 下午4:16:49
 * @since JDK 1.8
 */
```

## 异常处理规范 (Exception Handling Standards)

1. 使用统一的异常处理机制 (Use a unified exception handling mechanism)
2. 业务异常应继承自`BusinessException`或应用的基础异常类 (Business exceptions should inherit from `BusinessException` or the application's base exception class)
3. 合理使用自定义异常和错误码 (Reasonably use custom exceptions and error codes)
4. 异常信息应包含足够的上下文信息，便于问题定位 (Exception information should contain sufficient context for problem location)
5. 不要捕获异常后不处理或仅打印日志 (Don't catch exceptions without handling them or just printing logs)

```java
try {
    // 业务逻辑 (Business logic)
} catch (BusinessException e) {
    log.error("业务处理异常: {}", e.getMessage(), e);
    throw e;
} catch (Exception e) {
    log.error("系统异常: {}", e.getMessage(), e);
    throw new BusinessException(ExceptionCodes.SYSTEM_ERROR, "系统异常", e);
}
```

## 日志规范 (Logging Standards)

1. 使用SLF4J + Log4j2进行日志记录 (Use SLF4J + Log4j2 for logging)
2. 日志级别合理使用 (Reasonable use of log levels):
   - ERROR: 系统错误，需要立即关注的问题 (System errors, issues requiring immediate attention)
   - WARN: 潜在的问题，可能需要关注 (Potential issues that may need attention)
   - INFO: 重要业务操作，可用于生产环境问题跟踪 (Important business operations, can be used for production environment issue tracking)
   - DEBUG: 调试信息，仅在开发和测试环境使用 (Debug information, only used in development and testing environments)

3. 日志内容应包含足够的上下文信息 (Log content should contain sufficient context information)
4. 敏感信息不应记录到日志中，使用`PrivacyUtil`进行脱敏处理 (Sensitive information should not be recorded in logs, use `PrivacyUtil` for desensitization)
5. 使用占位符而非字符串拼接 (Use placeholders instead of string concatenation)

```java
// 正确的做法 (Correct approach)
log.info("处理订单, 订单号: {}, 客户号: {}", orderNo, PrivacyUtil.maskBankCardNo(txAcctNo));

// 错误的做法 (Incorrect approach)
log.info("处理订单, 订单号: " + orderNo + ", 客户号: " + txAcctNo);
```

## 代码审查重点 (Code Review Focus)

在进行代码审查时，应重点关注以下方面 (When conducting code reviews, focus on the following aspects):

1. **命名规范 (Naming Conventions)**: 类名、方法名、变量名是否符合规范 (Whether class names, method names, and variable names comply with standards)
2. **接口定义 (Interface Definition)**: Dubbo接口定义是否符合规范，包括注释、入参出参等 (Whether Dubbo interface definitions comply with standards, including comments, input/output parameters, etc.)
3. **接口实现 (Interface Implementation)**: 实现类是否位于正确的包下，是否使用了正确的注解 (Whether implementation classes are in the correct package and use the correct annotations)
4. **服务层调用 (Service Layer Invocation)**: Service与Repository的职责是否分明，方法是否有必要的注释 (Whether the responsibilities of Service and Repository are clear, and whether methods have necessary comments)
5. **事务管理 (Transaction Management)**: 是否正确使用了事务注解，事务传播行为是否合适 (Whether transaction annotations are used correctly and transaction propagation behavior is appropriate)
6. **异常处理 (Exception Handling)**: 是否有统一的异常处理机制，是否合理使用了自定义异常 (Whether there is a unified exception handling mechanism and custom exceptions are used reasonably)
7. **日志规范 (Logging Standards)**: 是否使用了正确的日志级别，日志内容是否合适，敏感信息是否脱敏 (Whether the correct log levels are used, log content is appropriate, and sensitive information is desensitized)
8. **性能考虑 (Performance Considerations)**: 是否有潜在的性能问题，如N+1查询、大事务等 (Whether there are potential performance issues, such as N+1 queries, large transactions, etc.)

## 业务错误码规范 (Business Error Code Standards)

错误码应具有一定的结构，便于问题定位和排查。建议使用以下格式 (Error codes should have a certain structure for easy problem location and troubleshooting. The following format is recommended):

- 0000: 成功 (Success)
- A开头: 系统级错误 (System-level errors)
- B开头: 业务级错误 (Business-level errors)
- C开头: 接口调用错误 (Interface call errors)
- D开头: 数据错误 (Data errors)

每个错误码都应有详细的说明文档，便于开发和运维人员查阅 (Each error code should have detailed documentation for developers and operations personnel to reference).

## 安全规范 (Security Standards)

1. 敏感数据（如密码、证件号）需要加密存储 (Sensitive data such as passwords and ID numbers need to be encrypted)
2. API调用需要进行身份验证和授权 (API calls need authentication and authorization)
3. 防止SQL注入、XSS等常见安全问题 (Prevent common security issues such as SQL injection and XSS)
4. 日志中不应包含敏感信息，使用`PrivacyUtil`进行脱敏处理 (Logs should not contain sensitive information, use `PrivacyUtil` for desensitization)
5. 错误响应不应暴露系统内部信息 (Error responses should not expose internal system information)

## 性能优化指南 (Performance Optimization Guidelines)

1. 合理使用索引提高查询性能 (Reasonable use of indexes to improve query performance)
2. 避免N+1查询问题 (Avoid N+1 query problems)
3. 使用批量操作替代循环单条操作 (Use batch operations instead of looping single operations)
4. 使用缓存减少数据库访问 (Use caching to reduce database access)
5. 大数据量处理时使用分页查询 (Use paged queries when processing large amounts of data)
6. 合理设置连接池参数 (Set connection pool parameters reasonably)
7. 使用异步处理提高并发能力 (Use asynchronous processing to improve concurrency)
8. 对于并行处理任务，考虑使用线程池和CompletableFuture (For parallel processing tasks, consider using thread pools and CompletableFuture)

## 常见问题处理 (Common Issue Handling)

1. **并发控制 (Concurrency Control)**
   - 使用分布式锁控制并发访问 (Use distributed locks to control concurrent access)
   - 使用乐观锁处理数据更新冲突 (Use optimistic locking to handle data update conflicts)
   - 关键业务操作使用悲观锁 (Use pessimistic locking for critical business operations)

2. **幂等性处理 (Idempotency Handling)**
   - 生成唯一标识符作为幂等性控制 (Generate unique identifiers for idempotency control)
   - 使用状态检查防止重复操作 (Use state checks to prevent duplicate operations)
   - 通过Redis等实现分布式锁或标记 (Implement distributed locks or markers through Redis, etc.)

3. **缓存使用 (Cache Usage)**
   - 根据业务特点选择合适的缓存策略 (Choose appropriate caching strategies based on business characteristics)
   - 处理缓存穿透、缓存击穿和缓存雪崩问题 (Handle cache penetration, cache breakdown, and cache avalanche issues)
   - 保持缓存与数据库的一致性 (Maintain consistency between cache and database)

4. **跨服务调用 (Cross-Service Invocation)**
   - 设置合理的超时时间和重试策略 (Set reasonable timeout and retry strategies)
   - 实现熔断和降级机制 (Implement circuit breaking and degradation mechanisms)
   - 使用异步调用提高系统吞吐量 (Use asynchronous calls to improve system throughput)

## 批处理特有规范 (Batch Processing Specific Standards)

1. **批处理任务 (Batch Processing Tasks)**
   - 批处理任务应有明确的开始和结束日志 (Batch tasks should have clear start and end logs)
   - 批处理任务应有进度日志，便于监控 (Batch tasks should have progress logs for easy monitoring)
   - 批处理任务应有异常处理机制，避免因单条数据异常导致整个任务失败 (Batch tasks should have exception handling mechanisms to avoid entire task failure due to single data exceptions)

2. **文件处理 (File Processing)**
   - 文件处理应有明确的文件命名规范 (File processing should have clear file naming conventions)
   - 文件处理应有备份机制，避免数据丢失 (File processing should have backup mechanisms to avoid data loss)
   - 文件处理应有校验机制，确保数据完整性 (File processing should have verification mechanisms to ensure data integrity)

3. **定时任务 (Scheduled Tasks)**
   - 定时任务应有明确的执行时间和频率 (Scheduled tasks should have clear execution times and frequencies)
   - 定时任务应有锁机制，避免重复执行 (Scheduled tasks should have locking mechanisms to avoid duplicate execution)
   - 定时任务应有监控机制，便于问题排查 (Scheduled tasks should have monitoring mechanisms for easy troubleshooting)

## 附录 (Appendix)
- 常用状态码说明 (Common status code descriptions)
- 数据库表关系图 (Database table relationship diagrams)
- 服务依赖关系 (Service dependency relationships)
