<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.howbuy.tms</groupId>
    <artifactId>high-batch-center</artifactId>
    <version>4.8.50-RELEASE</version>
  </parent>

	<artifactId>high-batch-center-remote</artifactId>
  <name>high-batch-center-remote</name>
  <packaging>jar</packaging>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <dependencies>

	  <dependency>
		  <groupId>org.javassist</groupId>
		  <artifactId>javassist</artifactId>
		  <version>3.30.2-GA</version>
	  </dependency>
	  <dependency>
		  <groupId>org.slf4j</groupId>
		  <artifactId>slf4j-api</artifactId>
		  <version>1.7.30</version>
	  </dependency>
	  <!-- Log4j2 实现 -->
	  <dependency>
		  <groupId>org.apache.logging.log4j</groupId>
		  <artifactId>log4j-api</artifactId>
	  </dependency>
	  <dependency>
		  <groupId>org.apache.logging.log4j</groupId>
		  <artifactId>log4j-core</artifactId>
	  </dependency>
	  <dependency>
		  <groupId>org.apache.logging.log4j</groupId>
		  <artifactId>log4j-slf4j-impl</artifactId>
	  </dependency>
	  <dependency>
		  <groupId>commons-logging</groupId>
		  <artifactId>commons-logging</artifactId>
		  <version>1.2</version>
	  </dependency>
	  <dependency>
		  <groupId>com.howbuy.common</groupId>
		  <artifactId>common-facade</artifactId>
		  <version>${com.howbuy.common-facade.version}</version>
	  </dependency>
	  <dependency>
		  <groupId>org.hibernate.validator</groupId>
		  <artifactId>hibernate-validator</artifactId>
	  </dependency>
	  <dependency>
		  <groupId>com.howbuy.boot</groupId>
		  <artifactId>howbuy-boot-actuator-dubbo3</artifactId>
	  </dependency>
  	<dependency>
  		<groupId>com.howbuy.tms</groupId>
  		<artifactId>high-batch-center-service</artifactId>
		<exclusions>
			<exclusion>
				<artifactId>dubbo</artifactId>
				<groupId>com.alibaba</groupId>
			</exclusion>
			<exclusion>
				<artifactId>log4j</artifactId>
				<groupId>log4j</groupId>
			</exclusion>
			<exclusion>
				<groupId>org.slf4j</groupId>
				<artifactId>log4j-over-slf4j</artifactId>
			</exclusion>
			<exclusion>
				<artifactId>zkclient</artifactId>
				<groupId>com.github.sgroschupf</groupId>
			</exclusion>
			<exclusion>
				<artifactId>howbuy-comcalculate</artifactId>
				<groupId>com.howbuy.fp</groupId>
			</exclusion>
			<exclusion>
				<artifactId>howbuy-framework-rpc</artifactId>
				<groupId>com.howbuy.pa.framework</groupId>
			</exclusion>
			<exclusion>
				<artifactId>commons-beanutils</artifactId>
				<groupId>commons-beanutils</groupId>
			</exclusion>
			<exclusion>
				<artifactId>common-facade</artifactId>
				<groupId>com.howbuy.common</groupId>
			</exclusion>
			<exclusion>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-extension</artifactId>
			</exclusion>
			<exclusion>
				<artifactId>spring-kafka</artifactId>
				<groupId>org.springframework.kafka</groupId>
			</exclusion>
			<exclusion>
				<artifactId>springframework-addons</artifactId>
				<groupId>net.unicon.springframework</groupId>
			</exclusion>
		</exclusions>
	</dependency>
	  <dependency>
		  <groupId>com.howbuy.boot</groupId>
		  <artifactId>howbuy-boot-actuator</artifactId>
		  <exclusions>
			  <exclusion>
				  <artifactId>commons-io</artifactId>
				  <groupId>commons-io</groupId>
			  </exclusion>
			  <exclusion>
				  <groupId>org.slf4j</groupId>
				  <artifactId>slf4j-api</artifactId>
			  </exclusion>
			  <exclusion>
				  <artifactId>howbuy-boot-actuator-core</artifactId>
				  <groupId>com.howbuy.boot</groupId>
			  </exclusion>
		  </exclusions>
	  </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
  		<finalName>high-batch-center-remote</finalName>
	  <!-- 根据环境打包本地文件 -->
	  <resources>
		  <resource>
			  <directory>src/main/resources</directory>
			  <includes>
				  <include>**/*</include>
			  </includes>
		  </resource>
		  <resource>
			  <directory>src/main/profiles/${env}</directory>
		  </resource>
	  </resources>

	  <plugins>
		  <plugin>
			  <groupId>org.apache.maven.plugins</groupId>
			  <artifactId>maven-jar-plugin</artifactId>
			  <version>3.2.2</version>
			  <configuration>
				  <archive>
					  <manifest>
						  <addClasspath>true</addClasspath>
						  <!-- 使用唯一版本, 不带时间戳 -->
						  <useUniqueVersions>false</useUniqueVersions>
						  <!--这个jar所依赖的jar包添加classPath的时候的前缀，如果这个jar本身和依赖包在同一级目录，则不需要添加-->
						  <classpathPrefix>.</classpathPrefix>
						  <mainClass>com.howbuy.tms.high.batch.remote.main.HighBatchCenterApplication</mainClass>
					  </manifest>
					  <manifestEntries>
						  <Package-Stamp>${parelease}</Package-Stamp>
					  </manifestEntries>
				  </archive>
			  </configuration>
		  </plugin>

		  <plugin>
			  <groupId>org.apache.maven.plugins</groupId>
			  <artifactId>maven-dependency-plugin</artifactId>
			  <executions>
				  <execution>
					  <id>copy-lib</id>
					  <phase>package</phase>
					  <goals>
						  <goal>copy-dependencies</goal>
					  </goals>
					  <configuration>
						  <outputDirectory>${project.build.directory}/${project.build.finalName}/lib</outputDirectory>
						  <excludeTransitive>false</excludeTransitive>
						  <stripVersion>false</stripVersion>
						  <includeScope>runtime</includeScope>
					  </configuration>
				  </execution>
			  </executions>
		  </plugin>
	  </plugins>
  </build>

	<!--环境配置-->
	<profiles>
		<profile>
			<id>dev</id>
			<properties>
				<env>dev</env>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<id>pro</id>
			<properties>
				<env>pro</env>
			</properties>
		</profile>
	</profiles>
</project>