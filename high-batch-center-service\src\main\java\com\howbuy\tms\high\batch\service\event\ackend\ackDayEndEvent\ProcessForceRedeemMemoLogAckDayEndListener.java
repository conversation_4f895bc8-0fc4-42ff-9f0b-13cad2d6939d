package com.howbuy.tms.high.batch.service.event.ackend.ackDayEndEvent;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.enums.database.ProcessStatusEnum;
import com.howbuy.tms.high.batch.dao.po.batch.HighForceRedMemoChgLogPo;
import com.howbuy.tms.high.batch.service.business.syncmodifyforceredeemmemo.SyncModifyForceRedeemMemoService;
import com.howbuy.tms.high.batch.service.common.utils.HowBuyRunTaskUil;
import com.howbuy.tms.high.batch.service.common.utils.AbstractHowbuyBaseTask;
import com.howbuy.tms.high.batch.service.event.HighEventListener;
import com.howbuy.tms.high.batch.service.repository.HighForceRedMemoChgLogRepository;
import com.howbuy.tms.high.batch.service.service.batch.ackdayend.task.ModifyForceRedeemMemoTask;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:处理强减原因修改记录
 * @Author: yun.lu
 * Date: 2023/12/22 13:25
 */
@Component
public class ProcessForceRedeemMemoLogAckDayEndListener extends HighEventListener<AckDayEndEvent> {
    private static final Logger log = LogManager.getLogger(ProcessForceRedeemMemoLogAckDayEndListener.class);

    @Autowired
    private HighForceRedMemoChgLogRepository highForceRedMemoChgLogRepository;
    @Autowired
    private SyncModifyForceRedeemMemoService syncModifyForceRedeemMemoService;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;


    @Override
    public void processEvent(AckDayEndEvent event) {
        log.info("ProcessForceRedeemMemoLogAckDayEndListener-确认处理日终,处理强减原因修改记录-start,event={}", JSON.toJSONString(event));
        List<HighForceRedMemoChgLogPo> list = getHighForceRedMemoChgLogPos();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<AbstractHowbuyBaseTask> taskList = new ArrayList<>();
        for (HighForceRedMemoChgLogPo po : list) {
            // 只处理最新的一条，非最新的更新成无需处理
            po.setProcessStatus(ProcessStatusEnum.NOT_NEED_PROCESS.getCode());
            taskList.add(new ModifyForceRedeemMemoTask(po, syncModifyForceRedeemMemoService));
        }
        howBuyRunTaskUil.runTask(taskList);
        log.info("ProcessForceRedeemMemoLogAckDayEndListener-确认处理日终,处理强减原因修改记录-end,event={}", JSON.toJSONString(event));
    }


    /**
     * 查询强制赎回原因日志记录
     */
    private List<HighForceRedMemoChgLogPo> getHighForceRedMemoChgLogPos() {
        return highForceRedMemoChgLogRepository.selectByProcessStatus(ProcessStatusEnum.NOT_PROCESS.getCode());
    }
}
