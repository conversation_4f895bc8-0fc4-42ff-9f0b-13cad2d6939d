package com.howbuy.tms.high.batch.service.task;

import com.howbuy.tms.high.batch.service.common.utils.AbstractHowbuyBaseTask;
import com.howbuy.tms.high.batch.service.repository.HighDayEndRepository;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @Description:日终两套账本备份
 * @Author: yun.lu
 * Date: 2025/4/9 17:27
 */
@Data
@AllArgsConstructor
public class DayEndBackBookTask extends AbstractHowbuyBaseTask {
    private static Logger logger = LogManager.getLogger(DayEndBackBookTask.class);
    private String taCode;
    private String tradeDt;
    private HighDayEndRepository highDayEndRepository;

    @Override
    protected void callTask() {
        try {
            logger.info("DayEndBackBookTask-日终两套账本备份,taCode:{},tradeDt:{}", taCode, tradeDt);
            // 备份账本
            highDayEndRepository.insertHisCustBooks(tradeDt, taCode);
            // 备份子账本
            highDayEndRepository.insertHisSubCustBooks(tradeDt, taCode);
        } catch (Exception e) {
            logger.error("DayEndBackBookTask-日终两套账本备份异常,errMsg:{}", e.getMessage(), e);
        }
    }
}
