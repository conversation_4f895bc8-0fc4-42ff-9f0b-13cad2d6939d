/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 * <p>
 * Copyright (c) 2016, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 * <p>
 * Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 *Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.query.queryfunddealorderdtl;

import com.github.pagehelper.Page;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.RedeemDirectionEnum;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.high.batch.dao.po.order.DealOrderRefundPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.dao.vo.ConsoleHighFundDealOrderDtlVo;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.QueryHighFundDealOrderDtlFacade;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.QueryHighFundDealOrderDtlRequest;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.QueryHighFundDealOrderDtlResponse;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.bean.HighFundDealOrderDtlBean;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.bean.QueryHighFundDealOrderDtlCondition;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.facade.AbstractService;
import com.howbuy.tms.high.batch.service.repository.DealOrderRefundRepository;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/***
 *
 * @description:(查询高端交易订单明细)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年7月19日 下午4:42:49
 * @since JDK 1.6
 */
@DubboService
@Service("queryHighFundDealOrderDtlFacade")
public class QueryHighFundDealOrderDtlFacadeService extends AbstractService<QueryHighFundDealOrderDtlRequest, QueryHighFundDealOrderDtlResponse> implements
        QueryHighFundDealOrderDtlFacade {
    private static Logger logger = LogManager.getLogger(QueryHighFundDealOrderDtlFacadeService.class);
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private DealOrderRefundRepository dealOrderRefundRepository;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;


    @Override
    public QueryHighFundDealOrderDtlResponse process(QueryHighFundDealOrderDtlRequest request) {
        logger.info("QueryHighFundDealOrderDtlFacadeService|process|start");
        QueryHighFundDealOrderDtlResponse resp = new QueryHighFundDealOrderDtlResponse();

        /* 参数 */
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        // 查询条件
        QueryHighFundDealOrderDtlCondition queryCondition = request.getQueryCondition();
        if (queryCondition == null) {
            throw new ValidateException(ExceptionCodes.PARAMS_ERROR, MessageSource.getMessageByCode(ExceptionCodes.PARAMS_ERROR));
        }
        long startTime = System.currentTimeMillis();
        logger.info("QueryHighFundDealOrderDtlFacadeService|getConsoleHighFundDealOrderDtl|start");
        List<String> filterFundCodeList;
        if (YesOrNoEnum.NO.getCode().equals(request.getIsHBJGAuth())) {
            filterFundCodeList = queryHighProductOuterService.queryNotHBJGFundListService();
            queryCondition.setFilterFundCodeList(filterFundCodeList);
        }

        /* 查询高端订单明细 */
        Page<ConsoleHighFundDealOrderDtlVo> consoleHighDealOrderVoList = getConsoleHighFundDealOrderDtl(queryCondition, pageNo, pageSize);
        logger.info("QueryHighFundDealOrderDtlFacadeService|getConsoleHighFundDealOrderDtl|end cost:{}", (System.currentTimeMillis() - startTime));
        long copyStartTime = System.currentTimeMillis();
        logger.info("QueryHighFundDealOrderDtlFacadeService|copy|start");
        /* 转换 */
        HighFundDealOrderDtlBean highfundDealOrderDtlBean = null;
        List<HighFundDealOrderDtlBean> highDealOrderDtlBeanList = new ArrayList<HighFundDealOrderDtlBean>();

        for (ConsoleHighFundDealOrderDtlVo consoleHighFundDealOrderDtlVo : consoleHighDealOrderVoList) {
            highfundDealOrderDtlBean = new HighFundDealOrderDtlBean();
            BeanUtils.copyProperties(consoleHighFundDealOrderDtlVo, highfundDealOrderDtlBean);
            // 设置回款信息
            setRefundInfo(highfundDealOrderDtlBean);
            highDealOrderDtlBeanList.add(highfundDealOrderDtlBean);
        }
        logger.info("QueryHighFundDealOrderDtlFacadeService|copy|end:{}", System.currentTimeMillis() - copyStartTime);
        resp.setFundDealOrderDtlBeanList(highDealOrderDtlBeanList);

        long pageCountStart = System.currentTimeMillis();
        logger.info("QueryHighFundDealOrderDtlFacadeService|pagecount|start");
        /* 当前页面小计 */
        countPageData(resp, highDealOrderDtlBeanList);
        logger.info("QueryHighFundDealOrderDtlFacadeService|pagecount|end:{}", System.currentTimeMillis() - pageCountStart);
        long pageAmtCountStartTime = System.currentTimeMillis();
        logger.info("QueryHighFundDealOrderDtlFacadeService|pageamtcount|start");
        /* 统计全部申请金额/份额 */
        countAllHighAmtAndVol(resp, queryCondition);
        logger.info("QueryHighFundDealOrderDtlFacadeService|pageamtcount|end:{}", System.currentTimeMillis() - pageAmtCountStartTime);
        resp.setPageNo(consoleHighDealOrderVoList.getPageNum());
        resp.setTotalCount(consoleHighDealOrderVoList.getTotal());
        resp.setTotalPage(consoleHighDealOrderVoList.getPages());

        resp.setReturnCode(ExceptionCodes.SUCCESS);
        logger.info("QueryHighFundDealOrderDtlFacadeService|process|end");
        return resp;
    }

    /***
     *
     * countAllHighAmtAndVol:(统计全部高端申请金额/份额)
     *
     * @param resp
     * @param queryCondition
     * <AUTHOR>
     * @date 2017年7月19日 下午4:43:03
     */
    private void countAllHighAmtAndVol(QueryHighFundDealOrderDtlResponse resp, QueryHighFundDealOrderDtlCondition queryCondition) {
        HighDealOrderDtlPo highDealOrderDtlPo = highDealOrderDtlRepository.getCountHighFundDealOrderDtlForConsole(queryCondition);

        if (highDealOrderDtlPo == null) {
            return;
        }
        resp.setTotoalAppVol(highDealOrderDtlPo.getAppVol());
        resp.setTotoalAppAmt(highDealOrderDtlPo.getAppAmt());
    }

    /***
     *
     * countPageData:(当前页面小计)
     *
     * @param resp
     * @param fundDealOrderDtlBeanList
     * <AUTHOR>
     * @date 2017年7月19日 下午4:43:15
     */
    private void countPageData(QueryHighFundDealOrderDtlResponse resp, List<HighFundDealOrderDtlBean> fundDealOrderDtlBeanList) {
        BigDecimal pageAppAmt = BigDecimal.ZERO;
        BigDecimal pageAppVol = BigDecimal.ZERO;
        for (HighFundDealOrderDtlBean fundDealOrderDtlBean : fundDealOrderDtlBeanList) {
            if (fundDealOrderDtlBean.getAppAmt() != null) {
                pageAppAmt = pageAppAmt.add(fundDealOrderDtlBean.getAppAmt());
            }
            if (fundDealOrderDtlBean.getAppVol() != null) {
                pageAppVol = pageAppVol.add(fundDealOrderDtlBean.getAppVol());
            }
        }
        resp.setPageAppVol(pageAppVol);
        resp.setPageAppAmt(pageAppAmt);
    }

    /***
     *
     * getConsoleHighFundDealOrderDtl:(根据条件查询高端交易订单明细)
     *
     * @param queryCondition
     * @param pageNo
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2017年7月19日 下午4:43:27
     */
    private Page<ConsoleHighFundDealOrderDtlVo> getConsoleHighFundDealOrderDtl(QueryHighFundDealOrderDtlCondition queryCondition, Integer pageNo, Integer pageSize) {
        return highDealOrderDtlRepository.getHighFundDealOrderDtlForConsole(queryCondition, pageNo, pageSize);
    }

    /***
     *
     * setRefundInfo:构建回款信息
     *
     * @param bean
     * @return
     * <AUTHOR>
     * @date 2021年7月26日 下午4:43:27
     */
    private void setRefundInfo(HighFundDealOrderDtlBean bean) {
        // 回可用才需要处理
        if (!RedeemDirectionEnum.isRefundFinaAvail(bean.getRedeemDirection())) {
            return;
        }
        DealOrderRefundPo po = getRefund(bean.getDealNo());
        if (Objects.isNull(po)) {
            return;
        }
        StringBuilder builder = new StringBuilder();
        builder.append(RedeemDirectionEnum.getName(po.getRefundDirection())).append(":");
        builder.append(po.getRefundAmt() == null ? "" : po.getRefundAmt());
        if (RedeemDirectionEnum.isRefundFinaAvail(po.getRefundDirection())) {
            builder.append(";回款备注:").append(StringUtils.isEmpty(po.getRefundMemo()) ? "" : po.getRefundMemo());
        }
        builder.append(";");
        bean.setRefundInfo(builder.toString());
    }

    /**
     * @description:查询回款信息
     * @param dealNo
     * @return com.howbuy.tms.high.batch.dao.po.order.DealOrderRefundPo
     * @author: chuanguang.tang
     * @date: 2021/8/12 13:22
     * @since JDK 1.8
     */
    private DealOrderRefundPo getRefund(String dealNo) {
        return dealOrderRefundRepository.getByDealNo(dealNo);
    }

}
