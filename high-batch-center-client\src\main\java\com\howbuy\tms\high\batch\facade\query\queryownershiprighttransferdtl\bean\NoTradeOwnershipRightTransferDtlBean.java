package com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.bean;

import lombok.Data;

/**
 * @Description:非交易股份份额转让相应
 * @Author: yun.lu
 * Date: 2023/5/18 16:35
 */
@Data
public class NoTradeOwnershipRightTransferDtlBean extends AbstractOwnershipRightTransferDtlBean {
    private static final long serialVersionUID = -185222355523137L;

    /**
     * 转出方账号
     */
    private String outTxAcctNo;

    /**
     * 转出方姓名
     */
    private String outCustName;

    /**
     * 转入方账号
     */
    private String inTxAcctNo;

    /**
     * 转入方姓名
     */
    private String inCustName;

    /**
     * 转出订单
     */
    private String outDealDtlNo;

    /**
     * 转入订单
     */
    private String inDealDtlNo;


}
