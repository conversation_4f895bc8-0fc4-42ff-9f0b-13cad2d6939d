package com.howbuy.tms.high.batch.service.common;

import com.howbuy.trace.RequestChainTrace;
import org.apache.logging.log4j.ThreadContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.MDC;

/**
 * OpsMonitor测试类
 * 验证warn方法的日志输出问题
 */
public class OpsMonitorTest {

    @Before
    public void setUp() {
        // 清理所有上下文
        ThreadContext.clearAll();
        MDC.clear();
        RequestChainTrace.remove();
    }

    @After
    public void tearDown() {
        // 清理所有上下文
        ThreadContext.clearAll();
        MDC.clear();
        RequestChainTrace.remove();
    }

    @Test
    public void testBasicWarn() {
        // 设置ThreadContext
        ThreadContext.put("uuid", "test-uuid-12345");
        
        System.out.println("=== 开始测试 OpsMonitor.warn() ===");
        
        // 调用warn方法
        OpsMonitor.warn("这是一个测试告警消息", OpsMonitor.ERROR);
        
        System.out.println("=== OpsMonitor.warn() 调用完成 ===");
        
        // 等待一下，确保日志写入
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            // ignore
        }
        
        System.out.println("=== 测试完成，请检查 warn.log 文件 ===");
    }
}
