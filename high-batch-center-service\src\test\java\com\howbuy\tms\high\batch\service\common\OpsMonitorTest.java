package com.howbuy.tms.high.batch.service.common;

import com.howbuy.trace.RequestChainTrace;
import org.apache.logging.log4j.ThreadContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.MDC;

/**
 * OpsMonitor测试类
 * 验证warn方法能够从不同来源正确获取traceId
 */
public class OpsMonitorTest {

    @Before
    public void setUp() {
        // 清理所有上下文
        ThreadContext.clearAll();
        MDC.clear();
        RequestChainTrace.remove();
    }

    @After
    public void tearDown() {
        // 清理所有上下文
        ThreadContext.clearAll();
        MDC.clear();
        RequestChainTrace.remove();
    }

    @Test
    public void testWarnWithThreadContext() {
        // 设置ThreadContext
        ThreadContext.put("uuid", "test-thread-context-uuid");
        
        // 调用warn方法
        OpsMonitor.warn("测试ThreadContext获取uuid", OpsMonitor.ERROR);
        
        System.out.println("测试ThreadContext完成");
    }

    @Test
    public void testWarnWithMDC() {
        // 设置MDC
        MDC.put("uuid", "test-mdc-uuid");
        
        // 调用warn方法
        OpsMonitor.warn("测试MDC获取uuid", OpsMonitor.ERROR);
        
        System.out.println("测试MDC完成");
    }

    @Test
    public void testWarnWithRequestChainTrace() {
        // 设置RequestChainTrace
        RequestChainTrace.buildAndSet("test-request-chain-uuid", null);
        
        // 调用warn方法
        OpsMonitor.warn("测试RequestChainTrace获取uuid", OpsMonitor.ERROR);
        
        System.out.println("测试RequestChainTrace完成");
    }

    @Test
    public void testWarnWithoutAnyContext() {
        // 不设置任何上下文，应该使用默认值
        OpsMonitor.warn("测试默认uuid", OpsMonitor.ERROR);
        
        System.out.println("测试默认uuid完成");
    }

    @Test
    public void testWarnPriority() {
        // 设置所有三种上下文，验证优先级
        RequestChainTrace.buildAndSet("request-chain-uuid", null);
        MDC.put("uuid", "mdc-uuid");
        ThreadContext.put("uuid", "thread-context-uuid");
        
        // 应该使用ThreadContext的值（优先级最高）
        OpsMonitor.warn("测试优先级：应该使用ThreadContext的uuid", OpsMonitor.ERROR);
        
        System.out.println("测试优先级完成");
    }

    @Test
    public void testWarnFallbackToMDC() {
        // 只设置MDC和RequestChainTrace，验证fallback到MDC
        RequestChainTrace.buildAndSet("request-chain-uuid", null);
        MDC.put("uuid", "mdc-uuid");
        
        // 应该使用MDC的值
        OpsMonitor.warn("测试fallback：应该使用MDC的uuid", OpsMonitor.ERROR);
        
        System.out.println("测试fallback到MDC完成");
    }

    @Test
    public void testWarnFallbackToRequestChainTrace() {
        // 只设置RequestChainTrace，验证fallback到RequestChainTrace
        RequestChainTrace.buildAndSet("request-chain-uuid", null);
        
        // 应该使用RequestChainTrace的值
        OpsMonitor.warn("测试fallback：应该使用RequestChainTrace的uuid", OpsMonitor.ERROR);
        
        System.out.println("测试fallback到RequestChainTrace完成");
    }
}
