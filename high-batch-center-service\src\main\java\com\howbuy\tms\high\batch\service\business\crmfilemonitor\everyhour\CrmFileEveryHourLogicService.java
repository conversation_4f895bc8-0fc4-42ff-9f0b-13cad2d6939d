package com.howbuy.tms.high.batch.service.business.crmfilemonitor.everyhour;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.enums.database.ProcessStatusEnum;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.batch.CmFileProcessRecPo;
import com.howbuy.tms.high.batch.service.business.crmfilemonitor.AbstractCrmFileProcessorService;
import com.howbuy.tms.high.batch.service.common.enums.CrmFileReceiveTypeEnum;
import com.howbuy.tms.high.batch.service.common.utils.HowBuyRunTaskUil;
import com.howbuy.tms.high.batch.service.common.utils.AbstractHowbuyBaseTask;
import com.howbuy.tms.high.batch.service.common.ThreadExceptionStatus;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.CmNaFileImportService;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.CmTradeFileImportService;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.CmVolFileImportService;
import com.howbuy.tms.high.batch.service.task.ParseNaFeeFileTask;
import com.howbuy.tms.high.batch.service.task.ParseTradeFileTask;
import com.howbuy.tms.high.batch.service.task.ParseVolFileTask;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:crm非香港每小时推送文件逻辑处理类
 * @Author: yun.lu
 * Date: 2023/8/9 16:29
 */
@Service
public class CrmFileEveryHourLogicService extends AbstractCrmFileProcessorService {

    private static Logger logger = LogManager.getLogger(CrmFileEveryHourLogicService.class);
    @Autowired
    private CmVolFileImportService cmVolFileImportService;
    @Autowired
    private CmTradeFileImportService cmTradeFileImportService;
    @Autowired
    private CmNaFileImportService cmNaFileImportService;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;

    @Override
    protected void processFile(CmFileProcessRecPo po) throws Exception {
        // 查询上个工作日
        Date lastDay = DateUtils.addDay(new Date(), -1);
        String lastDayStr = DateUtils.formatToString(lastDay, DateUtils.YYYYMMDD);
        List<ThreadExceptionStatus> statusList = new ArrayList<>();
        List<AbstractHowbuyBaseTask> taskList = new ArrayList<>();
        taskList.add(new ParseNaFeeFileTask(po, statusList, lastDayStr, cmNaFileImportService, CrmFileReceiveTypeEnum.CRM_EVERY_HOUR.getType(), naFeeFilePathName));
        taskList.add(new ParseTradeFileTask(po, statusList, lastDayStr, cmTradeFileImportService, tradeDirectFilePathName, CrmFileReceiveTypeEnum.CRM_EVERY_HOUR.getType()));
        taskList.add(new ParseVolFileTask(po, statusList, lastDayStr, cmVolFileImportService, volFilePathName, CrmFileReceiveTypeEnum.CRM_EVERY_HOUR.getType()));
        howBuyRunTaskUil.runTask(taskList);
        for (ThreadExceptionStatus status : statusList) {
            if (status.isExsitException()) {
                throw new Exception("CrmFileEveryHourLogicService checkFundCount err");
            }
        }
        po.setProcessStatus(ProcessStatusEnum.PROCESS_SUCCESS.getCode());
    }

    @Override
    protected void checkFundCount(CmFileProcessRecPo po) throws Exception {
        logger.info("CrmFileEveryHourLogicService-checkFundCount,每小时同步,不需要校验总持仓,po={}", JSON.toJSONString(po));
    }

    @Override
    protected void insertFormalTable(CmFileProcessRecPo po) {
        if (!ProcessStatusEnum.PROCESS_SUCCESS.getCode().equals(po.getProcessStatus())) {
            logger.warn("CrmFileEveryHourLogicService|insertFormalTable|versionNo:{} and ProcessStatus:{}", po.getVersionNo(), po.getProcessStatus());
            return;
        }
        cmDataProcessService.everyHourExecBatchInsert(po.getVersionNo() + "");
    }
}
