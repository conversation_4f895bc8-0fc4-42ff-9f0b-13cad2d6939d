/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.tradecheck;

import com.howbuy.common.exception.BusinessException;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.OrderUpdateType;
import com.howbuy.tms.common.enums.database.CancelOrderSrcEnum;
import com.howbuy.tms.common.enums.database.DiffProcessStatusEnum;
import com.howbuy.tms.common.enums.database.NotifySubmitFlagEnum;
import com.howbuy.tms.common.enums.database.SubmitAppFlagEnum;
import com.howbuy.tms.common.enums.database.TradeCheckErrorEnum;
import com.howbuy.tms.common.enums.database.TradeExcpSrcEnum;
import com.howbuy.tms.common.enums.database.TxAppFlagEnum;
import com.howbuy.tms.common.enums.database.TxCompFlagEnum;
import com.howbuy.tms.common.outerservice.fbsonlinesearch.querydeals.AppFlagEnum;
import com.howbuy.tms.common.outerservice.fbsonlinesearch.querydeals.QueryTradeCheckContext;
import com.howbuy.tms.common.outerservice.fbsonlinesearch.querydeals.QueryTradeCheckOuterService;
import com.howbuy.tms.common.outerservice.fbsonlinesearch.querydeals.QueryTradeCheckResult;
import com.howbuy.tms.common.utils.PartListUtil;
import com.howbuy.tms.high.batch.dao.po.batch.HighTradeExceptionPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.service.business.refreshdealorderstatus.RefreshDealOrderStatusService;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.common.utils.HowBuyRunTaskUil;
import com.howbuy.tms.high.batch.service.repository.HighTradeExceptionRepository;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.repository.SimuFundCheckOrderRepository;
import com.howbuy.tms.high.batch.service.service.order.tradepaymentcheck.TradePaymentCheckService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.RecursiveTask;

/**
 * Description:交易对账处模块
 *
 * @reason:
 * <AUTHOR>
 * @date 2016年9月19日 上午11:38:55
 * @since JDK 1.7
 */
@Service("doTradeCheckService")
public class DoTradeCheckService {
    private Logger logger = LogManager.getLogger(DoTradeCheckService.class);

    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;

    @Autowired
    private SimuFundCheckOrderRepository simuFundCheckOrderRepository;

    @Autowired
    private TradePaymentCheckService tradePaymentCheckService;

    @Autowired
    private QueryTradeCheckOuterService queryTradeCheckOuterService;

    @Autowired
    private RefreshDealOrderStatusService refreshDealOrderStatusService;

    @Autowired
    private HighTradeExceptionRepository highTradeExceptionRepository;

    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;

    /**
     *
     * doTradeCheck:交易对账处理业务逻辑
     *
     * @param orderList
     *            需要对账的交易对账订单列表
     * @return
     * @return List<TradeCheckDetailBean> 交易对账错误信息
     * <AUTHOR>
     * @date 2016年10月13日 下午10:04:19
     */
    public List<TradeCheckDetailBean> doTradeCheck(List<SimuFundCheckOrderPo> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return new ArrayList<>();
        }
        List<TradeCheckDetailBean> result = new ArrayList<>();
        List<List<SimuFundCheckOrderPo>> partition = PartListUtil.partition(orderList, 10);
        List<TradeCheckTask> tradeCheckTaskList = new ArrayList<>();
        for (List<SimuFundCheckOrderPo> simuFundCheckOrderPos : partition) {
            tradeCheckTaskList.add(new TradeCheckTask(simuFundCheckOrderPos,result,this));
        }
        howBuyRunTaskUil.runTask(tradeCheckTaskList);

        if (CollectionUtils.isEmpty(result)) {
            logger.info("DoTradeCheckService|doTradeCheck  |check end,no error check detail.");
            return result;
        }

        for (TradeCheckDetailBean bean : result) {
            try {

                List<HighTradeExceptionPo> highTradeExceptionPoList = highTradeExceptionRepository.selectByDealNo(bean.getDealNo());

                // 是否存在未处理的对账异常记录
                boolean exitFlag = false;
                if (!CollectionUtils.isEmpty(highTradeExceptionPoList)) {
                    for (HighTradeExceptionPo highTradeExceptionPo : highTradeExceptionPoList) {
                        if (TradeExcpSrcEnum.TRADE_CHECK_EXCP.getCode().equals(highTradeExceptionPo.getTradeExcepSrc())
                                && DiffProcessStatusEnum.UNPROCESS.getKey().equals(highTradeExceptionPo.getProcessStatus())) {
                            exitFlag = true;
                            break;
                        }
                    }
                }

                logger.info("DoTradeCheckService|doTradeCheck |dealNo:{}, exitFlag:{}", bean.getDealNo(), exitFlag);
                if (!exitFlag) {
                    HighTradeExceptionPo highTradeExceptionPo = getFundCheckOrderDiff(bean);
                    highTradeExceptionRepository.insertSelective(highTradeExceptionPo);
                }

            } catch (Throwable e) {
                logger.error("DoTradeCheckService|Add FundCheckOrderDiffPo Exception，submitDealNo:" + bean.getSubmitDealNo(), e);
            }
        }

        return result;
    }

//    /**
//     *
//     * Description:交易对账任务处理类
//     *
//     * @reason:
//     * <AUTHOR>
//     * @date 2016年9月19日 下午1:20:26
//     * @since JDK 1.7
//     */
//    private class TradeCheckTask extends RecursiveTask<List<TradeCheckDetailBean>> {
//
//        private static final long serialVersionUID = -5668990781704464642L;
//
//        private List<SimuFundCheckOrderPo> orderList;
//        public TradeCheckTask(List<SimuFundCheckOrderPo> orderList) {
//            this.orderList = orderList;
//        }
//
//        @Override
//        protected List<TradeCheckDetailBean> compute() {
//
//            List<TradeCheckDetailBean> result = new ArrayList<TradeCheckDetailBean>();
//            if (CollectionUtils.isEmpty(orderList)) {
//                return result;
//            }
//            int count = orderList.size();
//            if (count == 1) {
//                SimuFundCheckOrderPo po = orderList.get(0);
//                TradeCheckDetailBean bean = check(po);
//                if (bean != null) {
//                    result.add(bean);
//                }
//                return result;
//            }
//
//            int mid = count / 2;
//
//            List<SimuFundCheckOrderPo> tasksOne = new ArrayList<SimuFundCheckOrderPo>();
//            List<SimuFundCheckOrderPo> tasksTwo = new ArrayList<SimuFundCheckOrderPo>();
//            tasksOne.addAll(orderList.subList(0, mid));
//            tasksTwo.addAll(orderList.subList(mid, count));
//
//            TradeCheckTask taskOne = new TradeCheckTask(tasksOne);
//            TradeCheckTask taskTwo = new TradeCheckTask(tasksTwo);
//            taskOne.fork();
//            taskTwo.fork();
//
//            List<TradeCheckDetailBean> resultOne = taskOne.join();
//            List<TradeCheckDetailBean> resultTwo = taskTwo.join();
//            if (CollectionUtils.isNotEmpty(resultOne)) {
//                result.addAll(resultOne);
//            }
//            if (CollectionUtils.isNotEmpty(resultTwo)) {
//                result.addAll(resultTwo);
//            }
//
//            return result;
//
//        }
//    }

    /**
     *
     * check:交易对账核心处理方法
     *
     * @param po
     * @return
     * <AUTHOR>
     * @date 2017年7月6日 下午5:33:37
     */
    TradeCheckDetailBean check(SimuFundCheckOrderPo po) {
        if (po == null) {
            return null;
        }

        String submitAppFlag = po.getSubmitAppFlag();
        // 校验上报申请标记，只有上报成功的订单才对账
        if (!SubmitAppFlagEnum.SUBMITTED.getCode().equals(submitAppFlag)) {
            logger.info("DoTradeCheckService|check|submitAppFlag error.submitDealNo:{}", po.getSubmitDealNo());
            return null;
        }

        String txCompFlag = po.getTxCompFlag();
        // 校验对账标记，对账状态为“未对账”的才对账
        if (!TxCompFlagEnum.NOT.getCode().equals(txCompFlag)) {
            logger.info("DoTradeCheckService|check|txCompFlag error.submitDealNo:{}", po.getSubmitDealNo());
            return null;
        }

        // 调用高端交易单笔对账接口
        QueryTradeCheckContext checkContext = new QueryTradeCheckContext();
        checkContext.setSubmitDealNo(po.getSubmitDealNo());
        checkContext.setDisCode(po.getDisCode());
        checkContext.setmBusiCode(po.getmBusiCode());
        QueryTradeCheckResult result = queryTradeCheckOuterService.getTradeCheck(checkContext);
        if (result != null && result.isCallSuccess()) {
            result.setCallSuccess(result.isCallSuccess());
        }
        // 判断是否调用成功，如果失败，则不进行对账操作
        if (!result.isCallSuccess()) {
            logger.info("DoTradeCheckService|check|call outservice failed.submitDealNo:{}", po.getSubmitDealNo());
            return null;
        }
        String dealDtlNo = po.getDealDtlNo();
        HighDealOrderDtlPo orderDtlPo = highDealOrderDtlRepository.selectByDealDtlNo(dealDtlNo);
        String submitDealNo = po.getSubmitDealNo();
        String txAppFlag = po.getTxAppFlag();
        String appFlag = result.getAppFlag();
        int updateCount = 0;
        // 如果返回订单不存在，更新交易订单明细上报状态为“待重报”
        if (TxAppFlagEnum.APP_SUCCESS.getCode().equals(txAppFlag) && AppFlagEnum.DEAL_NONEXIST.getKey().equals(appFlag)) {
            // 中台成功
            updateCount = highDealOrderDtlRepository.updateAppFlagAndSubmitFlag(dealDtlNo, null, NotifySubmitFlagEnum.RE_NOTIFY.getCode(),
                    orderDtlPo.getUpdateDtm());
            logger.info("DoTradeCheckService|check|appFlag unequal.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);
            if (updateCount != 1) {
                throw new BusinessException(ExceptionCodes.BATCH_CENTER_ORDER_DEAL_DTL_UPDATE_ERROR,
                        MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_ORDER_DEAL_DTL_UPDATE_ERROR));
            }

            refreshDealOrderStatusService.refleshDealOrderStatus(po.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
            return null;
        }
        if (TxAppFlagEnum.FORCE_REVOCATION.getCode().equals(txAppFlag) && AppFlagEnum.DEAL_NONEXIST.getKey().equals(appFlag)) {
            // 中台强制取消
            updateCount = simuFundCheckOrderRepository.updateAppFlagOrCompFlag(submitDealNo, null, TxCompFlagEnum.COMPLETED.getCode(), null, null,
                    po.getUpdateDtm());
            logger.info("DoTradeCheckService|check|appFlag unequal.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);
            if (updateCount != 1) {
                throw new BusinessException(ExceptionCodes.BATCH_CENTER_CHECK_ORDER_UPDATE_ERROR,
                        MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_CHECK_ORDER_UPDATE_ERROR));
            }
            refreshDealOrderStatusService.refleshDealOrderStatus(po.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
            return null;
        }
        if (TxAppFlagEnum.SELF_REVOCATION.getCode().equals(txAppFlag) && AppFlagEnum.DEAL_NONEXIST.getKey().equals(appFlag)) {
            // 中台自行撤单
            updateCount = simuFundCheckOrderRepository.updateAppFlagOrCompFlag(submitDealNo, null, TxCompFlagEnum.COMPLETED.getCode(), null, null,
                    po.getUpdateDtm());
            logger.info("DoTradeCheckService|check|appFlag unequal.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);
            if (updateCount != 1) {
                throw new BusinessException(ExceptionCodes.BATCH_CENTER_CHECK_ORDER_UPDATE_ERROR,
                        MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_CHECK_ORDER_UPDATE_ERROR));
            }
            refreshDealOrderStatusService.refleshDealOrderStatus(po.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
            return null;
        }

        BigDecimal appAmt = result.getAppAmt();
        BigDecimal amount = po.getAppAmt();
        if (!checkAmt(amount, appAmt)) {
            logger.info("DoTradeCheckService|check|appAmt unequal.dealAppAmt:{},appAmt:{},submitDealNo:{}", appAmt, amount, submitDealNo);
            // 金额不一致
            return getTradeCheckDetailBean(po, appFlag, appAmt, BigDecimal.ZERO, TradeCheckErrorEnum.NOT_CONSISTEND);
        }

        BigDecimal appVol = result.getAppVol();
        BigDecimal vol = po.getAppVol();
        if (!checkVol(vol, appVol)) {
            logger.info("DoTradeCheckService|check|appVol unequal.dealAppVol:{},appVol:{},submitDealNo:{}", appVol, vol, submitDealNo);
            // 份额不一致
            return getTradeCheckDetailBean(po, appFlag, BigDecimal.ZERO, appVol, TradeCheckErrorEnum.NOT_CONSISTEND);
        }

        // 状态匹配,将对账状态设置为对账成功
        if (txAppFlag.equals(appFlag)) {
            logger.info("DoTradeCheckService|check|appFlag equal.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);
            tradePaymentCheckService.updateTradeCheck(orderDtlPo, po, null, TxCompFlagEnum.COMPLETED.getCode(), null, null, null, null, null, false, true);
            return null;
        }

        // 中台成功，后台强制取消；将对账订单中的申请状态设置
        // 为“强制取消”、对账状态设置为“对账匹配”；将订单明细中的交易申请状态设置为“强制取消”；记录份额变动明细信息
        if (TxAppFlagEnum.APP_SUCCESS.getCode().equals(txAppFlag) && AppFlagEnum.FORE_WITHDRAW.getKey().equals(appFlag)) {
            logger.info("DoTradeCheckService|check|appFlag unequal.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);
            tradePaymentCheckService.updateTradeCheck(orderDtlPo, po, TxAppFlagEnum.FORCE_REVOCATION.getCode(), TxCompFlagEnum.COMPLETED.getCode(),
                    TxAppFlagEnum.FORCE_REVOCATION.getCode(), null, null, CancelOrderSrcEnum.FORCE_CANCEL.getCode(), null, true, true);
            refreshDealOrderStatusService.refleshDealOrderStatus(po.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
            return null;
        }

        // 中台成功，后台自行撤销；将对账订单中的申请状态设置为“自行撤销”、对账状态设置为“对账匹配”；将订单明细中的交易申请状态设置为“自行撤销”；记录份额变动明细信息
        if (TxAppFlagEnum.APP_SUCCESS.getCode().equals(txAppFlag) && AppFlagEnum.SELF_WITHDRAW.getKey().equals(appFlag)) {
            logger.info("DoTradeCheckService|check|appFlag unequal.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);
            tradePaymentCheckService.updateTradeCheck(orderDtlPo, po, TxAppFlagEnum.SELF_REVOCATION.getCode(), TxCompFlagEnum.COMPLETED.getCode(),
                    TxAppFlagEnum.SELF_REVOCATION.getCode(), null, null, CancelOrderSrcEnum.FORCE_CANCEL.getCode(), null, true, true);
            refreshDealOrderStatusService.refleshDealOrderStatus(po.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
            return null;
        }

        // 中台强制取消，后台自行撤销；将对账订单中的申请状态设置为“自行撤销”、对账状态设置为“对账匹配”；将订单明细中的交易申请状态设置为“自行撤销”
        if (TxAppFlagEnum.FORCE_REVOCATION.getCode().equals(txAppFlag) && AppFlagEnum.SELF_WITHDRAW.getKey().equals(appFlag)) {
            logger.info("DoTradeCheckService|check|appFlag unequal.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);
            tradePaymentCheckService.updateTradeCheck(orderDtlPo, po, TxAppFlagEnum.SELF_REVOCATION.getCode(), TxCompFlagEnum.COMPLETED.getCode(),
                    TxAppFlagEnum.SELF_REVOCATION.getCode(), null, null, null, null, false, null);

            refreshDealOrderStatusService.refleshDealOrderStatus(po.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
            return null;
        }

        // 中台强制取消，后台申请成功；记录对账异常数据
        if (TxAppFlagEnum.FORCE_REVOCATION.getCode().equals(txAppFlag) && AppFlagEnum.SUCCESS.getKey().equals(appFlag)) {
            logger.info("DoTradeCheckService|check|appFlag unequal.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);
            return getTradeCheckDetailBean(po, appFlag, appAmt, appVol, TradeCheckErrorEnum.CHECK_STATUS_INCONFORMITY);
        }

        // 中台自行撤单，后台强制取消；将对账订单中的申请状态设置为“强制取消”、对账状态设置为“对账匹配”；将订单明细中的交易申请状态设置为“强制取消”
        if (TxAppFlagEnum.SELF_REVOCATION.getCode().equals(txAppFlag) && AppFlagEnum.FORE_WITHDRAW.getKey().equals(appFlag)) {
            logger.info("DoTradeCheckService|check|appFlag unequal.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);
            tradePaymentCheckService.updateTradeCheck(orderDtlPo, po, TxAppFlagEnum.FORCE_REVOCATION.getCode(), TxCompFlagEnum.COMPLETED.getCode(),
                    TxAppFlagEnum.FORCE_REVOCATION.getCode(), null, null, CancelOrderSrcEnum.FORCE_CANCEL.getCode(), null, false, null);
            refreshDealOrderStatusService.refleshDealOrderStatus(po.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
            return null;
        }

        // 中台自行撤单，后台申请成功；将订单明细中的交易申请状态设置为“待重新上报”
        if (TxAppFlagEnum.SELF_REVOCATION.getCode().equals(txAppFlag) && AppFlagEnum.SUCCESS.getKey().equals(appFlag)) {
            logger.info("DoTradeCheckService|check|appFlag unequal.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);
            tradePaymentCheckService.updateTradeCheck(orderDtlPo, po, null, null, null, NotifySubmitFlagEnum.RE_NOTIFY.getCode(), null, null, null, false,
                    null);
            refreshDealOrderStatusService.refleshDealOrderStatus(po.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
            return null;
        }

        // 中台成功，后台失败；将对账订单中的申请状态设置为“申请失败”、对账状态设置为“对账匹配”；将订单明细中的交易申请状态设置为“申请失败”；记录份额变动明细信息
        if (TxAppFlagEnum.APP_SUCCESS.getCode().equals(txAppFlag) && AppFlagEnum.FAILED.getKey().equals(appFlag)) {
            logger.info("DoTradeCheckService|check|appFlag unequal.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);
            tradePaymentCheckService.updateTradeCheck(orderDtlPo, po, TxAppFlagEnum.APP_FAIL.getCode(), TxCompFlagEnum.COMPLETED.getCode(),
                    TxAppFlagEnum.APP_FAIL.getCode(), null, null, CancelOrderSrcEnum.FORCE_CANCEL.getCode(), null, true, true);
            refreshDealOrderStatusService.refleshDealOrderStatus(po.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
            return null;
        }

        // 中台自行撤单，后台失败；将对账订单中的申请状态设置为“申请失败”、对账状态设置为“对账匹配”；将订单明细中的交易申请状态设置为“申请失败”；记录份额变动明细信息
        if (TxAppFlagEnum.SELF_REVOCATION.getCode().equals(txAppFlag) && AppFlagEnum.FAILED.getKey().equals(appFlag)) {
            logger.info("DoTradeCheckService|check|appFlag unequal.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);
            tradePaymentCheckService.updateTradeCheck(orderDtlPo, po, TxAppFlagEnum.APP_FAIL.getCode(), TxCompFlagEnum.COMPLETED.getCode(),
                    TxAppFlagEnum.APP_FAIL.getCode(), null, null, CancelOrderSrcEnum.FORCE_CANCEL.getCode(), null, false, true);
            refreshDealOrderStatusService.refleshDealOrderStatus(po.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
            return null;
        }

        // 中台强制撤单，后台失败；将对账订单中的申请状态设置为“申请失败”、对账状态设置为“对账匹配”；将订单明细中的交易申请状态设置为“申请失败”；记录份额变动明细信息
        if (TxAppFlagEnum.FORCE_REVOCATION.getCode().equals(txAppFlag) && AppFlagEnum.FAILED.getKey().equals(appFlag)) {
            logger.info("DoTradeCheckService|check|appFlag unequal.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);
            tradePaymentCheckService.updateTradeCheck(orderDtlPo, po, TxAppFlagEnum.APP_FAIL.getCode(), TxCompFlagEnum.COMPLETED.getCode(),
                    TxAppFlagEnum.APP_FAIL.getCode(), null, null, CancelOrderSrcEnum.FORCE_CANCEL.getCode(), null, false, true);
            refreshDealOrderStatusService.refleshDealOrderStatus(po.getDealNo(), OrderUpdateType.ORDER_STATUS.getKey());
            return null;
        }

        logger.info("DoTradeCheckService|check|no update.txAppFlag:{},appFlag:{},submitDealNo:{}", txAppFlag, appFlag, submitDealNo);

        return null;
    }

    /**
     *
     * checkAmt:校验申请金额
     *
     * @param amount
     *            中台订单金额
     * @param appAmt
     *            后台订单申请金额
     * @return
     * @return boolean true:一致，false：不一致
     * <AUTHOR>
     * @date 2016年9月19日 下午3:03:22
     */
    private boolean checkAmt(BigDecimal amount, BigDecimal appAmt) {
        if (amount == null) {
            return true;
        }

        if (appAmt == null) {
            return true;
        }

        if (amount.compareTo(appAmt) != 0) {
            return false;
        }

        return true;
    }

    /**
     *
     * checkVol:校验申请份额
     *
     * @param vol
     *            中台订单份额
     * @param appVol
     *            后台订单申请份额
     * @return
     * @return boolean true:一致，false：不一致
     * <AUTHOR>
     * @date 2016年9月19日 下午3:04:28
     */
    private boolean checkVol(BigDecimal vol, BigDecimal appVol) {
        if (vol == null) {
            return true;
        }

        if (appVol == null) {
            return true;
        }

        if (vol.compareTo(appVol) != 0) {
            return false;
        }

        return true;
    }

    /**
     *
     * getTradeCheckDetailBean:创建交易对账异常明细信息
     *
     * @param checkOrder
     *            交易对账订单
     * @param tAppFlag
     *            后台申请标志
     * @param tAppAmt
     *            后台申请金额
     * @param tAppVol
     *            后台申请份额
     * @param checkError
     *            对账错误状态
     * @return
     * @return TradeCheckDetailBean
     * <AUTHOR>
     * @date 2016年9月20日 下午2:31:09
     */
    private TradeCheckDetailBean getTradeCheckDetailBean(SimuFundCheckOrderPo checkOrder, String tAppFlag, BigDecimal tAppAmt, BigDecimal tAppVol,
                                                         TradeCheckErrorEnum checkError) {
        TradeCheckDetailBean bean = new TradeCheckDetailBean();
        BeanUtils.copyProperties(checkOrder, bean);
        bean.setTaTradeDt(checkOrder.getTradeDt());
        bean.settAppFlag(tAppFlag);
        bean.settAppAmt(tAppAmt);
        bean.settAppVol(tAppVol);
        bean.setCheckErrorFlag(checkError.getCode());

        return bean;

    }

    /**
     *
     * getFundCheckOrderDiff:获取交易对账差异信息
     *
     * @param bean
     * @return
     * <AUTHOR>
     * @date 2017年7月6日 下午5:45:59
     */
    private HighTradeExceptionPo getFundCheckOrderDiff(TradeCheckDetailBean bean) {

        HighTradeExceptionPo orderDiff = new HighTradeExceptionPo();
        BeanUtils.copyProperties(bean, orderDiff);
        orderDiff.setTxCompFlag(bean.getCheckErrorFlag());
        orderDiff.setCheckFlag(Constant.CHECK_FLAG_YES);
        orderDiff.setOperator(Constant.OPERATOR_SYS);
        orderDiff.setChecker(Constant.OPERATOR_SYS);
        orderDiff.setTradeExcepSrc(TradeExcpSrcEnum.TRADE_CHECK_EXCP.getCode());
        orderDiff.setProcessStatus(DiffProcessStatusEnum.UNPROCESS.getKey());
        return orderDiff;
    }

}
