package com.howbuy.tms.high.batch.dao.po.order;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DealOrderExtendPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DealOrderExtendPoExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDealNoIsNull() {
            addCriterion("deal_no is null");
            return (Criteria) this;
        }

        public Criteria andDealNoIsNotNull() {
            addCriterion("deal_no is not null");
            return (Criteria) this;
        }

        public Criteria andDealNoEqualTo(String value) {
            addCriterion("deal_no =", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoNotEqualTo(String value) {
            addCriterion("deal_no <>", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoGreaterThan(String value) {
            addCriterion("deal_no >", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoGreaterThanOrEqualTo(String value) {
            addCriterion("deal_no >=", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoLessThan(String value) {
            addCriterion("deal_no <", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoLessThanOrEqualTo(String value) {
            addCriterion("deal_no <=", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoLike(String value) {
            addCriterion("deal_no like", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoNotLike(String value) {
            addCriterion("deal_no not like", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoIn(List<String> values) {
            addCriterion("deal_no in", values, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoNotIn(List<String> values) {
            addCriterion("deal_no not in", values, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoBetween(String value1, String value2) {
            addCriterion("deal_no between", value1, value2, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoNotBetween(String value1, String value2) {
            addCriterion("deal_no not between", value1, value2, "dealNo");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoIsNull() {
            addCriterion("transactor_id_no is null");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoIsNotNull() {
            addCriterion("transactor_id_no is not null");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoEqualTo(String value) {
            addCriterion("transactor_id_no =", value, "transactorIdNo");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoNotEqualTo(String value) {
            addCriterion("transactor_id_no <>", value, "transactorIdNo");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoGreaterThan(String value) {
            addCriterion("transactor_id_no >", value, "transactorIdNo");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoGreaterThanOrEqualTo(String value) {
            addCriterion("transactor_id_no >=", value, "transactorIdNo");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoLessThan(String value) {
            addCriterion("transactor_id_no <", value, "transactorIdNo");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoLessThanOrEqualTo(String value) {
            addCriterion("transactor_id_no <=", value, "transactorIdNo");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoLike(String value) {
            addCriterion("transactor_id_no like", value, "transactorIdNo");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoNotLike(String value) {
            addCriterion("transactor_id_no not like", value, "transactorIdNo");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoIn(List<String> values) {
            addCriterion("transactor_id_no in", values, "transactorIdNo");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoNotIn(List<String> values) {
            addCriterion("transactor_id_no not in", values, "transactorIdNo");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoBetween(String value1, String value2) {
            addCriterion("transactor_id_no between", value1, value2, "transactorIdNo");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoNotBetween(String value1, String value2) {
            addCriterion("transactor_id_no not between", value1, value2, "transactorIdNo");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeIsNull() {
            addCriterion("transactor_id_type is null");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeIsNotNull() {
            addCriterion("transactor_id_type is not null");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeEqualTo(String value) {
            addCriterion("transactor_id_type =", value, "transactorIdType");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeNotEqualTo(String value) {
            addCriterion("transactor_id_type <>", value, "transactorIdType");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeGreaterThan(String value) {
            addCriterion("transactor_id_type >", value, "transactorIdType");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeGreaterThanOrEqualTo(String value) {
            addCriterion("transactor_id_type >=", value, "transactorIdType");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeLessThan(String value) {
            addCriterion("transactor_id_type <", value, "transactorIdType");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeLessThanOrEqualTo(String value) {
            addCriterion("transactor_id_type <=", value, "transactorIdType");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeLike(String value) {
            addCriterion("transactor_id_type like", value, "transactorIdType");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeNotLike(String value) {
            addCriterion("transactor_id_type not like", value, "transactorIdType");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeIn(List<String> values) {
            addCriterion("transactor_id_type in", values, "transactorIdType");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeNotIn(List<String> values) {
            addCriterion("transactor_id_type not in", values, "transactorIdType");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeBetween(String value1, String value2) {
            addCriterion("transactor_id_type between", value1, value2, "transactorIdType");
            return (Criteria) this;
        }

        public Criteria andTransactorIdTypeNotBetween(String value1, String value2) {
            addCriterion("transactor_id_type not between", value1, value2, "transactorIdType");
            return (Criteria) this;
        }

        public Criteria andTransactorNameIsNull() {
            addCriterion("transactor_name is null");
            return (Criteria) this;
        }

        public Criteria andTransactorNameIsNotNull() {
            addCriterion("transactor_name is not null");
            return (Criteria) this;
        }

        public Criteria andTransactorNameEqualTo(String value) {
            addCriterion("transactor_name =", value, "transactorName");
            return (Criteria) this;
        }

        public Criteria andTransactorNameNotEqualTo(String value) {
            addCriterion("transactor_name <>", value, "transactorName");
            return (Criteria) this;
        }

        public Criteria andTransactorNameGreaterThan(String value) {
            addCriterion("transactor_name >", value, "transactorName");
            return (Criteria) this;
        }

        public Criteria andTransactorNameGreaterThanOrEqualTo(String value) {
            addCriterion("transactor_name >=", value, "transactorName");
            return (Criteria) this;
        }

        public Criteria andTransactorNameLessThan(String value) {
            addCriterion("transactor_name <", value, "transactorName");
            return (Criteria) this;
        }

        public Criteria andTransactorNameLessThanOrEqualTo(String value) {
            addCriterion("transactor_name <=", value, "transactorName");
            return (Criteria) this;
        }

        public Criteria andTransactorNameLike(String value) {
            addCriterion("transactor_name like", value, "transactorName");
            return (Criteria) this;
        }

        public Criteria andTransactorNameNotLike(String value) {
            addCriterion("transactor_name not like", value, "transactorName");
            return (Criteria) this;
        }

        public Criteria andTransactorNameIn(List<String> values) {
            addCriterion("transactor_name in", values, "transactorName");
            return (Criteria) this;
        }

        public Criteria andTransactorNameNotIn(List<String> values) {
            addCriterion("transactor_name not in", values, "transactorName");
            return (Criteria) this;
        }

        public Criteria andTransactorNameBetween(String value1, String value2) {
            addCriterion("transactor_name between", value1, value2, "transactorName");
            return (Criteria) this;
        }

        public Criteria andTransactorNameNotBetween(String value1, String value2) {
            addCriterion("transactor_name not between", value1, value2, "transactorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNoIsNull() {
            addCriterion("operator_no is null");
            return (Criteria) this;
        }

        public Criteria andOperatorNoIsNotNull() {
            addCriterion("operator_no is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorNoEqualTo(String value) {
            addCriterion("operator_no =", value, "operatorNo");
            return (Criteria) this;
        }

        public Criteria andOperatorNoNotEqualTo(String value) {
            addCriterion("operator_no <>", value, "operatorNo");
            return (Criteria) this;
        }

        public Criteria andOperatorNoGreaterThan(String value) {
            addCriterion("operator_no >", value, "operatorNo");
            return (Criteria) this;
        }

        public Criteria andOperatorNoGreaterThanOrEqualTo(String value) {
            addCriterion("operator_no >=", value, "operatorNo");
            return (Criteria) this;
        }

        public Criteria andOperatorNoLessThan(String value) {
            addCriterion("operator_no <", value, "operatorNo");
            return (Criteria) this;
        }

        public Criteria andOperatorNoLessThanOrEqualTo(String value) {
            addCriterion("operator_no <=", value, "operatorNo");
            return (Criteria) this;
        }

        public Criteria andOperatorNoLike(String value) {
            addCriterion("operator_no like", value, "operatorNo");
            return (Criteria) this;
        }

        public Criteria andOperatorNoNotLike(String value) {
            addCriterion("operator_no not like", value, "operatorNo");
            return (Criteria) this;
        }

        public Criteria andOperatorNoIn(List<String> values) {
            addCriterion("operator_no in", values, "operatorNo");
            return (Criteria) this;
        }

        public Criteria andOperatorNoNotIn(List<String> values) {
            addCriterion("operator_no not in", values, "operatorNo");
            return (Criteria) this;
        }

        public Criteria andOperatorNoBetween(String value1, String value2) {
            addCriterion("operator_no between", value1, value2, "operatorNo");
            return (Criteria) this;
        }

        public Criteria andOperatorNoNotBetween(String value1, String value2) {
            addCriterion("operator_no not between", value1, value2, "operatorNo");
            return (Criteria) this;
        }

        public Criteria andConsCodeIsNull() {
            addCriterion("cons_code is null");
            return (Criteria) this;
        }

        public Criteria andConsCodeIsNotNull() {
            addCriterion("cons_code is not null");
            return (Criteria) this;
        }

        public Criteria andConsCodeEqualTo(String value) {
            addCriterion("cons_code =", value, "consCode");
            return (Criteria) this;
        }

        public Criteria andConsCodeNotEqualTo(String value) {
            addCriterion("cons_code <>", value, "consCode");
            return (Criteria) this;
        }

        public Criteria andConsCodeGreaterThan(String value) {
            addCriterion("cons_code >", value, "consCode");
            return (Criteria) this;
        }

        public Criteria andConsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("cons_code >=", value, "consCode");
            return (Criteria) this;
        }

        public Criteria andConsCodeLessThan(String value) {
            addCriterion("cons_code <", value, "consCode");
            return (Criteria) this;
        }

        public Criteria andConsCodeLessThanOrEqualTo(String value) {
            addCriterion("cons_code <=", value, "consCode");
            return (Criteria) this;
        }

        public Criteria andConsCodeLike(String value) {
            addCriterion("cons_code like", value, "consCode");
            return (Criteria) this;
        }

        public Criteria andConsCodeNotLike(String value) {
            addCriterion("cons_code not like", value, "consCode");
            return (Criteria) this;
        }

        public Criteria andConsCodeIn(List<String> values) {
            addCriterion("cons_code in", values, "consCode");
            return (Criteria) this;
        }

        public Criteria andConsCodeNotIn(List<String> values) {
            addCriterion("cons_code not in", values, "consCode");
            return (Criteria) this;
        }

        public Criteria andConsCodeBetween(String value1, String value2) {
            addCriterion("cons_code between", value1, value2, "consCode");
            return (Criteria) this;
        }

        public Criteria andConsCodeNotBetween(String value1, String value2) {
            addCriterion("cons_code not between", value1, value2, "consCode");
            return (Criteria) this;
        }

        public Criteria andCreateDtmIsNull() {
            addCriterion("create_dtm is null");
            return (Criteria) this;
        }

        public Criteria andCreateDtmIsNotNull() {
            addCriterion("create_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDtmEqualTo(Date value) {
            addCriterion("create_dtm =", value, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmNotEqualTo(Date value) {
            addCriterion("create_dtm <>", value, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmGreaterThan(Date value) {
            addCriterion("create_dtm >", value, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmGreaterThanOrEqualTo(Date value) {
            addCriterion("create_dtm >=", value, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmLessThan(Date value) {
            addCriterion("create_dtm <", value, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmLessThanOrEqualTo(Date value) {
            addCriterion("create_dtm <=", value, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmIn(List<Date> values) {
            addCriterion("create_dtm in", values, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmNotIn(List<Date> values) {
            addCriterion("create_dtm not in", values, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmBetween(Date value1, Date value2) {
            addCriterion("create_dtm between", value1, value2, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmNotBetween(Date value1, Date value2) {
            addCriterion("create_dtm not between", value1, value2, "createDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmIsNull() {
            addCriterion("update_dtm is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmIsNotNull() {
            addCriterion("update_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmEqualTo(Date value) {
            addCriterion("update_dtm =", value, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmNotEqualTo(Date value) {
            addCriterion("update_dtm <>", value, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmGreaterThan(Date value) {
            addCriterion("update_dtm >", value, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmGreaterThanOrEqualTo(Date value) {
            addCriterion("update_dtm >=", value, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmLessThan(Date value) {
            addCriterion("update_dtm <", value, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmLessThanOrEqualTo(Date value) {
            addCriterion("update_dtm <=", value, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmIn(List<Date> values) {
            addCriterion("update_dtm in", values, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmNotIn(List<Date> values) {
            addCriterion("update_dtm not in", values, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmBetween(Date value1, Date value2) {
            addCriterion("update_dtm between", value1, value2, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmNotBetween(Date value1, Date value2) {
            addCriterion("update_dtm not between", value1, value2, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoIsNull() {
            addCriterion("appointment_deal_no is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoIsNotNull() {
            addCriterion("appointment_deal_no is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoEqualTo(String value) {
            addCriterion("appointment_deal_no =", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoNotEqualTo(String value) {
            addCriterion("appointment_deal_no <>", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoGreaterThan(String value) {
            addCriterion("appointment_deal_no >", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoGreaterThanOrEqualTo(String value) {
            addCriterion("appointment_deal_no >=", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoLessThan(String value) {
            addCriterion("appointment_deal_no <", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoLessThanOrEqualTo(String value) {
            addCriterion("appointment_deal_no <=", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoLike(String value) {
            addCriterion("appointment_deal_no like", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoNotLike(String value) {
            addCriterion("appointment_deal_no not like", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoIn(List<String> values) {
            addCriterion("appointment_deal_no in", values, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoNotIn(List<String> values) {
            addCriterion("appointment_deal_no not in", values, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoBetween(String value1, String value2) {
            addCriterion("appointment_deal_no between", value1, value2, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoNotBetween(String value1, String value2) {
            addCriterion("appointment_deal_no not between", value1, value2, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andRecStatIsNull() {
            addCriterion("rec_stat is null");
            return (Criteria) this;
        }

        public Criteria andRecStatIsNotNull() {
            addCriterion("rec_stat is not null");
            return (Criteria) this;
        }

        public Criteria andRecStatEqualTo(String value) {
            addCriterion("rec_stat =", value, "recStat");
            return (Criteria) this;
        }

        public Criteria andRecStatNotEqualTo(String value) {
            addCriterion("rec_stat <>", value, "recStat");
            return (Criteria) this;
        }

        public Criteria andRecStatGreaterThan(String value) {
            addCriterion("rec_stat >", value, "recStat");
            return (Criteria) this;
        }

        public Criteria andRecStatGreaterThanOrEqualTo(String value) {
            addCriterion("rec_stat >=", value, "recStat");
            return (Criteria) this;
        }

        public Criteria andRecStatLessThan(String value) {
            addCriterion("rec_stat <", value, "recStat");
            return (Criteria) this;
        }

        public Criteria andRecStatLessThanOrEqualTo(String value) {
            addCriterion("rec_stat <=", value, "recStat");
            return (Criteria) this;
        }

        public Criteria andRecStatLike(String value) {
            addCriterion("rec_stat like", value, "recStat");
            return (Criteria) this;
        }

        public Criteria andRecStatNotLike(String value) {
            addCriterion("rec_stat not like", value, "recStat");
            return (Criteria) this;
        }

        public Criteria andRecStatIn(List<String> values) {
            addCriterion("rec_stat in", values, "recStat");
            return (Criteria) this;
        }

        public Criteria andRecStatNotIn(List<String> values) {
            addCriterion("rec_stat not in", values, "recStat");
            return (Criteria) this;
        }

        public Criteria andRecStatBetween(String value1, String value2) {
            addCriterion("rec_stat between", value1, value2, "recStat");
            return (Criteria) this;
        }

        public Criteria andRecStatNotBetween(String value1, String value2) {
            addCriterion("rec_stat not between", value1, value2, "recStat");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmIsNull() {
            addCriterion("risk_ack_dtm is null");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmIsNotNull() {
            addCriterion("risk_ack_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmEqualTo(String value) {
            addCriterion("risk_ack_dtm =", value, "riskAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmNotEqualTo(String value) {
            addCriterion("risk_ack_dtm <>", value, "riskAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmGreaterThan(String value) {
            addCriterion("risk_ack_dtm >", value, "riskAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmGreaterThanOrEqualTo(String value) {
            addCriterion("risk_ack_dtm >=", value, "riskAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmLessThan(String value) {
            addCriterion("risk_ack_dtm <", value, "riskAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmLessThanOrEqualTo(String value) {
            addCriterion("risk_ack_dtm <=", value, "riskAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmLike(String value) {
            addCriterion("risk_ack_dtm like", value, "riskAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmNotLike(String value) {
            addCriterion("risk_ack_dtm not like", value, "riskAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmIn(List<String> values) {
            addCriterion("risk_ack_dtm in", values, "riskAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmNotIn(List<String> values) {
            addCriterion("risk_ack_dtm not in", values, "riskAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmBetween(String value1, String value2) {
            addCriterion("risk_ack_dtm between", value1, value2, "riskAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskAckDtmNotBetween(String value1, String value2) {
            addCriterion("risk_ack_dtm not between", value1, value2, "riskAckDtm");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmIsNull() {
            addCriterion("high_risk_tip_dtm is null");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmIsNotNull() {
            addCriterion("high_risk_tip_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmEqualTo(String value) {
            addCriterion("high_risk_tip_dtm =", value, "highRiskTipDtm");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmNotEqualTo(String value) {
            addCriterion("high_risk_tip_dtm <>", value, "highRiskTipDtm");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmGreaterThan(String value) {
            addCriterion("high_risk_tip_dtm >", value, "highRiskTipDtm");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmGreaterThanOrEqualTo(String value) {
            addCriterion("high_risk_tip_dtm >=", value, "highRiskTipDtm");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmLessThan(String value) {
            addCriterion("high_risk_tip_dtm <", value, "highRiskTipDtm");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmLessThanOrEqualTo(String value) {
            addCriterion("high_risk_tip_dtm <=", value, "highRiskTipDtm");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmLike(String value) {
            addCriterion("high_risk_tip_dtm like", value, "highRiskTipDtm");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmNotLike(String value) {
            addCriterion("high_risk_tip_dtm not like", value, "highRiskTipDtm");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmIn(List<String> values) {
            addCriterion("high_risk_tip_dtm in", values, "highRiskTipDtm");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmNotIn(List<String> values) {
            addCriterion("high_risk_tip_dtm not in", values, "highRiskTipDtm");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmBetween(String value1, String value2) {
            addCriterion("high_risk_tip_dtm between", value1, value2, "highRiskTipDtm");
            return (Criteria) this;
        }

        public Criteria andHighRiskTipDtmNotBetween(String value1, String value2) {
            addCriterion("high_risk_tip_dtm not between", value1, value2, "highRiskTipDtm");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmIsNull() {
            addCriterion("normal_cust_tip_dtm is null");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmIsNotNull() {
            addCriterion("normal_cust_tip_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmEqualTo(String value) {
            addCriterion("normal_cust_tip_dtm =", value, "normalCustTipDtm");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmNotEqualTo(String value) {
            addCriterion("normal_cust_tip_dtm <>", value, "normalCustTipDtm");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmGreaterThan(String value) {
            addCriterion("normal_cust_tip_dtm >", value, "normalCustTipDtm");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmGreaterThanOrEqualTo(String value) {
            addCriterion("normal_cust_tip_dtm >=", value, "normalCustTipDtm");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmLessThan(String value) {
            addCriterion("normal_cust_tip_dtm <", value, "normalCustTipDtm");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmLessThanOrEqualTo(String value) {
            addCriterion("normal_cust_tip_dtm <=", value, "normalCustTipDtm");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmLike(String value) {
            addCriterion("normal_cust_tip_dtm like", value, "normalCustTipDtm");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmNotLike(String value) {
            addCriterion("normal_cust_tip_dtm not like", value, "normalCustTipDtm");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmIn(List<String> values) {
            addCriterion("normal_cust_tip_dtm in", values, "normalCustTipDtm");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmNotIn(List<String> values) {
            addCriterion("normal_cust_tip_dtm not in", values, "normalCustTipDtm");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmBetween(String value1, String value2) {
            addCriterion("normal_cust_tip_dtm between", value1, value2, "normalCustTipDtm");
            return (Criteria) this;
        }

        public Criteria andNormalCustTipDtmNotBetween(String value1, String value2) {
            addCriterion("normal_cust_tip_dtm not between", value1, value2, "normalCustTipDtm");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagIsNull() {
            addCriterion("portfolio_flag is null");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagIsNotNull() {
            addCriterion("portfolio_flag is not null");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagEqualTo(String value) {
            addCriterion("portfolio_flag =", value, "portfolioFlag");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagNotEqualTo(String value) {
            addCriterion("portfolio_flag <>", value, "portfolioFlag");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagGreaterThan(String value) {
            addCriterion("portfolio_flag >", value, "portfolioFlag");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagGreaterThanOrEqualTo(String value) {
            addCriterion("portfolio_flag >=", value, "portfolioFlag");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagLessThan(String value) {
            addCriterion("portfolio_flag <", value, "portfolioFlag");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagLessThanOrEqualTo(String value) {
            addCriterion("portfolio_flag <=", value, "portfolioFlag");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagLike(String value) {
            addCriterion("portfolio_flag like", value, "portfolioFlag");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagNotLike(String value) {
            addCriterion("portfolio_flag not like", value, "portfolioFlag");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagIn(List<String> values) {
            addCriterion("portfolio_flag in", values, "portfolioFlag");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagNotIn(List<String> values) {
            addCriterion("portfolio_flag not in", values, "portfolioFlag");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagBetween(String value1, String value2) {
            addCriterion("portfolio_flag between", value1, value2, "portfolioFlag");
            return (Criteria) this;
        }

        public Criteria andPortfolioFlagNotBetween(String value1, String value2) {
            addCriterion("portfolio_flag not between", value1, value2, "portfolioFlag");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmIsNull() {
            addCriterion("risk_reveal_book_ack_dtm is null");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmIsNotNull() {
            addCriterion("risk_reveal_book_ack_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmEqualTo(String value) {
            addCriterion("risk_reveal_book_ack_dtm =", value, "riskRevealBookAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmNotEqualTo(String value) {
            addCriterion("risk_reveal_book_ack_dtm <>", value, "riskRevealBookAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmGreaterThan(String value) {
            addCriterion("risk_reveal_book_ack_dtm >", value, "riskRevealBookAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmGreaterThanOrEqualTo(String value) {
            addCriterion("risk_reveal_book_ack_dtm >=", value, "riskRevealBookAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmLessThan(String value) {
            addCriterion("risk_reveal_book_ack_dtm <", value, "riskRevealBookAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmLessThanOrEqualTo(String value) {
            addCriterion("risk_reveal_book_ack_dtm <=", value, "riskRevealBookAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmLike(String value) {
            addCriterion("risk_reveal_book_ack_dtm like", value, "riskRevealBookAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmNotLike(String value) {
            addCriterion("risk_reveal_book_ack_dtm not like", value, "riskRevealBookAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmIn(List<String> values) {
            addCriterion("risk_reveal_book_ack_dtm in", values, "riskRevealBookAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmNotIn(List<String> values) {
            addCriterion("risk_reveal_book_ack_dtm not in", values, "riskRevealBookAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmBetween(String value1, String value2) {
            addCriterion("risk_reveal_book_ack_dtm between", value1, value2, "riskRevealBookAckDtm");
            return (Criteria) this;
        }

        public Criteria andRiskRevealBookAckDtmNotBetween(String value1, String value2) {
            addCriterion("risk_reveal_book_ack_dtm not between", value1, value2, "riskRevealBookAckDtm");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeIsNull() {
            addCriterion("transfer_tube_busi_type is null");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeIsNotNull() {
            addCriterion("transfer_tube_busi_type is not null");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeEqualTo(String value) {
            addCriterion("transfer_tube_busi_type =", value, "transferTubeBusiType");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeNotEqualTo(String value) {
            addCriterion("transfer_tube_busi_type <>", value, "transferTubeBusiType");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeGreaterThan(String value) {
            addCriterion("transfer_tube_busi_type >", value, "transferTubeBusiType");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeGreaterThanOrEqualTo(String value) {
            addCriterion("transfer_tube_busi_type >=", value, "transferTubeBusiType");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeLessThan(String value) {
            addCriterion("transfer_tube_busi_type <", value, "transferTubeBusiType");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeLessThanOrEqualTo(String value) {
            addCriterion("transfer_tube_busi_type <=", value, "transferTubeBusiType");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeLike(String value) {
            addCriterion("transfer_tube_busi_type like", value, "transferTubeBusiType");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeNotLike(String value) {
            addCriterion("transfer_tube_busi_type not like", value, "transferTubeBusiType");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeIn(List<String> values) {
            addCriterion("transfer_tube_busi_type in", values, "transferTubeBusiType");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeNotIn(List<String> values) {
            addCriterion("transfer_tube_busi_type not in", values, "transferTubeBusiType");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeBetween(String value1, String value2) {
            addCriterion("transfer_tube_busi_type between", value1, value2, "transferTubeBusiType");
            return (Criteria) this;
        }

        public Criteria andTransferTubeBusiTypeNotBetween(String value1, String value2) {
            addCriterion("transfer_tube_busi_type not between", value1, value2, "transferTubeBusiType");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeIsNull() {
            addCriterion("t_outletcode is null");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeIsNotNull() {
            addCriterion("t_outletcode is not null");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeEqualTo(String value) {
            addCriterion("t_outletcode =", value, "tOutletcode");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeNotEqualTo(String value) {
            addCriterion("t_outletcode <>", value, "tOutletcode");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeGreaterThan(String value) {
            addCriterion("t_outletcode >", value, "tOutletcode");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeGreaterThanOrEqualTo(String value) {
            addCriterion("t_outletcode >=", value, "tOutletcode");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeLessThan(String value) {
            addCriterion("t_outletcode <", value, "tOutletcode");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeLessThanOrEqualTo(String value) {
            addCriterion("t_outletcode <=", value, "tOutletcode");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeLike(String value) {
            addCriterion("t_outletcode like", value, "tOutletcode");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeNotLike(String value) {
            addCriterion("t_outletcode not like", value, "tOutletcode");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeIn(List<String> values) {
            addCriterion("t_outletcode in", values, "tOutletcode");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeNotIn(List<String> values) {
            addCriterion("t_outletcode not in", values, "tOutletcode");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeBetween(String value1, String value2) {
            addCriterion("t_outletcode between", value1, value2, "tOutletcode");
            return (Criteria) this;
        }

        public Criteria andTOutletcodeNotBetween(String value1, String value2) {
            addCriterion("t_outletcode not between", value1, value2, "tOutletcode");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoIsNull() {
            addCriterion("t_seller_txacctno is null");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoIsNotNull() {
            addCriterion("t_seller_txacctno is not null");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoEqualTo(String value) {
            addCriterion("t_seller_txacctno =", value, "tSellerTxacctno");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoNotEqualTo(String value) {
            addCriterion("t_seller_txacctno <>", value, "tSellerTxacctno");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoGreaterThan(String value) {
            addCriterion("t_seller_txacctno >", value, "tSellerTxacctno");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoGreaterThanOrEqualTo(String value) {
            addCriterion("t_seller_txacctno >=", value, "tSellerTxacctno");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoLessThan(String value) {
            addCriterion("t_seller_txacctno <", value, "tSellerTxacctno");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoLessThanOrEqualTo(String value) {
            addCriterion("t_seller_txacctno <=", value, "tSellerTxacctno");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoLike(String value) {
            addCriterion("t_seller_txacctno like", value, "tSellerTxacctno");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoNotLike(String value) {
            addCriterion("t_seller_txacctno not like", value, "tSellerTxacctno");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoIn(List<String> values) {
            addCriterion("t_seller_txacctno in", values, "tSellerTxacctno");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoNotIn(List<String> values) {
            addCriterion("t_seller_txacctno not in", values, "tSellerTxacctno");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoBetween(String value1, String value2) {
            addCriterion("t_seller_txacctno between", value1, value2, "tSellerTxacctno");
            return (Criteria) this;
        }

        public Criteria andTSellerTxacctnoNotBetween(String value1, String value2) {
            addCriterion("t_seller_txacctno not between", value1, value2, "tSellerTxacctno");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeIsNull() {
            addCriterion("t_seller_code is null");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeIsNotNull() {
            addCriterion("t_seller_code is not null");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeEqualTo(String value) {
            addCriterion("t_seller_code =", value, "tSellerCode");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeNotEqualTo(String value) {
            addCriterion("t_seller_code <>", value, "tSellerCode");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeGreaterThan(String value) {
            addCriterion("t_seller_code >", value, "tSellerCode");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("t_seller_code >=", value, "tSellerCode");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeLessThan(String value) {
            addCriterion("t_seller_code <", value, "tSellerCode");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeLessThanOrEqualTo(String value) {
            addCriterion("t_seller_code <=", value, "tSellerCode");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeLike(String value) {
            addCriterion("t_seller_code like", value, "tSellerCode");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeNotLike(String value) {
            addCriterion("t_seller_code not like", value, "tSellerCode");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeIn(List<String> values) {
            addCriterion("t_seller_code in", values, "tSellerCode");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeNotIn(List<String> values) {
            addCriterion("t_seller_code not in", values, "tSellerCode");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeBetween(String value1, String value2) {
            addCriterion("t_seller_code between", value1, value2, "tSellerCode");
            return (Criteria) this;
        }

        public Criteria andTSellerCodeNotBetween(String value1, String value2) {
            addCriterion("t_seller_code not between", value1, value2, "tSellerCode");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoIsNull() {
            addCriterion("original_app_dealno is null");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoIsNotNull() {
            addCriterion("original_app_dealno is not null");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoEqualTo(String value) {
            addCriterion("original_app_dealno =", value, "originalAppDealno");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoNotEqualTo(String value) {
            addCriterion("original_app_dealno <>", value, "originalAppDealno");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoGreaterThan(String value) {
            addCriterion("original_app_dealno >", value, "originalAppDealno");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoGreaterThanOrEqualTo(String value) {
            addCriterion("original_app_dealno >=", value, "originalAppDealno");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoLessThan(String value) {
            addCriterion("original_app_dealno <", value, "originalAppDealno");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoLessThanOrEqualTo(String value) {
            addCriterion("original_app_dealno <=", value, "originalAppDealno");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoLike(String value) {
            addCriterion("original_app_dealno like", value, "originalAppDealno");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoNotLike(String value) {
            addCriterion("original_app_dealno not like", value, "originalAppDealno");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoIn(List<String> values) {
            addCriterion("original_app_dealno in", values, "originalAppDealno");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoNotIn(List<String> values) {
            addCriterion("original_app_dealno not in", values, "originalAppDealno");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoBetween(String value1, String value2) {
            addCriterion("original_app_dealno between", value1, value2, "originalAppDealno");
            return (Criteria) this;
        }

        public Criteria andOriginalAppDealnoNotBetween(String value1, String value2) {
            addCriterion("original_app_dealno not between", value1, value2, "originalAppDealno");
            return (Criteria) this;
        }

        public Criteria andRiskFlagIsNull() {
            addCriterion("risk_flag is null");
            return (Criteria) this;
        }

        public Criteria andRiskFlagIsNotNull() {
            addCriterion("risk_flag is not null");
            return (Criteria) this;
        }

        public Criteria andRiskFlagEqualTo(String value) {
            addCriterion("risk_flag =", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagNotEqualTo(String value) {
            addCriterion("risk_flag <>", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagGreaterThan(String value) {
            addCriterion("risk_flag >", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagGreaterThanOrEqualTo(String value) {
            addCriterion("risk_flag >=", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagLessThan(String value) {
            addCriterion("risk_flag <", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagLessThanOrEqualTo(String value) {
            addCriterion("risk_flag <=", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagLike(String value) {
            addCriterion("risk_flag like", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagNotLike(String value) {
            addCriterion("risk_flag not like", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagIn(List<String> values) {
            addCriterion("risk_flag in", values, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagNotIn(List<String> values) {
            addCriterion("risk_flag not in", values, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagBetween(String value1, String value2) {
            addCriterion("risk_flag between", value1, value2, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagNotBetween(String value1, String value2) {
            addCriterion("risk_flag not between", value1, value2, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionIsNull() {
            addCriterion("redeem_direction is null");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionIsNotNull() {
            addCriterion("redeem_direction is not null");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionEqualTo(String value) {
            addCriterion("redeem_direction =", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionNotEqualTo(String value) {
            addCriterion("redeem_direction <>", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionGreaterThan(String value) {
            addCriterion("redeem_direction >", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionGreaterThanOrEqualTo(String value) {
            addCriterion("redeem_direction >=", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionLessThan(String value) {
            addCriterion("redeem_direction <", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionLessThanOrEqualTo(String value) {
            addCriterion("redeem_direction <=", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionLike(String value) {
            addCriterion("redeem_direction like", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionNotLike(String value) {
            addCriterion("redeem_direction not like", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionIn(List<String> values) {
            addCriterion("redeem_direction in", values, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionNotIn(List<String> values) {
            addCriterion("redeem_direction not in", values, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionBetween(String value1, String value2) {
            addCriterion("redeem_direction between", value1, value2, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionNotBetween(String value1, String value2) {
            addCriterion("redeem_direction not between", value1, value2, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andInfoAckDtmIsNull() {
            addCriterion("info_ack_dtm is null");
            return (Criteria) this;
        }

        public Criteria andInfoAckDtmIsNotNull() {
            addCriterion("info_ack_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andInfoAckDtmEqualTo(Date value) {
            addCriterion("info_ack_dtm =", value, "infoAckDtm");
            return (Criteria) this;
        }

        public Criteria andInfoAckDtmNotEqualTo(Date value) {
            addCriterion("info_ack_dtm <>", value, "infoAckDtm");
            return (Criteria) this;
        }

        public Criteria andInfoAckDtmGreaterThan(Date value) {
            addCriterion("info_ack_dtm >", value, "infoAckDtm");
            return (Criteria) this;
        }

        public Criteria andInfoAckDtmGreaterThanOrEqualTo(Date value) {
            addCriterion("info_ack_dtm >=", value, "infoAckDtm");
            return (Criteria) this;
        }

        public Criteria andInfoAckDtmLessThan(Date value) {
            addCriterion("info_ack_dtm <", value, "infoAckDtm");
            return (Criteria) this;
        }

        public Criteria andInfoAckDtmLessThanOrEqualTo(Date value) {
            addCriterion("info_ack_dtm <=", value, "infoAckDtm");
            return (Criteria) this;
        }

        public Criteria andInfoAckDtmIn(List<Date> values) {
            addCriterion("info_ack_dtm in", values, "infoAckDtm");
            return (Criteria) this;
        }

        public Criteria andInfoAckDtmNotIn(List<Date> values) {
            addCriterion("info_ack_dtm not in", values, "infoAckDtm");
            return (Criteria) this;
        }

        public Criteria andInfoAckDtmBetween(Date value1, Date value2) {
            addCriterion("info_ack_dtm between", value1, value2, "infoAckDtm");
            return (Criteria) this;
        }

        public Criteria andInfoAckDtmNotBetween(Date value1, Date value2) {
            addCriterion("info_ack_dtm not between", value1, value2, "infoAckDtm");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameIsNull() {
            addCriterion("info_ack_file_name is null");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameIsNotNull() {
            addCriterion("info_ack_file_name is not null");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameEqualTo(String value) {
            addCriterion("info_ack_file_name =", value, "infoAckFileName");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameNotEqualTo(String value) {
            addCriterion("info_ack_file_name <>", value, "infoAckFileName");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameGreaterThan(String value) {
            addCriterion("info_ack_file_name >", value, "infoAckFileName");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameGreaterThanOrEqualTo(String value) {
            addCriterion("info_ack_file_name >=", value, "infoAckFileName");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameLessThan(String value) {
            addCriterion("info_ack_file_name <", value, "infoAckFileName");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameLessThanOrEqualTo(String value) {
            addCriterion("info_ack_file_name <=", value, "infoAckFileName");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameLike(String value) {
            addCriterion("info_ack_file_name like", value, "infoAckFileName");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameNotLike(String value) {
            addCriterion("info_ack_file_name not like", value, "infoAckFileName");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameIn(List<String> values) {
            addCriterion("info_ack_file_name in", values, "infoAckFileName");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameNotIn(List<String> values) {
            addCriterion("info_ack_file_name not in", values, "infoAckFileName");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameBetween(String value1, String value2) {
            addCriterion("info_ack_file_name between", value1, value2, "infoAckFileName");
            return (Criteria) this;
        }

        public Criteria andInfoAckFileNameNotBetween(String value1, String value2) {
            addCriterion("info_ack_file_name not between", value1, value2, "infoAckFileName");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagIsNull() {
            addCriterion("check_param_flag is null");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagIsNotNull() {
            addCriterion("check_param_flag is not null");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagEqualTo(String value) {
            addCriterion("check_param_flag =", value, "checkParamFlag");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagNotEqualTo(String value) {
            addCriterion("check_param_flag <>", value, "checkParamFlag");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagGreaterThan(String value) {
            addCriterion("check_param_flag >", value, "checkParamFlag");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagGreaterThanOrEqualTo(String value) {
            addCriterion("check_param_flag >=", value, "checkParamFlag");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagLessThan(String value) {
            addCriterion("check_param_flag <", value, "checkParamFlag");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagLessThanOrEqualTo(String value) {
            addCriterion("check_param_flag <=", value, "checkParamFlag");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagLike(String value) {
            addCriterion("check_param_flag like", value, "checkParamFlag");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagNotLike(String value) {
            addCriterion("check_param_flag not like", value, "checkParamFlag");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagIn(List<String> values) {
            addCriterion("check_param_flag in", values, "checkParamFlag");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagNotIn(List<String> values) {
            addCriterion("check_param_flag not in", values, "checkParamFlag");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagBetween(String value1, String value2) {
            addCriterion("check_param_flag between", value1, value2, "checkParamFlag");
            return (Criteria) this;
        }

        public Criteria andCheckParamFlagNotBetween(String value1, String value2) {
            addCriterion("check_param_flag not between", value1, value2, "checkParamFlag");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherIsNull() {
            addCriterion("transactor_id_no_cipher is null");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherIsNotNull() {
            addCriterion("transactor_id_no_cipher is not null");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherEqualTo(String value) {
            addCriterion("transactor_id_no_cipher =", value, "transactorIdNoCipher");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherNotEqualTo(String value) {
            addCriterion("transactor_id_no_cipher <>", value, "transactorIdNoCipher");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherGreaterThan(String value) {
            addCriterion("transactor_id_no_cipher >", value, "transactorIdNoCipher");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherGreaterThanOrEqualTo(String value) {
            addCriterion("transactor_id_no_cipher >=", value, "transactorIdNoCipher");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherLessThan(String value) {
            addCriterion("transactor_id_no_cipher <", value, "transactorIdNoCipher");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherLessThanOrEqualTo(String value) {
            addCriterion("transactor_id_no_cipher <=", value, "transactorIdNoCipher");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherLike(String value) {
            addCriterion("transactor_id_no_cipher like", value, "transactorIdNoCipher");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherNotLike(String value) {
            addCriterion("transactor_id_no_cipher not like", value, "transactorIdNoCipher");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherIn(List<String> values) {
            addCriterion("transactor_id_no_cipher in", values, "transactorIdNoCipher");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherNotIn(List<String> values) {
            addCriterion("transactor_id_no_cipher not in", values, "transactorIdNoCipher");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherBetween(String value1, String value2) {
            addCriterion("transactor_id_no_cipher between", value1, value2, "transactorIdNoCipher");
            return (Criteria) this;
        }

        public Criteria andTransactorIdNoCipherNotBetween(String value1, String value2) {
            addCriterion("transactor_id_no_cipher not between", value1, value2, "transactorIdNoCipher");
            return (Criteria) this;
        }

        public Criteria andOprReasonIsNull() {
            addCriterion("opr_reason is null");
            return (Criteria) this;
        }

        public Criteria andOprReasonIsNotNull() {
            addCriterion("opr_reason is not null");
            return (Criteria) this;
        }

        public Criteria andOprReasonEqualTo(String value) {
            addCriterion("opr_reason =", value, "oprReason");
            return (Criteria) this;
        }

        public Criteria andOprReasonNotEqualTo(String value) {
            addCriterion("opr_reason <>", value, "oprReason");
            return (Criteria) this;
        }

        public Criteria andOprReasonGreaterThan(String value) {
            addCriterion("opr_reason >", value, "oprReason");
            return (Criteria) this;
        }

        public Criteria andOprReasonGreaterThanOrEqualTo(String value) {
            addCriterion("opr_reason >=", value, "oprReason");
            return (Criteria) this;
        }

        public Criteria andOprReasonLessThan(String value) {
            addCriterion("opr_reason <", value, "oprReason");
            return (Criteria) this;
        }

        public Criteria andOprReasonLessThanOrEqualTo(String value) {
            addCriterion("opr_reason <=", value, "oprReason");
            return (Criteria) this;
        }

        public Criteria andOprReasonLike(String value) {
            addCriterion("opr_reason like", value, "oprReason");
            return (Criteria) this;
        }

        public Criteria andOprReasonNotLike(String value) {
            addCriterion("opr_reason not like", value, "oprReason");
            return (Criteria) this;
        }

        public Criteria andOprReasonIn(List<String> values) {
            addCriterion("opr_reason in", values, "oprReason");
            return (Criteria) this;
        }

        public Criteria andOprReasonNotIn(List<String> values) {
            addCriterion("opr_reason not in", values, "oprReason");
            return (Criteria) this;
        }

        public Criteria andOprReasonBetween(String value1, String value2) {
            addCriterion("opr_reason between", value1, value2, "oprReason");
            return (Criteria) this;
        }

        public Criteria andOprReasonNotBetween(String value1, String value2) {
            addCriterion("opr_reason not between", value1, value2, "oprReason");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelIsNull() {
            addCriterion("cust_risk_level is null");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelIsNotNull() {
            addCriterion("cust_risk_level is not null");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelEqualTo(String value) {
            addCriterion("cust_risk_level =", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelNotEqualTo(String value) {
            addCriterion("cust_risk_level <>", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelGreaterThan(String value) {
            addCriterion("cust_risk_level >", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelGreaterThanOrEqualTo(String value) {
            addCriterion("cust_risk_level >=", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelLessThan(String value) {
            addCriterion("cust_risk_level <", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelLessThanOrEqualTo(String value) {
            addCriterion("cust_risk_level <=", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelLike(String value) {
            addCriterion("cust_risk_level like", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelNotLike(String value) {
            addCriterion("cust_risk_level not like", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelIn(List<String> values) {
            addCriterion("cust_risk_level in", values, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelNotIn(List<String> values) {
            addCriterion("cust_risk_level not in", values, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelBetween(String value1, String value2) {
            addCriterion("cust_risk_level between", value1, value2, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelNotBetween(String value1, String value2) {
            addCriterion("cust_risk_level not between", value1, value2, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmIsNull() {
            addCriterion("invest_ack_dtm is null");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmIsNotNull() {
            addCriterion("invest_ack_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmEqualTo(String value) {
            addCriterion("invest_ack_dtm =", value, "investAckDtm");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmNotEqualTo(String value) {
            addCriterion("invest_ack_dtm <>", value, "investAckDtm");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmGreaterThan(String value) {
            addCriterion("invest_ack_dtm >", value, "investAckDtm");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmGreaterThanOrEqualTo(String value) {
            addCriterion("invest_ack_dtm >=", value, "investAckDtm");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmLessThan(String value) {
            addCriterion("invest_ack_dtm <", value, "investAckDtm");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmLessThanOrEqualTo(String value) {
            addCriterion("invest_ack_dtm <=", value, "investAckDtm");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmLike(String value) {
            addCriterion("invest_ack_dtm like", value, "investAckDtm");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmNotLike(String value) {
            addCriterion("invest_ack_dtm not like", value, "investAckDtm");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmIn(List<String> values) {
            addCriterion("invest_ack_dtm in", values, "investAckDtm");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmNotIn(List<String> values) {
            addCriterion("invest_ack_dtm not in", values, "investAckDtm");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmBetween(String value1, String value2) {
            addCriterion("invest_ack_dtm between", value1, value2, "investAckDtm");
            return (Criteria) this;
        }

        public Criteria andInvestAckDtmNotBetween(String value1, String value2) {
            addCriterion("invest_ack_dtm not between", value1, value2, "investAckDtm");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeIsNull() {
            addCriterion("expire_op_type is null");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeIsNotNull() {
            addCriterion("expire_op_type is not null");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeEqualTo(String value) {
            addCriterion("expire_op_type =", value, "expireOpType");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeNotEqualTo(String value) {
            addCriterion("expire_op_type <>", value, "expireOpType");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeGreaterThan(String value) {
            addCriterion("expire_op_type >", value, "expireOpType");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeGreaterThanOrEqualTo(String value) {
            addCriterion("expire_op_type >=", value, "expireOpType");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeLessThan(String value) {
            addCriterion("expire_op_type <", value, "expireOpType");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeLessThanOrEqualTo(String value) {
            addCriterion("expire_op_type <=", value, "expireOpType");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeLike(String value) {
            addCriterion("expire_op_type like", value, "expireOpType");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeNotLike(String value) {
            addCriterion("expire_op_type not like", value, "expireOpType");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeIn(List<String> values) {
            addCriterion("expire_op_type in", values, "expireOpType");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeNotIn(List<String> values) {
            addCriterion("expire_op_type not in", values, "expireOpType");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeBetween(String value1, String value2) {
            addCriterion("expire_op_type between", value1, value2, "expireOpType");
            return (Criteria) this;
        }

        public Criteria andExpireOpTypeNotBetween(String value1, String value2) {
            addCriterion("expire_op_type not between", value1, value2, "expireOpType");
            return (Criteria) this;
        }

        public Criteria andCouponIdIsNull() {
            addCriterion("coupon_id is null");
            return (Criteria) this;
        }

        public Criteria andCouponIdIsNotNull() {
            addCriterion("coupon_id is not null");
            return (Criteria) this;
        }

        public Criteria andCouponIdEqualTo(String value) {
            addCriterion("coupon_id =", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdNotEqualTo(String value) {
            addCriterion("coupon_id <>", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdGreaterThan(String value) {
            addCriterion("coupon_id >", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdGreaterThanOrEqualTo(String value) {
            addCriterion("coupon_id >=", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdLessThan(String value) {
            addCriterion("coupon_id <", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdLessThanOrEqualTo(String value) {
            addCriterion("coupon_id <=", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdLike(String value) {
            addCriterion("coupon_id like", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdNotLike(String value) {
            addCriterion("coupon_id not like", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdIn(List<String> values) {
            addCriterion("coupon_id in", values, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdNotIn(List<String> values) {
            addCriterion("coupon_id not in", values, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdBetween(String value1, String value2) {
            addCriterion("coupon_id between", value1, value2, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdNotBetween(String value1, String value2) {
            addCriterion("coupon_id not between", value1, value2, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponTypeIsNull() {
            addCriterion("coupon_type is null");
            return (Criteria) this;
        }

        public Criteria andCouponTypeIsNotNull() {
            addCriterion("coupon_type is not null");
            return (Criteria) this;
        }

        public Criteria andCouponTypeEqualTo(String value) {
            addCriterion("coupon_type =", value, "couponType");
            return (Criteria) this;
        }

        public Criteria andCouponTypeNotEqualTo(String value) {
            addCriterion("coupon_type <>", value, "couponType");
            return (Criteria) this;
        }

        public Criteria andCouponTypeGreaterThan(String value) {
            addCriterion("coupon_type >", value, "couponType");
            return (Criteria) this;
        }

        public Criteria andCouponTypeGreaterThanOrEqualTo(String value) {
            addCriterion("coupon_type >=", value, "couponType");
            return (Criteria) this;
        }

        public Criteria andCouponTypeLessThan(String value) {
            addCriterion("coupon_type <", value, "couponType");
            return (Criteria) this;
        }

        public Criteria andCouponTypeLessThanOrEqualTo(String value) {
            addCriterion("coupon_type <=", value, "couponType");
            return (Criteria) this;
        }

        public Criteria andCouponTypeLike(String value) {
            addCriterion("coupon_type like", value, "couponType");
            return (Criteria) this;
        }

        public Criteria andCouponTypeNotLike(String value) {
            addCriterion("coupon_type not like", value, "couponType");
            return (Criteria) this;
        }

        public Criteria andCouponTypeIn(List<String> values) {
            addCriterion("coupon_type in", values, "couponType");
            return (Criteria) this;
        }

        public Criteria andCouponTypeNotIn(List<String> values) {
            addCriterion("coupon_type not in", values, "couponType");
            return (Criteria) this;
        }

        public Criteria andCouponTypeBetween(String value1, String value2) {
            addCriterion("coupon_type between", value1, value2, "couponType");
            return (Criteria) this;
        }

        public Criteria andCouponTypeNotBetween(String value1, String value2) {
            addCriterion("coupon_type not between", value1, value2, "couponType");
            return (Criteria) this;
        }

        public Criteria andCouponDiscountRateIsNull() {
            addCriterion("coupon_discount_rate is null");
            return (Criteria) this;
        }

        public Criteria andCouponDiscountRateIsNotNull() {
            addCriterion("coupon_discount_rate is not null");
            return (Criteria) this;
        }

        public Criteria andCouponDiscountRateEqualTo(BigDecimal value) {
            addCriterion("coupon_discount_rate =", value, "couponDiscountRate");
            return (Criteria) this;
        }

        public Criteria andCouponDiscountRateNotEqualTo(BigDecimal value) {
            addCriterion("coupon_discount_rate <>", value, "couponDiscountRate");
            return (Criteria) this;
        }

        public Criteria andCouponDiscountRateGreaterThan(BigDecimal value) {
            addCriterion("coupon_discount_rate >", value, "couponDiscountRate");
            return (Criteria) this;
        }

        public Criteria andCouponDiscountRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("coupon_discount_rate >=", value, "couponDiscountRate");
            return (Criteria) this;
        }

        public Criteria andCouponDiscountRateLessThan(BigDecimal value) {
            addCriterion("coupon_discount_rate <", value, "couponDiscountRate");
            return (Criteria) this;
        }

        public Criteria andCouponDiscountRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("coupon_discount_rate <=", value, "couponDiscountRate");
            return (Criteria) this;
        }

        public Criteria andCouponDiscountRateIn(List<BigDecimal> values) {
            addCriterion("coupon_discount_rate in", values, "couponDiscountRate");
            return (Criteria) this;
        }

        public Criteria andCouponDiscountRateNotIn(List<BigDecimal> values) {
            addCriterion("coupon_discount_rate not in", values, "couponDiscountRate");
            return (Criteria) this;
        }

        public Criteria andCouponDiscountRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("coupon_discount_rate between", value1, value2, "couponDiscountRate");
            return (Criteria) this;
        }

        public Criteria andCouponDiscountRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("coupon_discount_rate not between", value1, value2, "couponDiscountRate");
            return (Criteria) this;
        }

        public Criteria andMaxDeductibleIsNull() {
            addCriterion("max_deductible is null");
            return (Criteria) this;
        }

        public Criteria andMaxDeductibleIsNotNull() {
            addCriterion("max_deductible is not null");
            return (Criteria) this;
        }

        public Criteria andMaxDeductibleEqualTo(BigDecimal value) {
            addCriterion("max_deductible =", value, "maxDeductible");
            return (Criteria) this;
        }

        public Criteria andMaxDeductibleNotEqualTo(BigDecimal value) {
            addCriterion("max_deductible <>", value, "maxDeductible");
            return (Criteria) this;
        }

        public Criteria andMaxDeductibleGreaterThan(BigDecimal value) {
            addCriterion("max_deductible >", value, "maxDeductible");
            return (Criteria) this;
        }

        public Criteria andMaxDeductibleGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("max_deductible >=", value, "maxDeductible");
            return (Criteria) this;
        }

        public Criteria andMaxDeductibleLessThan(BigDecimal value) {
            addCriterion("max_deductible <", value, "maxDeductible");
            return (Criteria) this;
        }

        public Criteria andMaxDeductibleLessThanOrEqualTo(BigDecimal value) {
            addCriterion("max_deductible <=", value, "maxDeductible");
            return (Criteria) this;
        }

        public Criteria andMaxDeductibleIn(List<BigDecimal> values) {
            addCriterion("max_deductible in", values, "maxDeductible");
            return (Criteria) this;
        }

        public Criteria andMaxDeductibleNotIn(List<BigDecimal> values) {
            addCriterion("max_deductible not in", values, "maxDeductible");
            return (Criteria) this;
        }

        public Criteria andMaxDeductibleBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_deductible between", value1, value2, "maxDeductible");
            return (Criteria) this;
        }

        public Criteria andMaxDeductibleNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_deductible not between", value1, value2, "maxDeductible");
            return (Criteria) this;
        }

        public Criteria andLiveAddressIsNull() {
            addCriterion("live_address is null");
            return (Criteria) this;
        }

        public Criteria andLiveAddressIsNotNull() {
            addCriterion("live_address is not null");
            return (Criteria) this;
        }

        public Criteria andLiveAddressEqualTo(String value) {
            addCriterion("live_address =", value, "liveAddress");
            return (Criteria) this;
        }

        public Criteria andLiveAddressNotEqualTo(String value) {
            addCriterion("live_address <>", value, "liveAddress");
            return (Criteria) this;
        }

        public Criteria andLiveAddressGreaterThan(String value) {
            addCriterion("live_address >", value, "liveAddress");
            return (Criteria) this;
        }

        public Criteria andLiveAddressGreaterThanOrEqualTo(String value) {
            addCriterion("live_address >=", value, "liveAddress");
            return (Criteria) this;
        }

        public Criteria andLiveAddressLessThan(String value) {
            addCriterion("live_address <", value, "liveAddress");
            return (Criteria) this;
        }

        public Criteria andLiveAddressLessThanOrEqualTo(String value) {
            addCriterion("live_address <=", value, "liveAddress");
            return (Criteria) this;
        }

        public Criteria andLiveAddressLike(String value) {
            addCriterion("live_address like", value, "liveAddress");
            return (Criteria) this;
        }

        public Criteria andLiveAddressNotLike(String value) {
            addCriterion("live_address not like", value, "liveAddress");
            return (Criteria) this;
        }

        public Criteria andLiveAddressIn(List<String> values) {
            addCriterion("live_address in", values, "liveAddress");
            return (Criteria) this;
        }

        public Criteria andLiveAddressNotIn(List<String> values) {
            addCriterion("live_address not in", values, "liveAddress");
            return (Criteria) this;
        }

        public Criteria andLiveAddressBetween(String value1, String value2) {
            addCriterion("live_address between", value1, value2, "liveAddress");
            return (Criteria) this;
        }

        public Criteria andLiveAddressNotBetween(String value1, String value2) {
            addCriterion("live_address not between", value1, value2, "liveAddress");
            return (Criteria) this;
        }

        public Criteria andCardAddressIsNull() {
            addCriterion("card_address is null");
            return (Criteria) this;
        }

        public Criteria andCardAddressIsNotNull() {
            addCriterion("card_address is not null");
            return (Criteria) this;
        }

        public Criteria andCardAddressEqualTo(String value) {
            addCriterion("card_address =", value, "cardAddress");
            return (Criteria) this;
        }

        public Criteria andCardAddressNotEqualTo(String value) {
            addCriterion("card_address <>", value, "cardAddress");
            return (Criteria) this;
        }

        public Criteria andCardAddressGreaterThan(String value) {
            addCriterion("card_address >", value, "cardAddress");
            return (Criteria) this;
        }

        public Criteria andCardAddressGreaterThanOrEqualTo(String value) {
            addCriterion("card_address >=", value, "cardAddress");
            return (Criteria) this;
        }

        public Criteria andCardAddressLessThan(String value) {
            addCriterion("card_address <", value, "cardAddress");
            return (Criteria) this;
        }

        public Criteria andCardAddressLessThanOrEqualTo(String value) {
            addCriterion("card_address <=", value, "cardAddress");
            return (Criteria) this;
        }

        public Criteria andCardAddressLike(String value) {
            addCriterion("card_address like", value, "cardAddress");
            return (Criteria) this;
        }

        public Criteria andCardAddressNotLike(String value) {
            addCriterion("card_address not like", value, "cardAddress");
            return (Criteria) this;
        }

        public Criteria andCardAddressIn(List<String> values) {
            addCriterion("card_address in", values, "cardAddress");
            return (Criteria) this;
        }

        public Criteria andCardAddressNotIn(List<String> values) {
            addCriterion("card_address not in", values, "cardAddress");
            return (Criteria) this;
        }

        public Criteria andCardAddressBetween(String value1, String value2) {
            addCriterion("card_address between", value1, value2, "cardAddress");
            return (Criteria) this;
        }

        public Criteria andCardAddressNotBetween(String value1, String value2) {
            addCriterion("card_address not between", value1, value2, "cardAddress");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptIsNull() {
            addCriterion("compliance_prompt is null");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptIsNotNull() {
            addCriterion("compliance_prompt is not null");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptEqualTo(String value) {
            addCriterion("compliance_prompt =", value, "compliancePrompt");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptNotEqualTo(String value) {
            addCriterion("compliance_prompt <>", value, "compliancePrompt");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptGreaterThan(String value) {
            addCriterion("compliance_prompt >", value, "compliancePrompt");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptGreaterThanOrEqualTo(String value) {
            addCriterion("compliance_prompt >=", value, "compliancePrompt");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptLessThan(String value) {
            addCriterion("compliance_prompt <", value, "compliancePrompt");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptLessThanOrEqualTo(String value) {
            addCriterion("compliance_prompt <=", value, "compliancePrompt");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptLike(String value) {
            addCriterion("compliance_prompt like", value, "compliancePrompt");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptNotLike(String value) {
            addCriterion("compliance_prompt not like", value, "compliancePrompt");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptIn(List<String> values) {
            addCriterion("compliance_prompt in", values, "compliancePrompt");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptNotIn(List<String> values) {
            addCriterion("compliance_prompt not in", values, "compliancePrompt");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptBetween(String value1, String value2) {
            addCriterion("compliance_prompt between", value1, value2, "compliancePrompt");
            return (Criteria) this;
        }

        public Criteria andCompliancePromptNotBetween(String value1, String value2) {
            addCriterion("compliance_prompt not between", value1, value2, "compliancePrompt");
            return (Criteria) this;
        }

        public Criteria andDatasourceIsNull() {
            addCriterion("datasource is null");
            return (Criteria) this;
        }

        public Criteria andDatasourceIsNotNull() {
            addCriterion("datasource is not null");
            return (Criteria) this;
        }

        public Criteria andDatasourceEqualTo(Integer value) {
            addCriterion("datasource =", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceNotEqualTo(Integer value) {
            addCriterion("datasource <>", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceGreaterThan(Integer value) {
            addCriterion("datasource >", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("datasource >=", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceLessThan(Integer value) {
            addCriterion("datasource <", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceLessThanOrEqualTo(Integer value) {
            addCriterion("datasource <=", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceIn(List<Integer> values) {
            addCriterion("datasource in", values, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceNotIn(List<Integer> values) {
            addCriterion("datasource not in", values, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceBetween(Integer value1, Integer value2) {
            addCriterion("datasource between", value1, value2, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceNotBetween(Integer value1, Integer value2) {
            addCriterion("datasource not between", value1, value2, "datasource");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameIsNull() {
            addCriterion("service_entity_name is null");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameIsNotNull() {
            addCriterion("service_entity_name is not null");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameEqualTo(String value) {
            addCriterion("service_entity_name =", value, "serviceEntityName");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameNotEqualTo(String value) {
            addCriterion("service_entity_name <>", value, "serviceEntityName");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameGreaterThan(String value) {
            addCriterion("service_entity_name >", value, "serviceEntityName");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameGreaterThanOrEqualTo(String value) {
            addCriterion("service_entity_name >=", value, "serviceEntityName");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameLessThan(String value) {
            addCriterion("service_entity_name <", value, "serviceEntityName");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameLessThanOrEqualTo(String value) {
            addCriterion("service_entity_name <=", value, "serviceEntityName");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameLike(String value) {
            addCriterion("service_entity_name like", value, "serviceEntityName");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameNotLike(String value) {
            addCriterion("service_entity_name not like", value, "serviceEntityName");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameIn(List<String> values) {
            addCriterion("service_entity_name in", values, "serviceEntityName");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameNotIn(List<String> values) {
            addCriterion("service_entity_name not in", values, "serviceEntityName");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameBetween(String value1, String value2) {
            addCriterion("service_entity_name between", value1, value2, "serviceEntityName");
            return (Criteria) this;
        }

        public Criteria andServiceEntityNameNotBetween(String value1, String value2) {
            addCriterion("service_entity_name not between", value1, value2, "serviceEntityName");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeIsNull() {
            addCriterion("fpqc_code is null");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeIsNotNull() {
            addCriterion("fpqc_code is not null");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeEqualTo(String value) {
            addCriterion("fpqc_code =", value, "fpqcCode");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeNotEqualTo(String value) {
            addCriterion("fpqc_code <>", value, "fpqcCode");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeGreaterThan(String value) {
            addCriterion("fpqc_code >", value, "fpqcCode");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeGreaterThanOrEqualTo(String value) {
            addCriterion("fpqc_code >=", value, "fpqcCode");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeLessThan(String value) {
            addCriterion("fpqc_code <", value, "fpqcCode");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeLessThanOrEqualTo(String value) {
            addCriterion("fpqc_code <=", value, "fpqcCode");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeLike(String value) {
            addCriterion("fpqc_code like", value, "fpqcCode");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeNotLike(String value) {
            addCriterion("fpqc_code not like", value, "fpqcCode");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeIn(List<String> values) {
            addCriterion("fpqc_code in", values, "fpqcCode");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeNotIn(List<String> values) {
            addCriterion("fpqc_code not in", values, "fpqcCode");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeBetween(String value1, String value2) {
            addCriterion("fpqc_code between", value1, value2, "fpqcCode");
            return (Criteria) this;
        }

        public Criteria andFpqcCodeNotBetween(String value1, String value2) {
            addCriterion("fpqc_code not between", value1, value2, "fpqcCode");
            return (Criteria) this;
        }

        public Criteria andServiceConfirmTimeIsNull() {
            addCriterion("service_confirm_time is null");
            return (Criteria) this;
        }

        public Criteria andServiceConfirmTimeIsNotNull() {
            addCriterion("service_confirm_time is not null");
            return (Criteria) this;
        }

        public Criteria andServiceConfirmTimeEqualTo(Date value) {
            addCriterion("service_confirm_time =", value, "serviceConfirmTime");
            return (Criteria) this;
        }

        public Criteria andServiceConfirmTimeNotEqualTo(Date value) {
            addCriterion("service_confirm_time <>", value, "serviceConfirmTime");
            return (Criteria) this;
        }

        public Criteria andServiceConfirmTimeGreaterThan(Date value) {
            addCriterion("service_confirm_time >", value, "serviceConfirmTime");
            return (Criteria) this;
        }

        public Criteria andServiceConfirmTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("service_confirm_time >=", value, "serviceConfirmTime");
            return (Criteria) this;
        }

        public Criteria andServiceConfirmTimeLessThan(Date value) {
            addCriterion("service_confirm_time <", value, "serviceConfirmTime");
            return (Criteria) this;
        }

        public Criteria andServiceConfirmTimeLessThanOrEqualTo(Date value) {
            addCriterion("service_confirm_time <=", value, "serviceConfirmTime");
            return (Criteria) this;
        }

        public Criteria andServiceConfirmTimeIn(List<Date> values) {
            addCriterion("service_confirm_time in", values, "serviceConfirmTime");
            return (Criteria) this;
        }

        public Criteria andServiceConfirmTimeNotIn(List<Date> values) {
            addCriterion("service_confirm_time not in", values, "serviceConfirmTime");
            return (Criteria) this;
        }

        public Criteria andServiceConfirmTimeBetween(Date value1, Date value2) {
            addCriterion("service_confirm_time between", value1, value2, "serviceConfirmTime");
            return (Criteria) this;
        }

        public Criteria andServiceConfirmTimeNotBetween(Date value1, Date value2) {
            addCriterion("service_confirm_time not between", value1, value2, "serviceConfirmTime");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateIsNull() {
            addCriterion("risk_tolerance_date is null");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateIsNotNull() {
            addCriterion("risk_tolerance_date is not null");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateEqualTo(String value) {
            addCriterion("risk_tolerance_date =", value, "riskToleranceDate");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateNotEqualTo(String value) {
            addCriterion("risk_tolerance_date <>", value, "riskToleranceDate");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateGreaterThan(String value) {
            addCriterion("risk_tolerance_date >", value, "riskToleranceDate");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateGreaterThanOrEqualTo(String value) {
            addCriterion("risk_tolerance_date >=", value, "riskToleranceDate");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateLessThan(String value) {
            addCriterion("risk_tolerance_date <", value, "riskToleranceDate");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateLessThanOrEqualTo(String value) {
            addCriterion("risk_tolerance_date <=", value, "riskToleranceDate");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateLike(String value) {
            addCriterion("risk_tolerance_date like", value, "riskToleranceDate");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateNotLike(String value) {
            addCriterion("risk_tolerance_date not like", value, "riskToleranceDate");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateIn(List<String> values) {
            addCriterion("risk_tolerance_date in", values, "riskToleranceDate");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateNotIn(List<String> values) {
            addCriterion("risk_tolerance_date not in", values, "riskToleranceDate");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateBetween(String value1, String value2) {
            addCriterion("risk_tolerance_date between", value1, value2, "riskToleranceDate");
            return (Criteria) this;
        }

        public Criteria andRiskToleranceDateNotBetween(String value1, String value2) {
            addCriterion("risk_tolerance_date not between", value1, value2, "riskToleranceDate");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateIsNull() {
            addCriterion("investor_qualified_date is null");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateIsNotNull() {
            addCriterion("investor_qualified_date is not null");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateEqualTo(String value) {
            addCriterion("investor_qualified_date =", value, "investorQualifiedDate");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateNotEqualTo(String value) {
            addCriterion("investor_qualified_date <>", value, "investorQualifiedDate");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateGreaterThan(String value) {
            addCriterion("investor_qualified_date >", value, "investorQualifiedDate");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateGreaterThanOrEqualTo(String value) {
            addCriterion("investor_qualified_date >=", value, "investorQualifiedDate");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateLessThan(String value) {
            addCriterion("investor_qualified_date <", value, "investorQualifiedDate");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateLessThanOrEqualTo(String value) {
            addCriterion("investor_qualified_date <=", value, "investorQualifiedDate");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateLike(String value) {
            addCriterion("investor_qualified_date like", value, "investorQualifiedDate");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateNotLike(String value) {
            addCriterion("investor_qualified_date not like", value, "investorQualifiedDate");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateIn(List<String> values) {
            addCriterion("investor_qualified_date in", values, "investorQualifiedDate");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateNotIn(List<String> values) {
            addCriterion("investor_qualified_date not in", values, "investorQualifiedDate");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateBetween(String value1, String value2) {
            addCriterion("investor_qualified_date between", value1, value2, "investorQualifiedDate");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedDateNotBetween(String value1, String value2) {
            addCriterion("investor_qualified_date not between", value1, value2, "investorQualifiedDate");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmIsNull() {
            addCriterion("risk_hint_confirm_dtm is null");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmIsNotNull() {
            addCriterion("risk_hint_confirm_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmEqualTo(String value) {
            addCriterion("risk_hint_confirm_dtm =", value, "riskHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmNotEqualTo(String value) {
            addCriterion("risk_hint_confirm_dtm <>", value, "riskHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmGreaterThan(String value) {
            addCriterion("risk_hint_confirm_dtm >", value, "riskHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmGreaterThanOrEqualTo(String value) {
            addCriterion("risk_hint_confirm_dtm >=", value, "riskHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmLessThan(String value) {
            addCriterion("risk_hint_confirm_dtm <", value, "riskHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmLessThanOrEqualTo(String value) {
            addCriterion("risk_hint_confirm_dtm <=", value, "riskHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmLike(String value) {
            addCriterion("risk_hint_confirm_dtm like", value, "riskHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmNotLike(String value) {
            addCriterion("risk_hint_confirm_dtm not like", value, "riskHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmIn(List<String> values) {
            addCriterion("risk_hint_confirm_dtm in", values, "riskHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmNotIn(List<String> values) {
            addCriterion("risk_hint_confirm_dtm not in", values, "riskHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmBetween(String value1, String value2) {
            addCriterion("risk_hint_confirm_dtm between", value1, value2, "riskHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andRiskHintConfirmDtmNotBetween(String value1, String value2) {
            addCriterion("risk_hint_confirm_dtm not between", value1, value2, "riskHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmIsNull() {
            addCriterion("investor_qualified_hint_confirm_dtm is null");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmIsNotNull() {
            addCriterion("investor_qualified_hint_confirm_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmEqualTo(String value) {
            addCriterion("investor_qualified_hint_confirm_dtm =", value, "investorQualifiedHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmNotEqualTo(String value) {
            addCriterion("investor_qualified_hint_confirm_dtm <>", value, "investorQualifiedHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmGreaterThan(String value) {
            addCriterion("investor_qualified_hint_confirm_dtm >", value, "investorQualifiedHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmGreaterThanOrEqualTo(String value) {
            addCriterion("investor_qualified_hint_confirm_dtm >=", value, "investorQualifiedHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmLessThan(String value) {
            addCriterion("investor_qualified_hint_confirm_dtm <", value, "investorQualifiedHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmLessThanOrEqualTo(String value) {
            addCriterion("investor_qualified_hint_confirm_dtm <=", value, "investorQualifiedHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmLike(String value) {
            addCriterion("investor_qualified_hint_confirm_dtm like", value, "investorQualifiedHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmNotLike(String value) {
            addCriterion("investor_qualified_hint_confirm_dtm not like", value, "investorQualifiedHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmIn(List<String> values) {
            addCriterion("investor_qualified_hint_confirm_dtm in", values, "investorQualifiedHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmNotIn(List<String> values) {
            addCriterion("investor_qualified_hint_confirm_dtm not in", values, "investorQualifiedHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmBetween(String value1, String value2) {
            addCriterion("investor_qualified_hint_confirm_dtm between", value1, value2, "investorQualifiedHintConfirmDtm");
            return (Criteria) this;
        }

        public Criteria andInvestorQualifiedHintConfirmDtmNotBetween(String value1, String value2) {
            addCriterion("investor_qualified_hint_confirm_dtm not between", value1, value2, "investorQualifiedHintConfirmDtm");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}