package com.howbuy.tms.high.batch.service.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.howbuy.tms.common.client.BaseRequest;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.exception.DataBaseException;
import com.howbuy.tms.common.exception.SystemException;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.LogContextHelper;
import com.howbuy.tms.common.utils.ReflectUtil;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.trace.RequestChainTrace;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.ThreadContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @className BusinessAspect
 * @description 交易处理切面
 * @date 2015-3-23 下午5:24:31
 */
@Service("businessAspect")
class BusinessAspect {

    private static Logger logger = LogManager.getLogger(BusinessAspect.class);
    private static Logger mainLogger = LogManager.getLogger("mainlog");
    private static Logger txLogger = LogManager.getLogger("txServiceLog");

    private static final SerializerFeature[] serializer = {SerializerFeature.WriteClassName, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty};

    public Object doAround(ProceedingJoinPoint pjp) {
        long start = System.currentTimeMillis();
        // 获取方法参数
        Object[] args = pjp.getArgs();
        BaseRequest request = null;

        Signature signature = pjp.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();

        // traceId
        String reqId = RequestChainTrace.getReqId();
        String remoteHost = RequestChainTrace.getRemoteHost();
        RequestChainTrace.buildAndSet(reqId, remoteHost);
        if (StringUtils.isEmpty(reqId)) {
            reqId = String.valueOf(UUID.randomUUID());
        }

        // RequestChainTrace.buildAndSet已经自动设置了SLF4J MDC
        // 我们只需要设置Log4j2 ThreadContext来支持${ctx:uuid}语法
        String currentRanNo = RequestChainTrace.getRanNo();
        ThreadContext.put("uuid", reqId);
        ThreadContext.put("ranNo", currentRanNo);
        setLogLevel(pjp);

        // 记录 交易码.log 的日志(request)

        if (args != null && args.length > 0) {
            request = (BaseRequest) args[0];
            if (request != null) {
                if (txLogger.isInfoEnabled()) {
                    txLogger.info(JSON.toJSONString(request, serializer));
                }
            }
        }

        BaseResponse result = null;
        try {
            result = (BaseResponse) pjp.proceed();
        } catch (ValidateException e) {
            logger.error("ValidateException:", e);
            result = createResponse(method);
            result.setReturnCode(e.getErrorCode());
            result.setDescription(e.getErrorDesc());
        } catch (BusinessException e) {
            logger.error("BusinessException:", e);
            result = createResponse(method);
            result.setReturnCode(e.getErrorCode());
            result.setDescription(e.getErrorDesc());
        } catch (DataBaseException e) {
            logger.error("DataBaseException:", e);
            result = createResponse(method);
            result.setReturnCode(ExceptionCodes.BATCH_CENTER_DB_ERROR);
            result.setDescription(MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_DB_ERROR));
        } catch (SystemException e) {
            logger.error("SystemException:", e);
            result = createResponse(method);
            result.setReturnCode(e.getErrorCode());
            result.setDescription(e.getErrorDesc());
        } catch (Throwable e) {
            logger.error("Throwable:", e);
            result = createResponse(method);
            result.setReturnCode(ExceptionCodes.BATCH_CENTER_SYSTEM_ERROR);
            result.setDescription(MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_SYSTEM_ERROR));
        }
        result.setTxId(reqId);
        // 如果有异常码，但是没有异常描述，设置以上描述
        if (StringUtils.isNotEmpty(result.getReturnCode()) && StringUtils.isEmpty(result.getDescription())) {
            result.setDescription(MessageSource.getMessageByCode(result.getReturnCode()));
        }

        // 记录main.log
        long time = System.currentTimeMillis() - start;
        if (mainLogger.isInfoEnabled()) {
            String returnCode = result.getReturnCode();
            if (request != null) {
                mainLogger.info(convertMainLog(reqId, getTimeStr(), request.getTxCode(), returnCode, time, remoteHost));
            }
        }

        // 记录 交易码.log 的日志(response)
        if (txLogger.isInfoEnabled()) {
            txLogger.info(JSON.toJSONString(result, serializer));
        }

        // 清理ThreadContext，MDC由RequestChainTrace.remove()自动清理
        try {
            ThreadContext.remove("uuid");
            ThreadContext.remove("ranNo");
        } catch (Exception e) {
            // 忽略清理异常
        }

        return result;
    }

    private BaseResponse createResponse(Method method) {
        BaseResponse response = null;
        try {
            Type type = method.getGenericReturnType();
            Class<?> clazz = Class.forName(getClassName(type));
            response = (BaseResponse) clazz.newInstance();
        } catch (Exception e) {
            response = new BaseResponse();
        }

        return response;
    }

    private String convertMainLog(String traceId, String dateStr, String txCode, String returnCode, long costTime, String remoteHost) {
        JSONObject json = new JSONObject();
        json.put("time", dateStr);
        json.put("operation", "00");
        json.put("tid", traceId);
        json.put("tx_code", txCode);
        json.put("return_code", returnCode);
        json.put("costs", costTime);
        json.put("remoteHost", remoteHost);
        return json.toJSONString();
    }

    private String getTimeStr() {
        Date now = new Date();
        return DateUtils.formatToString(now, "HH:mm:ss");
    }

    private static final String NAME_PREFIX = "class ";

    private String getClassName(Type type) {
        String fullName = type.toString();
        if (fullName.startsWith(NAME_PREFIX)) {
            return fullName.substring(NAME_PREFIX.length());
        }
        return fullName;
    }

    // 请求打印debug日志，辅助查询
    private void setLogLevel(ProceedingJoinPoint joinPoint) {
        try {
            for (Object obj : joinPoint.getArgs()) {
                if (obj == null) {
                    continue;
                }
                Object logLevel = ReflectUtil.getValue(obj, "logLevel");
                if (!org.springframework.util.StringUtils.isEmpty(logLevel)) {
                    logger.info("setLogLevel:{}", logLevel);
                    RequestChainTrace.setTraceContext(LogContextHelper.LOG_LEVEL_KEY, logLevel);
                }
            }
        } catch (Exception e) {
            logger.error("setLogLevel error:{}", e.getMessage());
        }
    }

}