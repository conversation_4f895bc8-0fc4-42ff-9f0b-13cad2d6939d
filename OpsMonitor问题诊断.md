# OpsMonitor.warn() 日志不打印问题诊断

## 问题描述
`OpsMonitor.warn(msg, OpsMonitor.ERROR)` 调用时，虽然能获取到 uuid，但日志没有打印到 warn.log 文件中。

## 问题诊断方案

既然您确认日志配置是正确的（log4j2.xml 在容器内正确位置），我添加了详细的调试信息来定位具体问题。

### 修改内容

#### 1. 增强 OpsMonitor 初始化日志
```java
static{
    try {
        warnLogger = LoggerFactory.getLogger("monitorjson");
        logger.info("OpsMonitor warnLogger initialized: {}, class: {}", warnLogger != null, warnLogger != null ? warnLogger.getClass().getName() : "null");
        if (warnLogger != null) {
            logger.info("OpsMonitor warnLogger isWarnEnabled: {}", warnLogger.isWarnEnabled());
        }
    } catch (Exception e) {
        logger.error("OpsMonitor warnLogger initialization failed", e);
    }
}
```

#### 2. 增强 warn() 方法调试信息
```java
public static void warn(String content, String level) {
    try {
        logger.info("OpsMonitor.warn() called with content: {}, level: {}", content, level);
        
        if (warnLogger == null) {
            logger.error("OpsMonitor.warn() called but warnLogger is null!");
            return;
        }
        
        logger.info("OpsMonitor warnLogger isWarnEnabled: {}", warnLogger.isWarnEnabled());
        
        // ... 构建消息 ...
        
        String jsonMsg = JSON.toJSONString(warnMsg);
        logger.info("OpsMonitor about to call warnLogger.warn() with: {}", jsonMsg);
        
        warnLogger.warn(jsonMsg);
        
        logger.info("OpsMonitor warnLogger.warn() called successfully");
    } catch (Exception e) {
        logger.error("OpsMonitor Error:", e);
    }
}
```

### 诊断步骤

1. **重新启动应用**，观察启动日志中的以下信息：
   - `OpsMonitor warnLogger initialized: true, class: xxx`
   - `OpsMonitor warnLogger isWarnEnabled: true`

2. **触发 OpsMonitor.warn() 调用**，观察日志中的调试信息：
   - `OpsMonitor.warn() called with content: xxx, level: xxx`
   - `OpsMonitor warnLogger isWarnEnabled: true`
   - `OpsMonitor about to call warnLogger.warn() with: xxx`
   - `OpsMonitor warnLogger.warn() called successfully`

3. **检查可能的问题**：
   - 如果 `warnLogger` 为 null，说明初始化失败
   - 如果 `isWarnEnabled()` 返回 false，说明日志级别配置问题
   - 如果调用成功但文件中没有内容，说明是文件写入问题

### 可能的问题和解决方案

#### 问题1：warnLogger 为 null
**原因**：LoggerFactory.getLogger("monitorjson") 失败
**解决**：检查日志配置中 monitorjson logger 的定义

#### 问题2：isWarnEnabled() 返回 false
**原因**：monitorjson logger 的级别设置过高
**解决**：检查 log4j2.xml 中 monitorjson logger 的 level 设置

#### 问题3：调用成功但文件无内容
**原因**：文件权限或路径问题
**解决**：检查 warn.log 文件的写权限和路径

### 测试方法

我创建了一个简单的测试类 `OpsMonitorTest.java`，可以用来验证：

```java
@Test
public void testBasicWarn() {
    ThreadContext.put("uuid", "test-uuid-12345");
    OpsMonitor.warn("这是一个测试告警消息", OpsMonitor.ERROR);
    // 检查日志输出和 warn.log 文件
}
```

### 下一步

请运行修改后的代码，观察日志输出，然后告诉我具体看到了什么信息，这样我们就能准确定位问题所在。
