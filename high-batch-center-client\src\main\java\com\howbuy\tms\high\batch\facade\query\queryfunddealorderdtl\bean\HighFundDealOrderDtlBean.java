/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.bean;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:(私募交易订单明细)
 * @reason:
 * <AUTHOR>
 * @date 2016年10月11日 下午5:02:12
 * @since JDK 1.6
 */
public class HighFundDealOrderDtlBean implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
	private static final long serialVersionUID = 8468438634643894469L;
	
	/**
     * 订单号
     */
    private String dealNo;
    /**
     * 订单明细号
     */
    private String dealDtlNo;
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 证件号码
     */
    private String idNo;

    /**
     * 中台交易代码
     */
    private String txCode;
    /**
     * 中台业务代码
     */
    private String mBusiCode;
    /**
     * 产品代码
     */
    private String productCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 基金代码
     */
    private String fundCode;
    /***
     * 产品名称
     */
    private String fundName;
    /**
     * 分销机构
     */
    private String disCode;
    /**
     * 交易渠道
     */
    private String txChannel;
    /**
     * 申请金额
     */
    private BigDecimal appAmt;
    /**
     * 申请份额
     */
    private BigDecimal appVol;
    /**
     * 确认金额
     */
    private BigDecimal ackAmt;
    /**
     * 确认份额
     */
    private BigDecimal ackVol;
    /**
     * 基金净值
     */
    private BigDecimal nav;
    /**
     * 申请日期
     */
    private String appDate;
    /**
     * 申请时间
     */
    private String appTime;
    /**
     * TA交易日期
     */
    private String taTradeDt;
    /**
     * 折扣率
     */
    private BigDecimal discountRate;
    /**
     * 付款状态
     */
    private String payStatus;
    /**
     * 支付方式
     */
    private String paymentType;
    /**
     * 支付对账状态
     */
    private String pmtCompFlag;
    /**
     * 支付订单号
     */
    private String pmtDealNo;

    /**
     * 后台支付订单号
     */
    private String tPmtDealNo;
    /**
     * 支付返回信息
     */
    private String retDesc;
    /**
     * 目标基金代码
     */
    private String tFundCode;
    /**
     * 基金分红方式
     */
    private String fundDivMode;
    /**
     * 确认日期
     */
    private String ackDt;
    /**
     * 明细订单状态
     */
    private String orderStatus;
    /**
     * 订单明细申请标记
     */
    private String txAppFlag;
    /**
     * 订单明细确认标记
     */
    private String txAckFlag;

    /**
     * 订单明细通知标记
     */
    private String notifySubmitFlag;
    /**
     * 返回信息-(交易失败原因)
     */
    private String memo;

    /**
     * 赎回去向0-银行卡1-储蓄罐
     */
    private String redeemDirection;

    /**
     * 回款信息
     */
    private String refundInfo;

    /**
     * 渠道代码，101-自助渠道；102-预约渠道
     */
    private String channelCode;

    /**
     * 顺延标志：1-不顺延；2-顺延
     */
    private String advanceFlag;

    /**
     * 预约订单号
     */
    private String appointmentDealNo;

    /**
     * 0-有效；1-无效
     */
    private String recStat;

    /**
     * 创建日期
     */
    private Date appDtm;

    /**
     * 订单类型：1-公募；2-高端；3-定期
     */
    private String dealType;

    /**
     * 资金匹配日期（支付完成日期）
     */
    private Date pmtCompleteDtm;
    /**
     * 产品类别
     */
    private String productClass;

    /***
     * 限额类型
     */
    private String limitType;
    /**
     * 利息
     */
    private BigDecimal interest;
    /***
     * 业绩计提
     */
    private BigDecimal performanceFees;
    /***
     * 业绩补偿
     */
    private BigDecimal performanceRecoup;
    /***
     * 利息折份额
     */
    private BigDecimal volByInterest;

    /***
     * 冷静期（小时）
     */
    private String calmTime;
    /***
     * 冷静期（日期）
     */
    private String calmDtm;
    /***
     * 预约折扣
     */
    private BigDecimal appointmentDiscount;
    /***
     * 基金二级分类
     */
    private String fundSubType;
    /***
     * 基金类型
     */
    private String fundType;
    /***
     * 首次购买标识
     */
    private String firstBuyFlag;
    /***
     * 电子签名标识
     */
    private String esignatureFlag;
    /***
     * 电子合同标识
     */
    private String econtractFlag;
    /***
     * 支行名称
     */
    private String subBankName;
    /**
     * 可赎回日期
     */
    private String allowDt;

    /**
     * 中台详细业务码
     */
    private String zBusiCode;

    /**
     * 风险二次确认标识
     */
    private String riskFlag;

    /**
     * 风险二次确认时间
     */
    private String riskAckDtm;

    /**
     * 高风险提示时间
     */
    private String highRiskTipDtm;

    /**
     * 普通投资者风险提示时间
     */
    private String normalCustTipDtm;

    /**
     * 客户风险等级
     */
    private String custRiskLevel;
    /**
     * 成单方式 1-纸质成单 2-电子成单 3-无纸化 4-异常流程
     */
    private String orderFormType;
    /**
     * 更新时间
     */
    private Date updateDtm;

    /**
     * 产品交易通道
     */
    private String productChannel;
    /**
     * 对账日期
     */
    private String pmtCheckDt;
    /**
     * 上报后台日期
     */
    private String tradeDt;
    /**
     * 上报ta日期
     */
    private String submitTaDt;
    /**
     * 预约类型0-系统生成 1-投顾生成
     */
    private String appointmentDealNoType;
    
    /**
     * 资金账号(主订单)
     */
    private String cpAcctNo;
    /**
     * 银行卡号(主订单)
     */
    private String bankAcct;
    /**
     * 银行编码(主订单)
     */
    private String bankCode;
    /**
     * 协议类型(主订单)
     */
    private String protocolType;
    /**
     * 协议号(主订单)
     */
    private String protocolNo;
    
    /**
     * 资金帐号(明细订单)
     */
    private String dtlCpAcctNo;
    /**
     * 银行卡号(明细订单)
     */
    private String dtlBankAcct;
    /**
     * 银行编码(明细订单)
     */
    private String dtlBankCode;
    /**
     * 协议类型(明细订单)
     */
    private String dtlProtocolType;
    /**
     * 协议号(明细订单)
     */
    private String dtlProtocolNo;
    
    /**
     * 份额合并新增字段
     */
    /**
     * 目标协议号
     */
    private String targetProtocolNo;
    
    /**
     * 目标银行卡
     */
    private String targetBankAcct;
    /**
     * 资金账号
     */
    private String targetCpAcctNo;
    
    /**
     * 双录状态: 0-不需要双录, 1-未双录, 2-已双录
     */
    private String dualentryStatus;
    /**
     * 双录完成时间
     */
    private Date dualentryFinishDtm;
    /**
     * 双录干预标识: 0-不需干预, 1-未干预, 2-已干预
     */
    private String dualentryInterposeFlag;
    /**
     * 回访状态: 0-不需回访, 1-未回访, 2-已回访
     */
    private String callbackStatus;
    /**
     * 回访完成时间
     */
    private Date callbackFinishDtm;
    /**
     * 回访干预标识: 0-不需干预, 1-未干预, 2-已干预
     */
    private String callbackInterposeFlag;
    /**
     * 冷静期干预标识: 0-不需干预, 1-未干预, 2-已干预
     */
    private String calmdtmInterposeFlag;
    /**
     * 资产证明状态: 0-无效, 1-有效
     */
    private String assetcertificateStatus;
    /**
     * 资产证明干预标识: 0-不需干预, 1-未干预, 2-已干预
     */
    private String assetInterposeFlag;

    /**
     * 上报后台返回码
     */
    private String submitRetCode;
    /**
     * 上报后台返回描述
     */
    private String submitRetDesc;
    
    private String busiCode;

    /**
     * 基金风险等级
     */
    private String fundRiskLevel;

    /**
     * 投资者类型  0-普通; 1-专业
     */
    private String qualificationType;

    /**
     * 净申请金额
     */
    private String netAppAmt;

    /**
     * 手续费
     */
    private BigDecimal fee;

    /**
     * 是否私募定投 0-不是; 1-是
     */
    private String highFundInvPlanFlag;
    /**
     * 合格投资者再次确认时间
     */
    private String investAckDtm;

    /**
     * 是否巨额赎回顺延
     */
    private String continuanceFlag;

    /**
     * 是否淡水区拆单
     */
    private String stageFlag;
    /**
     * 服务主体名
     */
    private String serviceEntityName;
    /**
     * 基金从业者编码
     */
    private String fpqcCode;
    /**
     * 服务主体确认时间
     */
    private Date serviceConfirmTime;

    /**
     * 风险评测日期 yyyyMMdd
     */
    private String riskToleranceDate;
    /**
     * 投资者类型认证日期 YYYYMMDD
     */
    private String investorQualifiedDate;
    /**
     * 风测提醒确定时间 yyyyMMddHHmmss
     */
    private String riskHintConfirmDtm;
    /**
     * 投资者类型认证提醒确认时间 yyyyMMddHHmmss
     */
    private String investorQualifiedHintConfirmDtm;

    public String getRiskToleranceDate() {
        return riskToleranceDate;
    }

    public void setRiskToleranceDate(String riskToleranceDate) {
        this.riskToleranceDate = riskToleranceDate;
    }

    public String getInvestorQualifiedDate() {
        return investorQualifiedDate;
    }

    public void setInvestorQualifiedDate(String investorQualifiedDate) {
        this.investorQualifiedDate = investorQualifiedDate;
    }

    public String getRiskHintConfirmDtm() {
        return riskHintConfirmDtm;
    }

    public void setRiskHintConfirmDtm(String riskHintConfirmDtm) {
        this.riskHintConfirmDtm = riskHintConfirmDtm;
    }

    public String getInvestorQualifiedHintConfirmDtm() {
        return investorQualifiedHintConfirmDtm;
    }

    public void setInvestorQualifiedHintConfirmDtm(String investorQualifiedHintConfirmDtm) {
        this.investorQualifiedHintConfirmDtm = investorQualifiedHintConfirmDtm;
    }

    public String getServiceEntityName() {
        return serviceEntityName;
    }

    public void setServiceEntityName(String serviceEntityName) {
        this.serviceEntityName = serviceEntityName;
    }

    public String getFpqcCode() {
        return fpqcCode;
    }

    public void setFpqcCode(String fpqcCode) {
        this.fpqcCode = fpqcCode;
    }

    public Date getServiceConfirmTime() {
        return serviceConfirmTime;
    }

    public void setServiceConfirmTime(Date serviceConfirmTime) {
        this.serviceConfirmTime = serviceConfirmTime;
    }

    public String getContinuanceFlag() {
        return continuanceFlag;
    }

    public void setContinuanceFlag(String continuanceFlag) {
        this.continuanceFlag = continuanceFlag;
    }

    public String getStageFlag() {
        return stageFlag;
    }

    public void setStageFlag(String stageFlag) {
        this.stageFlag = stageFlag;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getDtlCpAcctNo() {
        return dtlCpAcctNo;
    }

    public void setDtlCpAcctNo(String dtlCpAcctNo) {
        this.dtlCpAcctNo = dtlCpAcctNo;
    }

    public String getDtlBankAcct() {
        return dtlBankAcct;
    }

    public void setDtlBankAcct(String dtlBankAcct) {
        this.dtlBankAcct = dtlBankAcct;
    }

    public String getDtlBankCode() {
        return dtlBankCode;
    }

    public void setDtlBankCode(String dtlBankCode) {
        this.dtlBankCode = dtlBankCode;
    }

    public String getDtlProtocolType() {
        return dtlProtocolType;
    }

    public void setDtlProtocolType(String dtlProtocolType) {
        this.dtlProtocolType = dtlProtocolType;
    }

    public String getDtlProtocolNo() {
        return dtlProtocolNo;
    }

    public void setDtlProtocolNo(String dtlProtocolNo) {
        this.dtlProtocolNo = dtlProtocolNo;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getAllowDt() {
        return allowDt;
    }

    public void setAllowDt(String allowDt) {
        this.allowDt = allowDt;
    }

    public String getFundSubType() {
        return fundSubType;
    }

    public void setFundSubType(String fundSubType) {
        this.fundSubType = fundSubType;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getFirstBuyFlag() {
        return firstBuyFlag;
    }

    public void setFirstBuyFlag(String firstBuyFlag) {
        this.firstBuyFlag = firstBuyFlag;
    }

    public String getEsignatureFlag() {
        return esignatureFlag;
    }

    public void setEsignatureFlag(String esignatureFlag) {
        this.esignatureFlag = esignatureFlag;
    }

    public String getEcontractFlag() {
        return econtractFlag;
    }

    public void setEcontractFlag(String econtractFlag) {
        this.econtractFlag = econtractFlag;
    }

    public String getSubBankName() {
        return subBankName;
    }

    public void setSubBankName(String subBankName) {
        this.subBankName = subBankName;
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getDealDtlNo() {
        return dealDtlNo;
    }

    public void setDealDtlNo(String dealDtlNo) {
        this.dealDtlNo = dealDtlNo;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getTxCode() {
        return txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode;
    }

    public String getmBusiCode() {
        return mBusiCode;
    }

    public void setmBusiCode(String mBusiCode) {
        this.mBusiCode = mBusiCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getTxChannel() {
        return txChannel;
    }

    public void setTxChannel(String txChannel) {
        this.txChannel = txChannel;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public BigDecimal getAckAmt() {
        return ackAmt;
    }

    public void setAckAmt(BigDecimal ackAmt) {
        this.ackAmt = ackAmt;
    }

    public BigDecimal getAckVol() {
        return ackVol;
    }

    public void setAckVol(BigDecimal ackVol) {
        this.ackVol = ackVol;
    }

    public BigDecimal getNav() {
        return nav;
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public String getAppDate() {
        return appDate;
    }

    public void setAppDate(String appDate) {
        this.appDate = appDate;
    }

    public String getAppTime() {
        return appTime;
    }

    public void setAppTime(String appTime) {
        this.appTime = appTime;
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getPmtCompFlag() {
        return pmtCompFlag;
    }

    public void setPmtCompFlag(String pmtCompFlag) {
        this.pmtCompFlag = pmtCompFlag;
    }

    public String getPmtDealNo() {
        return pmtDealNo;
    }

    public void setPmtDealNo(String pmtDealNo) {
        this.pmtDealNo = pmtDealNo;
    }

    public String gettPmtDealNo() {
        return tPmtDealNo;
    }

    public void settPmtDealNo(String tPmtDealNo) {
        this.tPmtDealNo = tPmtDealNo;
    }

    public String getRetDesc() {
        return retDesc;
    }

    public void setRetDesc(String retDesc) {
        this.retDesc = retDesc;
    }

    public String gettFundCode() {
        return tFundCode;
    }

    public void settFundCode(String tFundCode) {
        this.tFundCode = tFundCode;
    }

    public String getFundDivMode() {
        return fundDivMode;
    }

    public void setFundDivMode(String fundDivMode) {
        this.fundDivMode = fundDivMode;
    }

    public String getAckDt() {
        return ackDt;
    }

    public void setAckDt(String ackDt) {
        this.ackDt = ackDt;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getTxAppFlag() {
        return txAppFlag;
    }

    public void setTxAppFlag(String txAppFlag) {
        this.txAppFlag = txAppFlag;
    }

    public String getTxAckFlag() {
        return txAckFlag;
    }

    public void setTxAckFlag(String txAckFlag) {
        this.txAckFlag = txAckFlag;
    }

    public String getNotifySubmitFlag() {
        return notifySubmitFlag;
    }

    public void setNotifySubmitFlag(String notifySubmitFlag) {
        this.notifySubmitFlag = notifySubmitFlag;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getRedeemDirection() {
        return redeemDirection;
    }

    public void setRedeemDirection(String redeemDirection) {
        this.redeemDirection = redeemDirection;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getAdvanceFlag() {
        return advanceFlag;
    }

    public void setAdvanceFlag(String advanceFlag) {
        this.advanceFlag = advanceFlag;
    }

    public String getAppointmentDealNo() {
        return appointmentDealNo;
    }

    public void setAppointmentDealNo(String appointmentDealNo) {
        this.appointmentDealNo = appointmentDealNo;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }

    public Date getAppDtm() {
        return appDtm;
    }

    public void setAppDtm(Date appDtm) {
        this.appDtm = appDtm;
    }

    public String getDealType() {
        return dealType;
    }

    public void setDealType(String dealType) {
        this.dealType = dealType;
    }

    public Date getPmtCompleteDtm() {
        return pmtCompleteDtm;
    }

    public void setPmtCompleteDtm(Date pmtCompleteDtm) {
        this.pmtCompleteDtm = pmtCompleteDtm;
    }

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getLimitType() {
        return limitType;
    }

    public void setLimitType(String limitType) {
        this.limitType = limitType;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getPerformanceFees() {
        return performanceFees;
    }

    public void setPerformanceFees(BigDecimal performanceFees) {
        this.performanceFees = performanceFees;
    }

    public BigDecimal getPerformanceRecoup() {
        return performanceRecoup;
    }

    public void setPerformanceRecoup(BigDecimal performanceRecoup) {
        this.performanceRecoup = performanceRecoup;
    }

    public BigDecimal getVolByInterest() {
        return volByInterest;
    }

    public void setVolByInterest(BigDecimal volByInterest) {
        this.volByInterest = volByInterest;
    }

    public String getCalmTime() {
        return calmTime;
    }

    public void setCalmTime(String calmTime) {
        this.calmTime = calmTime;
    }

    public String getCalmDtm() {
        return calmDtm;
    }

    public void setCalmDtm(String calmDtm) {
        this.calmDtm = calmDtm;
    }

    public BigDecimal getAppointmentDiscount() {
        return appointmentDiscount;
    }

    public void setAppointmentDiscount(BigDecimal appointmentDiscount) {
        this.appointmentDiscount = appointmentDiscount;
    }

    public String getzBusiCode() {
        return zBusiCode;
    }

    public void setzBusiCode(String zBusiCode) {
        this.zBusiCode = zBusiCode;
    }

    public String getRiskFlag() {
        return riskFlag;
    }

    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag;
    }

    public String getRiskAckDtm() {
        return riskAckDtm;
    }

    public void setRiskAckDtm(String riskAckDtm) {
        this.riskAckDtm = riskAckDtm;
    }

    public String getHighRiskTipDtm() {
        return highRiskTipDtm;
    }

    public void setHighRiskTipDtm(String highRiskTipDtm) {
        this.highRiskTipDtm = highRiskTipDtm;
    }

    public String getNormalCustTipDtm() {
        return normalCustTipDtm;
    }

    public void setNormalCustTipDtm(String normalCustTipDtm) {
        this.normalCustTipDtm = normalCustTipDtm;
    }

    public String getCustRiskLevel() {
        return custRiskLevel;
    }

    public void setCustRiskLevel(String custRiskLevel) {
        this.custRiskLevel = custRiskLevel;
    }

    public String getOrderFormType() {
        return orderFormType;
    }

    public void setOrderFormType(String orderFormType) {
        this.orderFormType = orderFormType;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public String getPmtCheckDt() {
        return pmtCheckDt;
    }

    public void setPmtCheckDt(String pmtCheckDt) {
        this.pmtCheckDt = pmtCheckDt;
    }

    public String getTradeDt() {
        return tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public String getSubmitTaDt() {
        return submitTaDt;
    }

    public void setSubmitTaDt(String submitTaDt) {
        this.submitTaDt = submitTaDt;
    }

    public String getAppointmentDealNoType() {
        return appointmentDealNoType;
    }

    public void setAppointmentDealNoType(String appointmentDealNoType) {
        this.appointmentDealNoType = appointmentDealNoType;
    }

	public String getTargetProtocolNo() {
		return targetProtocolNo;
	}

	public void setTargetProtocolNo(String targetProtocolNo) {
		this.targetProtocolNo = targetProtocolNo;
	}

	public String getTargetBankAcct() {
		return targetBankAcct;
	}

	public void setTargetBankAcct(String targetBankAcct) {
		this.targetBankAcct = targetBankAcct;
	}

    public String getDualentryStatus() {
        return dualentryStatus;
    }

    public void setDualentryStatus(String dualentryStatus) {
        this.dualentryStatus = dualentryStatus;
    }

    public Date getDualentryFinishDtm() {
        return dualentryFinishDtm;
    }

    public void setDualentryFinishDtm(Date dualentryFinishDtm) {
        this.dualentryFinishDtm = dualentryFinishDtm;
    }

    public String getDualentryInterposeFlag() {
        return dualentryInterposeFlag;
    }

    public void setDualentryInterposeFlag(String dualentryInterposeFlag) {
        this.dualentryInterposeFlag = dualentryInterposeFlag;
    }

    public String getCallbackStatus() {
        return callbackStatus;
    }

    public void setCallbackStatus(String callbackStatus) {
        this.callbackStatus = callbackStatus;
    }

    public Date getCallbackFinishDtm() {
        return callbackFinishDtm;
    }

    public void setCallbackFinishDtm(Date callbackFinishDtm) {
        this.callbackFinishDtm = callbackFinishDtm;
    }

    public String getCallbackInterposeFlag() {
        return callbackInterposeFlag;
    }

    public void setCallbackInterposeFlag(String callbackInterposeFlag) {
        this.callbackInterposeFlag = callbackInterposeFlag;
    }

    public String getCalmdtmInterposeFlag() {
        return calmdtmInterposeFlag;
    }

    public void setCalmdtmInterposeFlag(String calmdtmInterposeFlag) {
        this.calmdtmInterposeFlag = calmdtmInterposeFlag;
    }

    public String getAssetcertificateStatus() {
        return assetcertificateStatus;
    }

    public void setAssetcertificateStatus(String assetcertificateStatus) {
        this.assetcertificateStatus = assetcertificateStatus;
    }

    public String getAssetInterposeFlag() {
        return assetInterposeFlag;
    }

    public void setAssetInterposeFlag(String assetInterposeFlag) {
        this.assetInterposeFlag = assetInterposeFlag;
    }

    public String getSubmitRetCode() {
        return submitRetCode;
    }

    public void setSubmitRetCode(String submitRetCode) {
        this.submitRetCode = submitRetCode;
    }

    public String getSubmitRetDesc() {
        return submitRetDesc;
    }

    public void setSubmitRetDesc(String submitRetDesc) {
        this.submitRetDesc = submitRetDesc;
    }

    public String getBusiCode() {
        return busiCode;
    }

    public void setBusiCode(String busiCode) {
        this.busiCode = busiCode;
    }

    public String getFundRiskLevel() {
        return fundRiskLevel;
    }

    public void setFundRiskLevel(String fundRiskLevel) {
        this.fundRiskLevel = fundRiskLevel;
    }

    public String getQualificationType() {
        return qualificationType;
    }

    public void setQualificationType(String qualificationType) {
        this.qualificationType = qualificationType;
    }

    public String getNetAppAmt() {
        return netAppAmt;
    }

    public void setNetAppAmt(String netAppAmt) {
        this.netAppAmt = netAppAmt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getTargetCpAcctNo() {
        return targetCpAcctNo;
    }

    public void setTargetCpAcctNo(String targetCpAcctNo) {
        this.targetCpAcctNo = targetCpAcctNo;
    }

    public String getRefundInfo() {
        return refundInfo;
    }

    public void setRefundInfo(String refundInfo) {
        this.refundInfo = refundInfo;
    }

    public String getHighFundInvPlanFlag() {
        return highFundInvPlanFlag;
    }

    public void setHighFundInvPlanFlag(String highFundInvPlanFlag) {
        this.highFundInvPlanFlag = highFundInvPlanFlag;
    }

    public String getInvestAckDtm() {
        return investAckDtm;
    }

    public void setInvestAckDtm(String investAckDtm) {
        this.investAckDtm = investAckDtm;
    }
}
