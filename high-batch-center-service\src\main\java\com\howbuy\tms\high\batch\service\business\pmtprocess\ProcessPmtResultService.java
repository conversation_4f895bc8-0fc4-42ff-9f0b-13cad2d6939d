/**
 * Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.batch.service.business.pmtprocess;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.cache.service.lock.LockService;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.enums.TxPmtFlagEnum;
import com.howbuy.tms.common.enums.busi.BusiProcessDirectEnum;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.OrderUpdateType;
import com.howbuy.tms.common.enums.busi.ProductTypeEsSysCodeMappingEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.DiffProcessStatusEnum;
import com.howbuy.tms.common.enums.database.PmtCompFlagEnum;
import com.howbuy.tms.common.enums.database.ProductChannelEnum;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.common.enums.database.TradeExcpSrcEnum;
import com.howbuy.tms.common.enums.database.TxAppFlagEnum;
import com.howbuy.tms.high.batch.dao.po.batch.HighTradeExceptionPo;
import com.howbuy.tms.high.batch.dao.po.order.DealOrderPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.dao.po.order.PaymentOrderPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.service.business.message.MsgNotifySendService;
import com.howbuy.tms.high.batch.service.business.quotaprocess.QuotaProcessService;
import com.howbuy.tms.high.batch.service.business.refreshdealorderstatus.RefreshDealOrderStatusService;
import com.howbuy.tms.high.batch.service.business.submittadtcal.SubmitTaDtCalService;
import com.howbuy.tms.high.batch.service.common.OpsSysMonitor;
import com.howbuy.tms.high.batch.service.exception.NotAllOrderPaidException;
import com.howbuy.tms.high.batch.service.logic.ComplianceSubmitCheckLogicService;
import com.howbuy.tms.high.batch.service.logic.bean.ComplianceSubmitCheckResultDto;
import com.howbuy.tms.high.batch.service.repository.HighTradeExceptionRepository;
import com.howbuy.tms.high.batch.service.repository.PaymentOrderRepository;
import com.howbuy.tms.high.batch.service.service.order.syncsubmittadt.SyncSubmitTaDtService;
import com.howbuy.tms.high.batch.service.service.order.tradepmthandle.TradePmtHandleService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;

/**
 * @description:(支付结果处理)
 * @reason:
 * <AUTHOR>
 * @date 2018年5月18日 下午5:09:40
 * @since JDK 1.6
 */
@Service("processPmtResultService")
public class ProcessPmtResultService {
    private static Logger logger = LogManager.getLogger(ProcessPmtResultService.class);


    /**
     * 交易支付处理服务
     */
    @Autowired
    private TradePmtHandleService tradePmtHandleService;

    @Autowired
    private PaymentOrderRepository paymentOrderRepository;

    @Autowired
    private HighTradeExceptionRepository highTradeExceptionRepository;

    @Autowired
    private QuotaProcessService quotaProcessService;

    @Autowired
    private SubmitTaDtCalService submitTaDtCalService;

    @Autowired
    private SyncSubmitTaDtService syncSubmitTaDtService;

    @Autowired
    private RefreshDealOrderStatusService refreshDealOrderStatusService;

    @Autowired
    private MsgNotifySendService msgNotifySendService;

    @Autowired
    private ComplianceSubmitCheckLogicService complianceSubmitCheckLogicService;


    @Autowired
    @Qualifier("cache.lockService")
    private LockService lockService;

    // 锁超时时间
    private static final int MAX_LOCK_TIME = 1;

    /**
     * refresh:(支付处理)
     *
     * @param order 订单
     * @param pmtOrder 支付订单
     * @param highDealOrderDtlPo 高端订单明细
     * @param txPmtFlag 交易申请标识
     * @param isForceUpdate 是否强制更新 
     * <AUTHOR>
     * @date 2018年5月18日 下午5:16:08
     */
    public void process(DealOrderPo order, PaymentOrderPo pmtOrder, HighDealOrderDtlPo highDealOrderDtlPo, String txPmtFlag, boolean isForceUpdate) {

        String lockKey = buildPayRstLockKey(pmtOrder.getPmtDealNo());
        boolean lock = lockService.getLock(lockKey, MAX_LOCK_TIME);
        try {
            if (lock) {
                logger.info("ProcessPmtResultService|get lock true, lockKey:{}", lockKey);
                // 支付之后更新支付明细订单表的回填信息
                Date oldUpdateDate = pmtOrder.getUpdateDtm();
                pmtOrder.setUpdateDtm(new Date());
                paymentOrderRepository.updatePmtOrderAfterPayCompletly(pmtOrder, oldUpdateDate);
                commonProcess(order, pmtOrder, highDealOrderDtlPo, txPmtFlag, isForceUpdate);
            } else {
                logger.info("ProcessPmtResultService|get lock false, lockKey:{}", lockKey);
            }
        } finally {
            lockService.releaseLock(lockKey);
        }

    }

    private String buildPayRstLockKey(String paymentDealNo) {
        return CacheKeyPrefix.HIGH_LOCK_PAY_RST_PREFIX + paymentDealNo;
    }

    /**
     * commonProcess:(通用处理)
     *
     *
     * @param order 订单
     * @param highDealOrderDtl 高端订单明细
     * @param pmtState 支付状态
     * @param isForceUpdate 是否强制更新
     * <AUTHOR>
     * @date 2018年5月18日 下午5:18:32
     */
    public void commonProcess(DealOrderPo order, PaymentOrderPo pmtOrder, HighDealOrderDtlPo highDealOrderDtl, String pmtState, boolean isForceUpdate) {
        // 备份原订单明细
        HighDealOrderDtlPo oldHighDealOrderDtlPo = new HighDealOrderDtlPo();
        BeanUtils.copyProperties(highDealOrderDtl, oldHighDealOrderDtlPo);
        String esSysCode = ProductTypeEsSysCodeMappingEnum.getSysCode(ProductClassEnum.HIGH.getCode(), highDealOrderDtl.getFundType());

        // 支付失败，更新交易支付标识“支付失败”，支付对账标识“无需对账”， 交易申请标识“申请失败”
        if (TxPmtFlagEnum.FAILED.getKey().equals(pmtState)) {
            pmtOrder.setTxPmtFlag(TxPmtFlagEnum.FAILED.getKey());
            pmtOrder.setPmtCompFlag(PmtCompFlagEnum.NOT.getCode());
            if (pmtOrder.getPmtCompleteDtm() == null) {
                pmtOrder.setPmtCompleteDtm(new Date());// 支付完成时间
            }
            tradePmtHandleService.updateStateOfDelOdrAndPmdOdr(order, highDealOrderDtl, pmtOrder, false, isForceUpdate, esSysCode);

            // 判断是否是高端定投交易，此时为第二次补偿扣款失败
            if (YesOrNoEnum.YES.getCode().equals(highDealOrderDtl.getHighFundInvPlanFlag())) {
                msgNotifySendService.sendHighInvPlanOrderStatusMessage(highDealOrderDtl.getDealNo(), pmtState, TxPmtFlagEnum.FAILED.getDesc());
            }

            // 发送kafka消息
            msgNotifySendService.sendKafka(order.getDealNo(), esSysCode, order.getTxAcctNo());
            // 通知外部系统，订单状态变化信息
            msgNotifySendService.sendHighActualMessage(order.getDealNo());
            // 通知机构，支付状态变化信息
            msgNotifySendService.sendInstPayResultMessage(pmtOrder.getDealNo(), pmtOrder.getTxPmtFlag(), pmtOrder.getDisCode());
        } else if (TxPmtFlagEnum.SUCCESSFUL.getKey().equals(pmtState)) {
            // 支付成功，更新支付标识“支付成功”，支付对账标识“未对账”
            pmtOrder.setTxPmtFlag(TxPmtFlagEnum.SUCCESSFUL.getKey());
            pmtOrder.setPmtCompFlag(PmtCompFlagEnum.NOT.getCode());
            if (pmtOrder.getPmtCompleteDtm() == null) {
                pmtOrder.setPmtCompleteDtm(new Date());// 支付完成时间
            }
            // 支付成功设置支付成功日期
            pmtOrder.setPaySuccessDt(pmtOrder.getPmtCheckDt());

            // 额度处理
            boolean processResult = quotaProcessService.process(order.getDealNo(), BusiProcessDirectEnum.FORWARD.getCode());
            logger.info("ProcessPmtResultService|commonProcess|dealNo:{}, processResult:{}", order.getDealNo(), processResult);

            // 更新订单和支付订单
            tradePmtHandleService.updateStateOfDelOdrAndPmdOdr(order, highDealOrderDtl, pmtOrder, processResult, isForceUpdate, esSysCode);

            // 判断是否是高端定投交易，此时为第二次补偿扣款成功
            if (YesOrNoEnum.YES.getCode().equals(highDealOrderDtl.getHighFundInvPlanFlag())) {
                msgNotifySendService.sendHighInvPlanOrderStatusMessage(highDealOrderDtl.getDealNo(), pmtState, null);
            }

            // 产品通道是高端公募支付完成重新计算上报日期
            if (processResult && ProductChannelEnum.HIGH_FUND.getCode().equals(highDealOrderDtl.getProductChannel())) {
                submitTaDtProcess(oldHighDealOrderDtlPo, order);
            }

            // 额度处理
            productLimitProcess(order, oldHighDealOrderDtlPo, processResult);
            // 发送kafka消息
            msgNotifySendService.sendKafka(order.getDealNo(), esSysCode, order.getTxAcctNo());
            // 通知外部系统，订单状态变化信息
            msgNotifySendService.sendHighActualMessage(order.getDealNo());
            // 通知机构，支付状态变化信息
            msgNotifySendService.sendInstPayResultMessage(pmtOrder.getDealNo(), pmtOrder.getTxPmtFlag(), pmtOrder.getDisCode());
        } else if (TxPmtFlagEnum.PIGGY_FRZ_SUCC.getKey().equals(pmtState)) {
            PaymentOrderPo currentPayOrder = paymentOrderRepository.getByPmtDealNo(pmtOrder.getPmtDealNo());
            if (!TxPmtFlagEnum.PIGGY_FRZING.getKey().equals(currentPayOrder.getTxPmtFlag())) {
                logger.warn("ProcessPmtResultService|commonProcess|非冻结中，不能更新为冻结成功,pmtDealNo:{}", pmtOrder.getPmtDealNo());
                return;
            }

            // 储蓄罐份额冻结成功
            // 支付成功，更新支付标识“支付成功”，支付对账标识“未对账”
            pmtOrder.setTxPmtFlag(TxPmtFlagEnum.PIGGY_FRZ_SUCC.getKey());
            pmtOrder.setPmtCompFlag(PmtCompFlagEnum.NOT.getCode());
            if (pmtOrder.getPmtCompleteDtm() == null) {
                // 支付完成时间
                pmtOrder.setPmtCompleteDtm(new Date());
            }

            // 冻结成功设置支付成功日期
            pmtOrder.setPaySuccessDt(pmtOrder.getPmtCheckDt());

            // 额度处理
            boolean processResult = quotaProcessService.process(order.getDealNo(), BusiProcessDirectEnum.FORWARD.getCode());
            logger.info("ProcessPmtResultService|commonProcess|dealNo:{}, processResult:{}", order.getDealNo(), processResult);

            // 更新订单和支付订单
            tradePmtHandleService.updateStateOfDelOdrAndPmdOdr(order, highDealOrderDtl, pmtOrder, processResult, isForceUpdate, esSysCode);

            // 判断是否是高端定投交易
            if (YesOrNoEnum.YES.getCode().equals(highDealOrderDtl.getHighFundInvPlanFlag())) {
                msgNotifySendService.sendHighInvPlanOrderStatusMessage(highDealOrderDtl.getDealNo(), pmtState, null);
            }

            // 产品通道是高端公募支付完成重新计算上报日期
            if (processResult && ProductChannelEnum.HIGH_FUND.getCode().equals(highDealOrderDtl.getProductChannel())) {
                submitTaDtProcess(oldHighDealOrderDtlPo, order);
            }

            // 额度处理
            productLimitProcess(order, oldHighDealOrderDtlPo, processResult);
            // 发送kafka消息
            msgNotifySendService.sendKafka(order.getDealNo(), esSysCode, order.getTxAcctNo());
            // 通知外部系统，订单状态变化信息
            msgNotifySendService.sendHighActualMessage(order.getDealNo());
            // 通知机构，支付状态变化信息
            msgNotifySendService.sendInstPayResultMessage(pmtOrder.getDealNo(), pmtOrder.getTxPmtFlag(), pmtOrder.getDisCode());
        } else if (TxPmtFlagEnum.PIGGY_FRZ_FAIL.getKey().equals(pmtState)) {
            // 告警
            logger.error( "ProcessPmtResultService|piggy frz er dealNo{}" ,order.getDealNo());
            String msg = String.format("ProcessPmtResultService|piggy frz er dealNo:%s", order.getDealNo());
            OpsSysMonitor.warn(msg, OpsSysMonitor.ERROR);
            pmtOrder.setTxPmtFlag(TxPmtFlagEnum.PIGGY_FRZ_FAIL.getKey());
            pmtOrder.setPmtCompFlag(PmtCompFlagEnum.NOT.getCode());
            paymentOrderRepository.updateFlagOfTxPmtAndPmtComp(pmtOrder, isForceUpdate);

            // 判断是否是高端定投交易
            if (YesOrNoEnum.YES.getCode().equals(highDealOrderDtl.getHighFundInvPlanFlag())) {
                msgNotifySendService.sendHighInvPlanOrderStatusMessage(highDealOrderDtl.getDealNo(), pmtState, TxPmtFlagEnum.PIGGY_FRZ_FAIL.getDesc());
            }

            // 发送kafka消息
            msgNotifySendService.sendKafka(order.getDealNo(), esSysCode, order.getTxAcctNo());
            // 通知外部系统，订单状态变化信息
            msgNotifySendService.sendHighActualMessage(order.getDealNo());
            // 通知机构，支付状态变化信息
            msgNotifySendService.sendInstPayResultMessage(pmtOrder.getDealNo(), pmtOrder.getTxPmtFlag(), pmtOrder.getDisCode());
        } else if (TxPmtFlagEnum.PIGGY_FRZ_PAY_SUCC.getKey().equals(pmtState)) {
            // 冻结支付成功
            pmtOrder.setTxPmtFlag(TxPmtFlagEnum.PIGGY_FRZ_PAY_SUCC.getKey());
            pmtOrder.setPmtCompFlag(PmtCompFlagEnum.NOT.getCode());

            paymentOrderRepository.updateFlagOfTxPmtAndPmtCompAndOldFlag(pmtOrder, TxPmtFlagEnum.PIGGY_FRZ_PAYING.getKey());

            // 判断是否是高端定投交易
            if (YesOrNoEnum.YES.getCode().equals(highDealOrderDtl.getHighFundInvPlanFlag())) {
                msgNotifySendService.sendHighInvPlanOrderStatusMessage(highDealOrderDtl.getDealNo(), pmtState, null);
            }

            // 发送kafka消息
            msgNotifySendService.sendKafka(order.getDealNo(), esSysCode, order.getTxAcctNo());
            // 通知外部系统，订单状态变化信息
            msgNotifySendService.sendHighActualMessage(order.getDealNo());
            // 通知机构，支付状态变化信息
            msgNotifySendService.sendInstPayResultMessage(pmtOrder.getDealNo(), pmtOrder.getTxPmtFlag(), pmtOrder.getDisCode());
        } else if (TxPmtFlagEnum.PIGGY_UN_FRZ_SUCC.getKey().equals(pmtState)) {
            // 解冻成功
            pmtOrder.setTxPmtFlag(TxPmtFlagEnum.PIGGY_UN_FRZ_SUCC.getKey());
            pmtOrder.setPmtCompFlag(PmtCompFlagEnum.NOT.getCode());
            paymentOrderRepository.updateFlagOfTxPmtAndPmtComp(pmtOrder, isForceUpdate);
            // 通知机构，支付状态变化信息
            msgNotifySendService.sendInstPayResultMessage(pmtOrder.getDealNo(), pmtOrder.getTxPmtFlag(), pmtOrder.getDisCode());
        } else if (TxPmtFlagEnum.PIGGY_FRZ_PAY_FAIL.getKey().equals(pmtState)) {
            pmtOrder.setTxPmtFlag(TxPmtFlagEnum.PIGGY_FRZ_PAY_FAIL.getKey());
            pmtOrder.setPmtCompFlag(PmtCompFlagEnum.NOT.getCode());
            // 冻结支付失败
            paymentOrderRepository.updateFlagOfTxPmtAndPmtComp(pmtOrder, isForceUpdate);
            // 判断是否是高端定投交易
            if (YesOrNoEnum.YES.getCode().equals(highDealOrderDtl.getHighFundInvPlanFlag())) {
                msgNotifySendService.sendHighInvPlanOrderStatusMessage(highDealOrderDtl.getDealNo(), pmtState, TxPmtFlagEnum.PIGGY_FRZ_PAY_FAIL.getDesc());
            }
            // 通知机构，支付状态变化信息
            msgNotifySendService.sendInstPayResultMessage(pmtOrder.getDealNo(), pmtOrder.getTxPmtFlag(), pmtOrder.getDisCode());
            // 告警
            logger.error("ProcessPmtResultService|piggy frz pay err, dealNo:{}" , pmtOrder.getDealNo());
            String msg = String.format("ProcessPmtResultService|piggy frz pay err, dealNo:%s", pmtOrder.getDealNo());
            OpsSysMonitor.warn(msg, OpsSysMonitor.ERROR);
        } else {
            // 刷新订单状态-更新支付中订单状态
            refreshDealOrderStatusService.refleshDealOrderStatus(order.getDealNo(), OrderUpdateType.ALL.getKey());
        }
    }

    /**
     * 上报TA日计算/更新处理
     * @param highDealOrderDtl
     * @param order
     */
    private void submitTaDtProcess(HighDealOrderDtlPo highDealOrderDtl, DealOrderPo order) {
        // 合规校验,如果校验不通过,先不发起冻结支付,不然影响客户收益
        ComplianceSubmitCheckResultDto complianceSubmitCheckResultDto = complianceSubmitCheckLogicService.complianceSubmitCheck(Collections.singletonList(highDealOrderDtl), new Date());
        logger.info("合规校验结果:{}", JSON.toJSONString(complianceSubmitCheckResultDto));
        if (!complianceSubmitCheckResultDto.checkAllPass()) {
            logger.info("submitTaDtProcess-合规校验不通过,不能更新上报日,dealNo={}", order.getDealNo());
            return;
        }
        String submitTaDt = null;
        try {
            submitTaDt = submitTaDtCalService.cal(highDealOrderDtl.getDealDtlNo());
        } catch (NotAllOrderPaidException e) {
            // 合并单没有全部支付完成，不更新上报日
            return;
        } catch (Exception e) {
            logger.error("ProcessPmtResultService|submitTaDtProcess|error msg:{}", e.getMessage(), e);
            // 创建计算上报TA日异常记录
            HighTradeExceptionPo highTradeExceptionPo = createHighTradeException(order, highDealOrderDtl, TradeExcpSrcEnum.SUBMITTADT_CAL_EXCP.getCode());

            // 新增异常记录
            highTradeExceptionRepository.insertSelective(highTradeExceptionPo);
            return;
        }

        syncSubmitTaDtService.execSyncSubmitTaDt(highDealOrderDtl, submitTaDt, highDealOrderDtl.getUpdateDtm());
    }

    /**
     * productLimitProcess:(额度后处理)
     *
     * @param order
     * @param highDealOrderDtl
     * @param processResult
     * <AUTHOR>
     * @date 2018年6月25日 下午3:30:38
     */
    private void productLimitProcess(DealOrderPo order, HighDealOrderDtlPo highDealOrderDtl, boolean processResult) {
        if (!TxAppFlagEnum.APP_SUCCESS.getCode().equals(highDealOrderDtl.getTxAppFlag())) {
            logger.info("ProcessPmtResultService|productLimitProcess|TxAppFlag is not succ, TxAppFlag:{}", highDealOrderDtl.getTxAppFlag());
            return;
        }

        // 额度处理失败
        if (!processResult) {
            // 创建额度校验异常记录
            HighTradeExceptionPo highTradeExceptionPo = createHighTradeException(order, highDealOrderDtl, TradeExcpSrcEnum.LIMIT_CANCEL.getCode());

            // 新增额度校验异常
            highTradeExceptionRepository.insertSelective(highTradeExceptionPo);
        }
    }

    /**
     * createHighTradeException:(创建异常订单)
     *
     * @param dealOrder
     * @param highDealOrderDtl
     * @param cancelSrc 撤单来源
     * @return
     * <AUTHOR>
     * @date 2018年6月5日 下午2:19:38
     */
    private HighTradeExceptionPo createHighTradeException(DealOrderPo dealOrder, HighDealOrderDtlPo highDealOrderDtl, String cancelSrc) {
        HighTradeExceptionPo highTradeExceptionPo = new HighTradeExceptionPo();
        highTradeExceptionPo.setSubmitDealNo(null);// 上报订单号
        highTradeExceptionPo.setDealNo(dealOrder.getDealNo());// 订单号
        highTradeExceptionPo.setDealDtlNo(highDealOrderDtl.getDealDtlNo());// 订单明细号
        highTradeExceptionPo.setContractNo(null);// 后台订单号
        highTradeExceptionPo.setTxAcctNo(dealOrder.getTxAcctNo());// 交易账号
        highTradeExceptionPo.setDisCode(dealOrder.getDisCode());// 分销机构号
        highTradeExceptionPo.setDisTxAcctNo(dealOrder.getDisTxAcctNo());// 分销交易账号
        highTradeExceptionPo.setTxChannel(dealOrder.getTxChannel());// 交易渠道
        highTradeExceptionPo.setOutletCode(dealOrder.getOutletCode());// 网点号
        highTradeExceptionPo.setCpAcctNo(dealOrder.getCpAcctNo());// 资金账号
        highTradeExceptionPo.setSubTxAcctNo(dealOrder.getSubTxAcctNo());// 子资金账号
        highTradeExceptionPo.setBankAcct(dealOrder.getBankAcct());// 银行卡号
        highTradeExceptionPo.setBankCode(dealOrder.getBankCode());// 银行代码
        highTradeExceptionPo.setInvstType(dealOrder.getInvstType());// 客户类型
        highTradeExceptionPo.setCustName(dealOrder.getCustName());// 客户姓名
        highTradeExceptionPo.setIdType(dealOrder.getIdType());// 证件类型
        highTradeExceptionPo.setIdNo(dealOrder.getIdNo());// 证件号
        highTradeExceptionPo.setFundCode(highDealOrderDtl.getFundCode());// 产品代码
        highTradeExceptionPo.setFundName(highDealOrderDtl.getFundName());// 产品名称
        highTradeExceptionPo.setFundType(highDealOrderDtl.getFundType());// 产品类型
        highTradeExceptionPo.setFundSubType(highDealOrderDtl.getFundSubType());// 产品子类型
        highTradeExceptionPo.setFundShareClass(highDealOrderDtl.getFundShareClass());// 产品份额类型
        highTradeExceptionPo.setAppDate(dealOrder.getAppDate());// 申请日期
        highTradeExceptionPo.setAppTime(dealOrder.getAppTime());// 申请时间
        highTradeExceptionPo.setAppAmt(highDealOrderDtl.getAppAmt());// 申请金额
        highTradeExceptionPo.setAppVol(highDealOrderDtl.getAppVol());// 申请份额
        highTradeExceptionPo.setAckAmt(highDealOrderDtl.getAckAmt());// 确认金额
        highTradeExceptionPo.setAckVol(highDealOrderDtl.getAckVol());// 确认份额
        highTradeExceptionPo.setAckDt(highDealOrderDtl.getAckDt());// 确认日期
        highTradeExceptionPo.setNav(highDealOrderDtl.getNav());// 净值
        highTradeExceptionPo.setFee(highDealOrderDtl.getFee());// 手续费
        highTradeExceptionPo.settFundCode(null);// 对方产品代码
        highTradeExceptionPo.settFundName(null);// 对方产品名称
        highTradeExceptionPo.settFundShareClass(null);// 对方产品份额类型
        highTradeExceptionPo.setTxRatio(null);// 成交比例
        highTradeExceptionPo.setTxStatus(null);// 成交状态
        highTradeExceptionPo.setTxCompleteDt(null);// 交易完成时间
        highTradeExceptionPo.setLargeRedmFlag(highDealOrderDtl.getLargeRedmFlag());// 巨额赎回标识
        highTradeExceptionPo.setRiskFlag(null);// 风险二次确认标识
        highTradeExceptionPo.setFundDivMode(highDealOrderDtl.getFundDivMode());// 产品分红方式
        highTradeExceptionPo.setTaTradeDt(dealOrder.getTaTradeDt());// TA交易日
        highTradeExceptionPo.setRedeemDirection(highDealOrderDtl.getRedeemDirection());// 赎回去向
        highTradeExceptionPo.setDiscountRate(highDealOrderDtl.getDiscountRate());// 折扣率
        highTradeExceptionPo.setCancelOrderSrc(null);// 撤单来源
        BusinessCodeEnum businessCodeEnum = BusinessCodeEnum.getByMCode(highDealOrderDtl.getmBusiCode());
        if (businessCodeEnum != null) {
            highTradeExceptionPo.setBusiCode(businessCodeEnum.getCode());// 后台业务码
        }
        highTradeExceptionPo.setmBusiCode(highDealOrderDtl.getmBusiCode());// 中台业务码
        highTradeExceptionPo.setTxAppFlag(highDealOrderDtl.getTxAppFlag());// 交易申请标识
        highTradeExceptionPo.setSubmitAppFlag(null);// 交易上报标识
        highTradeExceptionPo.setMemo(null);// 备注

        Date now = new Date();
        highTradeExceptionPo.setUpdateDtm(now);// 更新时间
        highTradeExceptionPo.setCreateDtm(now);// 创建时间
        highTradeExceptionPo.setTxCompFlag(null);// 交易对账标识
        highTradeExceptionPo.setCheckFlag(Constant.CHECK_FLAG_YES);// 审核标识
        highTradeExceptionPo.setOperator(Constant.OPERATOR_SYS);// 创建人
        highTradeExceptionPo.setChecker(Constant.OPERATOR_SYS);// 审核人
        highTradeExceptionPo.setProcessStatus(DiffProcessStatusEnum.UNPROCESS.getKey());
        highTradeExceptionPo.setTradeExcepSrc(TradeExcpSrcEnum.LIMIT_CANCEL.getCode());// 交易异常 来源
        highTradeExceptionPo.setTaCode(highDealOrderDtl.getTaCode());// ta代码
        return highTradeExceptionPo;
    }
}

