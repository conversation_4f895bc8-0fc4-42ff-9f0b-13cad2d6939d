package com.howbuy.tms.high.batch.service.task;

import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.high.batch.service.business.submitsubsamt.SubmitPeSubsAmtReportFileProcessor;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.common.utils.AbstractHowbuyBaseTask;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Description:异步上报股权认缴金额报表
 * @Author: yun.lu
 * Date: 2025/4/9 18:07
 */
@Data
@AllArgsConstructor
public class SubmitPeSubsAmtReportFileTask extends AbstractHowbuyBaseTask {
    private SubmitPeSubsAmtReportFileProcessor submitPeSubsAmtReportFileProcessor;

    @Override
    protected void callTask() {
        BaseResponse response = new BaseResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        submitPeSubsAmtReportFileProcessor.process(response);
    }
}
