package com.howbuy.tms.high.batch.service.task;

import com.howbuy.tms.high.batch.service.business.finadirectionmonitor.FinaDirectionFileRecPoProcessor;
import com.howbuy.tms.high.batch.service.common.utils.AbstractHowbuyBaseTask;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @Description:资金回款方向文件处理任务
 * @Author: yun.lu
 * Date: 2024/7/31 11:28
 */
public class FinaDirectionFileTask extends AbstractHowbuyBaseTask {
    private static Logger logger = LogManager.getLogger(FinaDirectionFileTask.class);
    private FinaDirectionFileRecPoProcessor finaDirectionFileRecPoProcessor;
    private String tradeDt;

    @Override
    protected void callTask() {
        finaDirectionFileRecPoProcessor.execute(tradeDt);
    }


    public FinaDirectionFileRecPoProcessor getFinaDirectionFileRecPoProcessor() {
        return finaDirectionFileRecPoProcessor;
    }

    public void setFinaDirectionFileRecPoProcessor(FinaDirectionFileRecPoProcessor finaDirectionFileRecPoProcessor) {
        this.finaDirectionFileRecPoProcessor = finaDirectionFileRecPoProcessor;
    }

    public String getTradeDt() {
        return tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public FinaDirectionFileTask(FinaDirectionFileRecPoProcessor finaDirectionFileRecPoProcessor, String tradeDt) {
        this.finaDirectionFileRecPoProcessor = finaDirectionFileRecPoProcessor;
        this.tradeDt = tradeDt;
    }
}
