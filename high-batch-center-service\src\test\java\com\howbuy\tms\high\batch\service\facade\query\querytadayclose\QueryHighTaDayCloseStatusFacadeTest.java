/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.query.querytadayclose;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.BatchStatEnum;
import com.howbuy.tms.common.enums.database.BusinessProcessingStepEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.high.batch.dao.po.batch.BusinessBatchFlowPo;
import com.howbuy.tms.high.batch.dao.po.batch.TaBusinessBatchFlowPo;
import com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusFacade;
import com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusRequest;
import com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusResponse;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.repository.BusinessBatchFlowRepository;
import com.howbuy.tms.high.batch.service.repository.TaBusinessBatchFlowRepository;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Description: 查询高端TA收市状态接口单元测试
 *
 * <AUTHOR>
 * @date 2025-05-16 10:56:54
 * @since JDK 1.8
 */
public class QueryHighTaDayCloseStatusFacadeTest {

    @InjectMocks
    private QueryHighTaDayCloseStatusFacadeService facadeService;

    @Mock
    private BusinessBatchFlowRepository businessBatchFlowRepository;

    @Mock
    private TaBusinessBatchFlowRepository taBusinessBatchFlowRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试参数为空的情况
     */
    @Test
    public void testExecuteWithEmptyParams() {
        // 准备测试数据
        QueryHighTaDayCloseStatusRequest request = new QueryHighTaDayCloseStatusRequest();
        
        // 执行测试
        QueryHighTaDayCloseStatusResponse response = facadeService.execute(request);
        
        // 验证结果
        assertEquals(ExceptionCodes.PARAMS_ERROR, response.getReturnCode());
    }

    /**
     * 测试工作日未初始化的情况
     */
    @Test
    public void testExecuteWithWorkdayNotInit() {
        // 准备测试数据
        QueryHighTaDayCloseStatusRequest request = new QueryHighTaDayCloseStatusRequest();
        request.setTaCode("001");
        request.setWorkDay("20250516");
        
        // 模拟依赖
        when(businessBatchFlowRepository.selectByTaskIdAndTradeDtAndSysCode(
                eq(BusinessProcessingStepEnum.BPS_WORK_DAY_INIT.getCode()),
                eq(request.getWorkDay()),
                eq(SysCodeEnum.BATCH_HIGH.getCode()))).thenReturn(null);
        
        // 执行测试
        QueryHighTaDayCloseStatusResponse response = facadeService.execute(request);
        
        // 验证结果
        assertEquals(ExceptionCodes.HIGH_BATCH_WORKDAY_NOT_DAY_INIT, response.getReturnCode());
    }

    /**
     * 测试不支持的TA的情况
     */
    @Test
    public void testExecuteWithNotSupportedTa() {
        // 准备测试数据
        QueryHighTaDayCloseStatusRequest request = new QueryHighTaDayCloseStatusRequest();
        request.setTaCode("001");
        request.setWorkDay("20250516");
        
        // 模拟依赖
        BusinessBatchFlowPo batchFlowPo = new BusinessBatchFlowPo();
        when(businessBatchFlowRepository.selectByTaskIdAndTradeDtAndSysCode(
                eq(BusinessProcessingStepEnum.BPS_WORK_DAY_INIT.getCode()),
                eq(request.getWorkDay()),
                eq(SysCodeEnum.BATCH_HIGH.getCode()))).thenReturn(batchFlowPo);
        
        when(taBusinessBatchFlowRepository.selectByTaCodeAndSysCodeAndTradeDtAndTaskId(
                eq(request.getTaCode()),
                eq(SysCodeEnum.BATCH_HIGH.getCode()),
                eq(request.getWorkDay()),
                eq(BusinessProcessingStepEnum.BPS_COUNTER_DAY_CLOSE.getCode()))).thenReturn(null);
        
        // 执行测试
        QueryHighTaDayCloseStatusResponse response = facadeService.execute(request);
        
        // 验证结果
        assertEquals(ExceptionCodes.SUCCESS, response.getReturnCode());
        assertEquals("2", response.getDayCloseStatus());
    }

    /**
     * 测试已收市的情况
     */
    @Test
    public void testExecuteWithAlreadyDayClose() {
        // 准备测试数据
        QueryHighTaDayCloseStatusRequest request = new QueryHighTaDayCloseStatusRequest();
        request.setTaCode("001");
        request.setWorkDay("20250516");
        
        // 模拟依赖
        BusinessBatchFlowPo batchFlowPo = new BusinessBatchFlowPo();
        when(businessBatchFlowRepository.selectByTaskIdAndTradeDtAndSysCode(
                eq(BusinessProcessingStepEnum.BPS_WORK_DAY_INIT.getCode()),
                eq(request.getWorkDay()),
                eq(SysCodeEnum.BATCH_HIGH.getCode()))).thenReturn(batchFlowPo);
        
        TaBusinessBatchFlowPo taBusinessBatchFlowPo = new TaBusinessBatchFlowPo();
        taBusinessBatchFlowPo.setFlowStat(BatchStatEnum.PROCESS_SUCCESS.getKey());
        when(taBusinessBatchFlowRepository.selectByTaCodeAndSysCodeAndTradeDtAndTaskId(
                eq(request.getTaCode()),
                eq(SysCodeEnum.BATCH_HIGH.getCode()),
                eq(request.getWorkDay()),
                eq(BusinessProcessingStepEnum.BPS_COUNTER_DAY_CLOSE.getCode()))).thenReturn(taBusinessBatchFlowPo);
        
        // 执行测试
        QueryHighTaDayCloseStatusResponse response = facadeService.execute(request);
        
        // 验证结果
        assertEquals(ExceptionCodes.SUCCESS, response.getReturnCode());
        assertEquals("0", response.getDayCloseStatus());
    }

    /**
     * 测试未收市的情况
     */
    @Test
    public void testExecuteWithNotDayClose() {
        // 准备测试数据
        QueryHighTaDayCloseStatusRequest request = new QueryHighTaDayCloseStatusRequest();
        request.setTaCode("001");
        request.setWorkDay("20250516");
        
        // 模拟依赖
        BusinessBatchFlowPo batchFlowPo = new BusinessBatchFlowPo();
        when(businessBatchFlowRepository.selectByTaskIdAndTradeDtAndSysCode(
                eq(BusinessProcessingStepEnum.BPS_WORK_DAY_INIT.getCode()),
                eq(request.getWorkDay()),
                eq(SysCodeEnum.BATCH_HIGH.getCode()))).thenReturn(batchFlowPo);
        
        TaBusinessBatchFlowPo taBusinessBatchFlowPo = new TaBusinessBatchFlowPo();
        taBusinessBatchFlowPo.setFlowStat(BatchStatEnum.NON_PROCESS.getKey()); // 非成功状态
        when(taBusinessBatchFlowRepository.selectByTaCodeAndSysCodeAndTradeDtAndTaskId(
                eq(request.getTaCode()),
                eq(SysCodeEnum.BATCH_HIGH.getCode()),
                eq(request.getWorkDay()),
                eq(BusinessProcessingStepEnum.BPS_COUNTER_DAY_CLOSE.getCode()))).thenReturn(taBusinessBatchFlowPo);
        
        // 执行测试
        QueryHighTaDayCloseStatusResponse response = facadeService.execute(request);
        
        // 验证结果
        assertEquals(ExceptionCodes.SUCCESS, response.getReturnCode());
        assertEquals("1", response.getDayCloseStatus());
    }
} 