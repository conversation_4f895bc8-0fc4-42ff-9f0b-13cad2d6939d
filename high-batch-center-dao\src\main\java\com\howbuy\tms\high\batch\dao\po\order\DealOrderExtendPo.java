package com.howbuy.tms.high.batch.dao.po.order;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class DealOrderExtendPo implements Serializable {
    private Long id;

    private String dealNo;

    private String transactorIdNo;

    private String transactorIdType;

    private String transactorName;

    private String operatorNo;

    private String consCode;

    private Date createDtm;

    private Date updateDtm;

    private String appointmentDealNo;

    private String recStat;

    private String riskAckDtm;

    private String highRiskTipDtm;

    private String normalCustTipDtm;

    private String portfolioFlag;

    private String riskRevealBookAckDtm;

    private String transferTubeBusiType;

    private String tOutletcode;

    private String tSellerTxacctno;

    private String tSellerCode;

    private String originalAppDealno;

    private String riskFlag;

    private String redeemDirection;

    private Date infoAckDtm;

    private String infoAckFileName;

    private String checkParamFlag;

    private String transactorIdNoCipher;

    private String oprReason;

    private String custRiskLevel;

    private String investAckDtm;

    private String expireOpType;

    private String couponId;

    private String couponType;

    private BigDecimal couponDiscountRate;

    private BigDecimal maxDeductible;

    private String liveAddress;

    private String cardAddress;

    private String compliancePrompt;

    private Integer datasource;

    private String serviceEntityName;

    private String fpqcCode;

    private Date serviceConfirmTime;
    /**
     * 风险评测日期 yyyyMMdd
     */
    private String riskToleranceDate;
    /**
     * 投资者类型认证日期 YYYYMMDD
     */
    private String investorQualifiedDate;
    /**
     * 风测提醒确定时间 yyyyMMddHHmmss
     */
    private String riskHintConfirmDtm;
    /**
     * 投资者类型认证提醒确认时间 yyyyMMddHHmmss
     */
    private String investorQualifiedHintConfirmDtm;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo == null ? null : dealNo.trim();
    }

    public String getTransactorIdNo() {
        return transactorIdNo;
    }

    public void setTransactorIdNo(String transactorIdNo) {
        this.transactorIdNo = transactorIdNo == null ? null : transactorIdNo.trim();
    }

    public String getTransactorIdType() {
        return transactorIdType;
    }

    public void setTransactorIdType(String transactorIdType) {
        this.transactorIdType = transactorIdType == null ? null : transactorIdType.trim();
    }

    public String getTransactorName() {
        return transactorName;
    }

    public void setTransactorName(String transactorName) {
        this.transactorName = transactorName == null ? null : transactorName.trim();
    }

    public String getOperatorNo() {
        return operatorNo;
    }

    public void setOperatorNo(String operatorNo) {
        this.operatorNo = operatorNo == null ? null : operatorNo.trim();
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode == null ? null : consCode.trim();
    }

    public Date getCreateDtm() {
        return createDtm;
    }

    public void setCreateDtm(Date createDtm) {
        this.createDtm = createDtm;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }

    public String getAppointmentDealNo() {
        return appointmentDealNo;
    }

    public void setAppointmentDealNo(String appointmentDealNo) {
        this.appointmentDealNo = appointmentDealNo == null ? null : appointmentDealNo.trim();
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat == null ? null : recStat.trim();
    }

    public String getRiskAckDtm() {
        return riskAckDtm;
    }

    public void setRiskAckDtm(String riskAckDtm) {
        this.riskAckDtm = riskAckDtm == null ? null : riskAckDtm.trim();
    }

    public String getHighRiskTipDtm() {
        return highRiskTipDtm;
    }

    public void setHighRiskTipDtm(String highRiskTipDtm) {
        this.highRiskTipDtm = highRiskTipDtm == null ? null : highRiskTipDtm.trim();
    }

    public String getNormalCustTipDtm() {
        return normalCustTipDtm;
    }

    public void setNormalCustTipDtm(String normalCustTipDtm) {
        this.normalCustTipDtm = normalCustTipDtm == null ? null : normalCustTipDtm.trim();
    }

    public String getPortfolioFlag() {
        return portfolioFlag;
    }

    public void setPortfolioFlag(String portfolioFlag) {
        this.portfolioFlag = portfolioFlag == null ? null : portfolioFlag.trim();
    }

    public String getRiskRevealBookAckDtm() {
        return riskRevealBookAckDtm;
    }

    public void setRiskRevealBookAckDtm(String riskRevealBookAckDtm) {
        this.riskRevealBookAckDtm = riskRevealBookAckDtm == null ? null : riskRevealBookAckDtm.trim();
    }

    public String getTransferTubeBusiType() {
        return transferTubeBusiType;
    }

    public void setTransferTubeBusiType(String transferTubeBusiType) {
        this.transferTubeBusiType = transferTubeBusiType == null ? null : transferTubeBusiType.trim();
    }

    public String gettOutletcode() {
        return tOutletcode;
    }

    public void settOutletcode(String tOutletcode) {
        this.tOutletcode = tOutletcode == null ? null : tOutletcode.trim();
    }

    public String gettSellerTxacctno() {
        return tSellerTxacctno;
    }

    public void settSellerTxacctno(String tSellerTxacctno) {
        this.tSellerTxacctno = tSellerTxacctno == null ? null : tSellerTxacctno.trim();
    }

    public String gettSellerCode() {
        return tSellerCode;
    }

    public void settSellerCode(String tSellerCode) {
        this.tSellerCode = tSellerCode == null ? null : tSellerCode.trim();
    }

    public String getOriginalAppDealno() {
        return originalAppDealno;
    }

    public void setOriginalAppDealno(String originalAppDealno) {
        this.originalAppDealno = originalAppDealno == null ? null : originalAppDealno.trim();
    }

    public String getRiskFlag() {
        return riskFlag;
    }

    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag == null ? null : riskFlag.trim();
    }

    public String getRedeemDirection() {
        return redeemDirection;
    }

    public void setRedeemDirection(String redeemDirection) {
        this.redeemDirection = redeemDirection == null ? null : redeemDirection.trim();
    }

    public Date getInfoAckDtm() {
        return infoAckDtm;
    }

    public void setInfoAckDtm(Date infoAckDtm) {
        this.infoAckDtm = infoAckDtm;
    }

    public String getInfoAckFileName() {
        return infoAckFileName;
    }

    public void setInfoAckFileName(String infoAckFileName) {
        this.infoAckFileName = infoAckFileName == null ? null : infoAckFileName.trim();
    }

    public String getCheckParamFlag() {
        return checkParamFlag;
    }

    public void setCheckParamFlag(String checkParamFlag) {
        this.checkParamFlag = checkParamFlag == null ? null : checkParamFlag.trim();
    }

    public String getTransactorIdNoCipher() {
        return transactorIdNoCipher;
    }

    public void setTransactorIdNoCipher(String transactorIdNoCipher) {
        this.transactorIdNoCipher = transactorIdNoCipher == null ? null : transactorIdNoCipher.trim();
    }

    public String getOprReason() {
        return oprReason;
    }

    public void setOprReason(String oprReason) {
        this.oprReason = oprReason == null ? null : oprReason.trim();
    }

    public String getCustRiskLevel() {
        return custRiskLevel;
    }

    public void setCustRiskLevel(String custRiskLevel) {
        this.custRiskLevel = custRiskLevel == null ? null : custRiskLevel.trim();
    }

    public String getInvestAckDtm() {
        return investAckDtm;
    }

    public void setInvestAckDtm(String investAckDtm) {
        this.investAckDtm = investAckDtm == null ? null : investAckDtm.trim();
    }

    public String getExpireOpType() {
        return expireOpType;
    }

    public void setExpireOpType(String expireOpType) {
        this.expireOpType = expireOpType == null ? null : expireOpType.trim();
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId == null ? null : couponId.trim();
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType == null ? null : couponType.trim();
    }

    public BigDecimal getCouponDiscountRate() {
        return couponDiscountRate;
    }

    public void setCouponDiscountRate(BigDecimal couponDiscountRate) {
        this.couponDiscountRate = couponDiscountRate;
    }

    public BigDecimal getMaxDeductible() {
        return maxDeductible;
    }

    public void setMaxDeductible(BigDecimal maxDeductible) {
        this.maxDeductible = maxDeductible;
    }

    public String getLiveAddress() {
        return liveAddress;
    }

    public void setLiveAddress(String liveAddress) {
        this.liveAddress = liveAddress == null ? null : liveAddress.trim();
    }

    public String getCardAddress() {
        return cardAddress;
    }

    public void setCardAddress(String cardAddress) {
        this.cardAddress = cardAddress == null ? null : cardAddress.trim();
    }

    public String getCompliancePrompt() {
        return compliancePrompt;
    }

    public void setCompliancePrompt(String compliancePrompt) {
        this.compliancePrompt = compliancePrompt == null ? null : compliancePrompt.trim();
    }

    public Integer getDatasource() {
        return datasource;
    }

    public void setDatasource(Integer datasource) {
        this.datasource = datasource;
    }

    public String getServiceEntityName() {
        return serviceEntityName;
    }

    public void setServiceEntityName(String serviceEntityName) {
        this.serviceEntityName = serviceEntityName == null ? null : serviceEntityName.trim();
    }

    public String getFpqcCode() {
        return fpqcCode;
    }

    public void setFpqcCode(String fpqcCode) {
        this.fpqcCode = fpqcCode == null ? null : fpqcCode.trim();
    }

    public Date getServiceConfirmTime() {
        return serviceConfirmTime;
    }

    public void setServiceConfirmTime(Date serviceConfirmTime) {
        this.serviceConfirmTime = serviceConfirmTime;
    }

    public String getRiskToleranceDate() {
        return riskToleranceDate;
    }

    public void setRiskToleranceDate(String riskToleranceDate) {
        this.riskToleranceDate = riskToleranceDate == null ? null : riskToleranceDate.trim();
    }

    public String getInvestorQualifiedDate() {
        return investorQualifiedDate;
    }

    public void setInvestorQualifiedDate(String investorQualifiedDate) {
        this.investorQualifiedDate = investorQualifiedDate == null ? null : investorQualifiedDate.trim();
    }

    public String getRiskHintConfirmDtm() {
        return riskHintConfirmDtm;
    }

    public void setRiskHintConfirmDtm(String riskHintConfirmDtm) {
        this.riskHintConfirmDtm = riskHintConfirmDtm == null ? null : riskHintConfirmDtm.trim();
    }

    public String getInvestorQualifiedHintConfirmDtm() {
        return investorQualifiedHintConfirmDtm;
    }

    public void setInvestorQualifiedHintConfirmDtm(String investorQualifiedHintConfirmDtm) {
        this.investorQualifiedHintConfirmDtm = investorQualifiedHintConfirmDtm == null ? null : investorQualifiedHintConfirmDtm.trim();
    }
}