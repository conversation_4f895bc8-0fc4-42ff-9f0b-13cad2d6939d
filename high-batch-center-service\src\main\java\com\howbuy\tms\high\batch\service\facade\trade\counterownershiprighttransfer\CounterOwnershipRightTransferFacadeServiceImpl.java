package com.howbuy.tms.high.batch.service.facade.trade.counterownershiprighttransfer;

import com.alibaba.fastjson.JSONObject;
import com.howbuy.tms.cache.service.lock.LockService;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.CounterCheckFlagEnum;
import com.howbuy.tms.common.enums.database.CounterOperaTypeEnum;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.mapper.batch.SubmitUncheckOrderPoAutoMapper;
import com.howbuy.tms.high.batch.dao.po.batch.SubmitUncheckOrderPo;
import com.howbuy.tms.high.batch.dao.po.batch.UncheckDealOrderDtlPo;
import com.howbuy.tms.high.batch.dao.po.batch.UncheckDealOrderDtlPoExample;
import com.howbuy.tms.high.batch.dao.po.order.DealOrderPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.bean.NoTradeOwnershipRightTransferDtlBean;
import com.howbuy.tms.high.batch.facade.trade.counterownershiprighttransferfacade.CounterOwnershipRightTransferFacade;
import com.howbuy.tms.high.batch.facade.trade.counterownershiprighttransferfacade.bean.CounterOwnershipRightTransferBean;
import com.howbuy.tms.high.batch.facade.trade.counterownershiprighttransferfacade.CounterOwnershipRightTransferRequest;
import com.howbuy.tms.high.batch.facade.trade.counterownershiprighttransferfacade.CounterOwnershipRightTransferResponse;
import com.howbuy.tms.high.batch.service.common.enums.UncheckDealOrderStatusEnum;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.facade.AbstractService;
import com.howbuy.tms.high.batch.service.logic.OwnershipTransferOrderLogicService;
import com.howbuy.tms.high.batch.service.repository.CounterOperatorRecRepository;
import com.howbuy.tms.high.batch.service.repository.UncheckDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.repository.DealOrderRepository;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.service.sequence.SequenceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:股权份额转让请求实现
 * @Author: yun.lu
 * Date: 2023/5/19 14:50
 */
@DubboService
@Service("counterOwnershipRightTransferFacade")
@Slf4j
public class CounterOwnershipRightTransferFacadeServiceImpl extends AbstractService<CounterOwnershipRightTransferRequest, CounterOwnershipRightTransferResponse> implements CounterOwnershipRightTransferFacade {
    @Autowired
    private SubmitUncheckOrderPoAutoMapper submitUncheckOrderPoAutoMapper;
    @Autowired
    private CounterOperatorRecRepository counterOperatorRecRepository;
    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private DealOrderRepository dealOrderRepository;
    @Autowired
    private OwnershipTransferOrderLogicService ownershipTransferOrderLogicService;
    @Autowired
    private LockService lockService;
    @Autowired
    private UncheckDealOrderDtlRepository uncheckDealOrderDtlRepository;

    @Override
    public CounterOwnershipRightTransferResponse process(CounterOwnershipRightTransferRequest request) {
        log.info("CounterOwnershipRightTransferFacadeServiceImpl-process,start,request={}", request);
        // 1.参数校验
        check(request);
        // 2.加锁
        String uniqKey = CacheKeyPrefix.HIGH_LOCK_CONCURRENT_PREFIX + "OWNERSHIP_TRANSFER_PREFIX" + request.getCounterOwnershipRightTransferBean().getDealDtlNo();
        boolean lock = lockService.getLock(uniqKey, 10);
        try {
            if (lock) {
                SubmitUncheckOrderPo submitUncheckOrderPo;
                // 3.幂等
                UncheckDealOrderDtlPoExample example = new UncheckDealOrderDtlPoExample();
                example.createCriteria().andDealDtlNoEqualTo(request.getCounterOwnershipRightTransferBean().getDealDtlNo()).andTxCodeEqualTo(TxCodes.OWNERSHIP_RIGHT_TRANSFER_TX_CODE).andStatusEqualTo(UncheckDealOrderStatusEnum.UN_CHECK.getStatus());
                List<UncheckDealOrderDtlPo> hasUncheckDealOrderList = uncheckDealOrderDtlRepository.selectByExample(example);
                if (CollectionUtils.isNotEmpty(hasUncheckDealOrderList)) {
                    log.info("CounterOwnershipRightTransferFacadeServiceImpl-process,已经有进行中的股权份额维护,不可以再次提交,uncheckDealOrderDtlPoList={}", JSONObject.toJSONString(hasUncheckDealOrderList));
                    CounterOwnershipRightTransferResponse resp = new CounterOwnershipRightTransferResponse();
                    resp.setDealAppNo(hasUncheckDealOrderList.get(0).getDealAppNo());
                    resp.setReturnCode(ExceptionCodes.SUCCESS);
                    return resp;
                }
                // 4.构建申请单实体
                List<UncheckDealOrderDtlPo> uncheckDealOrderDtlPoList = new ArrayList<>();
                submitUncheckOrderPo = new SubmitUncheckOrderPo();
                buildAppInfo(request.getCounterOwnershipRightTransferBean(), uncheckDealOrderDtlPoList, submitUncheckOrderPo);
                // 5.申请单持久化
                saveApplyOrder(submitUncheckOrderPo, uncheckDealOrderDtlPoList);
                // 6.返回结果
                CounterOwnershipRightTransferResponse resp = new CounterOwnershipRightTransferResponse();
                resp.setDealAppNo(submitUncheckOrderPo.getDealAppNo());
                resp.setReturnCode(ExceptionCodes.SUCCESS);
                return resp;
            } else {
                log.error("CounterOwnershipRightTransferFacadeServiceImpl-process,获取锁失败,uniqKey={}", uniqKey);
                throw new BatchException(ExceptionCodes.GET_LOCK_FAILED, "获取锁失败,请稍后重试");
            }
        } finally {
            if (lock) {
                lockService.releaseLock(uniqKey);
            }
        }
    }

    /**
     * 构建申请单/关联表实体
     *
     * @param bean 请求入参
     * @return 申请单
     */
    private void buildAppInfo(CounterOwnershipRightTransferBean bean, List<UncheckDealOrderDtlPo> uncheckDealOrderDtlPoList, SubmitUncheckOrderPo po) {
        // 1.查询订单信息
        HighDealOrderDtlPo highDealOrderDtlPo = highDealOrderDtlRepository.selectByDealDtlNo(bean.getDealDtlNo());
        if (highDealOrderDtlPo == null) {
            throw new BatchException(ExceptionCodes.PARAMS_ERROR, "根据订单号查不到订单信息:dealDtlNo:" + bean.getDealDtlNo());
        }
        DealOrderPo dealOrderPo = dealOrderRepository.getByDealNo(highDealOrderDtlPo.getDealNo());
        if (dealOrderPo == null) {
            throw new BatchException(ExceptionCodes.PARAMS_ERROR, "根据订单号查不到订单信息:dealNo:" + highDealOrderDtlPo.getDealNo());
        }
        // 2.构建申请单实体
        buildSubmitUncheckOrderPo(bean, po, highDealOrderDtlPo, dealOrderPo);
        // 3.构建关联记录实体
        buildUncheckDealOrderDtlPo(uncheckDealOrderDtlPoList, po, highDealOrderDtlPo);
    }

    /**
     * 构建关联记录实体
     */
    private void buildUncheckDealOrderDtlPo(List<UncheckDealOrderDtlPo> uncheckDealOrderDtlPoList, SubmitUncheckOrderPo submitUncheckOrderPo, HighDealOrderDtlPo highDealOrderDtlPo) {
        // 1.如果是非交易转入/转出,需要找到对应的转入/转出单,然后生成关联表
        if (ownershipTransferOrderLogicService.isNoTradeBusinessOrder(highDealOrderDtlPo.getmBusiCode())) {
            NoTradeOwnershipRightTransferDtlBean noTradeBean = (NoTradeOwnershipRightTransferDtlBean) ownershipTransferOrderLogicService.queryOwnershipRightTransferDtlByDealDtlNo(highDealOrderDtlPo.getDealDtlNo());
            if (StringUtils.isNotBlank(noTradeBean.getOutDealDtlNo())) {
                UncheckDealOrderDtlPo uncheckDealOrderDtlPo = new UncheckDealOrderDtlPo();
                uncheckDealOrderDtlPo.setDealDtlNo(noTradeBean.getOutDealDtlNo());
                uncheckDealOrderDtlPo.setDealAppNo(submitUncheckOrderPo.getDealAppNo());
                uncheckDealOrderDtlPo.setTxCode(TxCodes.OWNERSHIP_RIGHT_TRANSFER_TX_CODE);
                uncheckDealOrderDtlPo.setCreateTime(new Date());
                uncheckDealOrderDtlPo.setUpdateTime(new Date());
                uncheckDealOrderDtlPo.setStatus(UncheckDealOrderStatusEnum.UN_CHECK.getStatus());
                uncheckDealOrderDtlPo.setChecker(submitUncheckOrderPo.getChecker());
                uncheckDealOrderDtlPo.setAppEr(submitUncheckOrderPo.getModifier());
                uncheckDealOrderDtlPoList.add(uncheckDealOrderDtlPo);
            }
            if (StringUtils.isNotBlank(noTradeBean.getInDealDtlNo())) {
                UncheckDealOrderDtlPo uncheckDealOrderDtlPo = new UncheckDealOrderDtlPo();
                uncheckDealOrderDtlPo.setDealDtlNo(noTradeBean.getInDealDtlNo());
                uncheckDealOrderDtlPo.setDealAppNo(submitUncheckOrderPo.getDealAppNo());
                uncheckDealOrderDtlPo.setTxCode(TxCodes.OWNERSHIP_RIGHT_TRANSFER_TX_CODE);
                uncheckDealOrderDtlPo.setCreateTime(new Date());
                uncheckDealOrderDtlPo.setUpdateTime(new Date());
                uncheckDealOrderDtlPo.setStatus(UncheckDealOrderStatusEnum.UN_CHECK.getStatus());
                uncheckDealOrderDtlPo.setChecker(submitUncheckOrderPo.getChecker());
                uncheckDealOrderDtlPo.setAppEr(submitUncheckOrderPo.getModifier());
                uncheckDealOrderDtlPoList.add(uncheckDealOrderDtlPo);
            }

        } else {
            // 2.否则只需要建立当前订单的关联记录
            UncheckDealOrderDtlPo uncheckDealOrderDtlPo = new UncheckDealOrderDtlPo();
            uncheckDealOrderDtlPo.setDealDtlNo(highDealOrderDtlPo.getDealDtlNo());
            uncheckDealOrderDtlPo.setDealAppNo(submitUncheckOrderPo.getDealAppNo());
            uncheckDealOrderDtlPo.setTxCode(TxCodes.OWNERSHIP_RIGHT_TRANSFER_TX_CODE);
            uncheckDealOrderDtlPo.setCreateTime(new Date());
            uncheckDealOrderDtlPo.setUpdateTime(new Date());
            uncheckDealOrderDtlPo.setStatus(UncheckDealOrderStatusEnum.UN_CHECK.getStatus());
            uncheckDealOrderDtlPo.setChecker(submitUncheckOrderPo.getChecker());
            uncheckDealOrderDtlPo.setAppEr(submitUncheckOrderPo.getModifier());
            uncheckDealOrderDtlPoList.add(uncheckDealOrderDtlPo);
        }
    }

    /**
     * 构建变更申请单实体
     *
     * @param counterOwnershipRightTransferBean 变更申请入参
     * @param submitUncheckOrderPo              变更申请单实体
     * @param highDealOrderDtlPo                订单明细
     * @param dealOrderPo                       主单
     */
    private void buildSubmitUncheckOrderPo(CounterOwnershipRightTransferBean counterOwnershipRightTransferBean,
                                           SubmitUncheckOrderPo submitUncheckOrderPo, HighDealOrderDtlPo highDealOrderDtlPo, DealOrderPo dealOrderPo) {
        submitUncheckOrderPo.setDealAppNo(sequenceService.getDealNo(highDealOrderDtlPo.getTxAcctNo()));
        submitUncheckOrderPo.setDealNo(highDealOrderDtlPo.getDealNo());
        submitUncheckOrderPo.setTxAcctNo(highDealOrderDtlPo.getTxAcctNo());
        submitUncheckOrderPo.setCpAcctNo(highDealOrderDtlPo.getCpAcctNo());
        submitUncheckOrderPo.setCustName(dealOrderPo.getCustName());
        submitUncheckOrderPo.setIdNo(dealOrderPo.getIdNo());
        submitUncheckOrderPo.setIdType(dealOrderPo.getIdType());
        submitUncheckOrderPo.setDisCode(highDealOrderDtlPo.getDisCode());
        submitUncheckOrderPo.setmBusiCode(highDealOrderDtlPo.getmBusiCode());
        submitUncheckOrderPo.setAppAmt(highDealOrderDtlPo.getAckAmt());
        submitUncheckOrderPo.setAppVol(highDealOrderDtlPo.getAckVol());
        if (counterOwnershipRightTransferBean.getTransferPrice() != null) {
            submitUncheckOrderPo.setTransferPrice(counterOwnershipRightTransferBean.getTransferPrice());
        }
        submitUncheckOrderPo.setFundCode(highDealOrderDtlPo.getFundCode());
        submitUncheckOrderPo.setFundName(highDealOrderDtlPo.getFundName());
        // 当前日期
        Date now = new Date();
        submitUncheckOrderPo.setAppDt(DateUtils.formatToString(now, DateUtils.YYYYMMDD));
        submitUncheckOrderPo.setAppTm(DateUtils.formatToString(now, DateUtils.HHMMSS));
        String tradeDt = queryTradeDayOuterService.getWorkDay(submitUncheckOrderPo.getAppDt(), submitUncheckOrderPo.getAppTm());
        submitUncheckOrderPo.setTaTradeDt(tradeDt);
        submitUncheckOrderPo.setTxCode(TxCodes.OWNERSHIP_RIGHT_TRANSFER_TX_CODE);
        submitUncheckOrderPo.setProtocolNo(highDealOrderDtlPo.getProtocolNo());
        submitUncheckOrderPo.setProtocolType(highDealOrderDtlPo.getProtocolType());
        submitUncheckOrderPo.setCheckFlag(CounterCheckFlagEnum.NO_CHECKED.getKey());
        submitUncheckOrderPo.setProductClass(ProductClassEnum.HIGH.getCode());
        submitUncheckOrderPo.setOrderFormMemo(JSONObject.toJSONString(counterOwnershipRightTransferBean));
        submitUncheckOrderPo.setProductChannel(highDealOrderDtlPo.getProductChannel());
        submitUncheckOrderPo.setTaCode(highDealOrderDtlPo.getTaCode());
        // 如果需要转为非交易转,那么该标识标注,后续用来判断该申请单是否将订单由 交易->非交易;非交易->交易
        submitUncheckOrderPo.setMatchedTransfer(counterOwnershipRightTransferBean.getIsNoTradeTransfer());
        // 操作员编号
        submitUncheckOrderPo.setOperatorNo(counterOwnershipRightTransferBean.getOperatorNo());
        submitUncheckOrderPo.setCreator(counterOwnershipRightTransferBean.getCreator());
        submitUncheckOrderPo.setModifier(counterOwnershipRightTransferBean.getCreator());
        submitUncheckOrderPo.setCreateDtm(now);
        submitUncheckOrderPo.setUpdateDtm(now);
        submitUncheckOrderPo.setTotalSubsAmt(counterOwnershipRightTransferBean.getTotalSubsAmt());
        submitUncheckOrderPo.setSubsAmt(counterOwnershipRightTransferBean.getSubsAmt());
    }

    /**
     * 参数校验
     *
     * @param request 入参
     */
    private void check(CounterOwnershipRightTransferRequest request) {
        CounterOwnershipRightTransferBean bean = request.getCounterOwnershipRightTransferBean();
        // 订单号必传
        if (StringUtils.isBlank(bean.getDealDtlNo())) {
            throw new BatchException(ExceptionCodes.PARAMS_ERROR, "订单号dealDtlNo不能为空");
        }
        // 如果是非交已转让,转让价格非空
        if (YesOrNoEnum.YES.getCode().equals(bean.getIsNoTradeTransfer())) {
            if (bean.getTransferPrice() == null || BigDecimal.ZERO.compareTo(bean.getTransferPrice()) > 0) {
                throw new BatchException(ExceptionCodes.PARAMS_ERROR, "dealDtlNo:" + bean.getDealDtlNo() + "是非交易过户,交易价格不能为空/负数");
            }
        }
        // 过户份额对应的认缴金额不能为空
        if (bean.getSubsAmt() == null) {
            throw new BatchException(ExceptionCodes.PARAMS_ERROR, "过户份额对应的认缴金额不能为空");
        }
        // 过户的总认缴金额不能为空
        if (bean.getTotalSubsAmt() == null) {
            throw new BatchException(ExceptionCodes.PARAMS_ERROR, "过户的总认缴金额不能为空");
        }
    }

    /**
     * 保存申请
     */
    private void saveApplyOrder(SubmitUncheckOrderPo submitUncheckOrderPo, List<UncheckDealOrderDtlPo> uncheckDealOrderDtlPoList) {
        submitUncheckOrderPoAutoMapper.insertSelective(submitUncheckOrderPo);
        counterOperatorRecRepository.insertCounterOperator(null, submitUncheckOrderPo, CounterOperaTypeEnum.COUNTER_ADD.getKey());
        log.info("saveApplyOrder,uncheckDealOrderDtlPoList={}", JSONObject.toJSONString(uncheckDealOrderDtlPoList));
        for (UncheckDealOrderDtlPo uncheckDealOrderDtlPo : uncheckDealOrderDtlPoList) {
            uncheckDealOrderDtlRepository.insertSelective(uncheckDealOrderDtlPo);
        }
    }

}
