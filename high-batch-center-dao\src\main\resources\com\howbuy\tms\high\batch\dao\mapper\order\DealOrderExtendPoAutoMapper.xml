<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.tms.high.batch.dao.mapper.order.DealOrderExtendPoAutoMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.tms.high.batch.dao.po.order.DealOrderExtendPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="deal_no" jdbcType="VARCHAR" property="dealNo" />
    <result column="transactor_id_no" jdbcType="VARCHAR" property="transactorIdNo" />
    <result column="transactor_id_type" jdbcType="VARCHAR" property="transactorIdType" />
    <result column="transactor_name" jdbcType="VARCHAR" property="transactorName" />
    <result column="operator_no" jdbcType="VARCHAR" property="operatorNo" />
    <result column="cons_code" jdbcType="VARCHAR" property="consCode" />
    <result column="create_dtm" jdbcType="TIMESTAMP" property="createDtm" />
    <result column="update_dtm" jdbcType="TIMESTAMP" property="updateDtm" />
    <result column="appointment_deal_no" jdbcType="VARCHAR" property="appointmentDealNo" />
    <result column="rec_stat" jdbcType="VARCHAR" property="recStat" />
    <result column="risk_ack_dtm" jdbcType="VARCHAR" property="riskAckDtm" />
    <result column="high_risk_tip_dtm" jdbcType="VARCHAR" property="highRiskTipDtm" />
    <result column="normal_cust_tip_dtm" jdbcType="VARCHAR" property="normalCustTipDtm" />
    <result column="portfolio_flag" jdbcType="VARCHAR" property="portfolioFlag" />
    <result column="risk_reveal_book_ack_dtm" jdbcType="VARCHAR" property="riskRevealBookAckDtm" />
    <result column="transfer_tube_busi_type" jdbcType="CHAR" property="transferTubeBusiType" />
    <result column="t_outletcode" jdbcType="VARCHAR" property="tOutletcode" />
    <result column="t_seller_txacctno" jdbcType="VARCHAR" property="tSellerTxacctno" />
    <result column="t_seller_code" jdbcType="VARCHAR" property="tSellerCode" />
    <result column="original_app_dealno" jdbcType="VARCHAR" property="originalAppDealno" />
    <result column="risk_flag" jdbcType="VARCHAR" property="riskFlag" />
    <result column="redeem_direction" jdbcType="VARCHAR" property="redeemDirection" />
    <result column="info_ack_dtm" jdbcType="TIMESTAMP" property="infoAckDtm" />
    <result column="info_ack_file_name" jdbcType="VARCHAR" property="infoAckFileName" />
    <result column="check_param_flag" jdbcType="VARCHAR" property="checkParamFlag" />
    <result column="transactor_id_no_cipher" jdbcType="VARCHAR" property="transactorIdNoCipher" />
    <result column="opr_reason" jdbcType="VARCHAR" property="oprReason" />
    <result column="cust_risk_level" jdbcType="VARCHAR" property="custRiskLevel" />
    <result column="invest_ack_dtm" jdbcType="VARCHAR" property="investAckDtm" />
    <result column="expire_op_type" jdbcType="CHAR" property="expireOpType" />
    <result column="coupon_id" jdbcType="VARCHAR" property="couponId" />
    <result column="coupon_type" jdbcType="VARCHAR" property="couponType" />
    <result column="coupon_discount_rate" jdbcType="DECIMAL" property="couponDiscountRate" />
    <result column="max_deductible" jdbcType="DECIMAL" property="maxDeductible" />
    <result column="live_address" jdbcType="VARCHAR" property="liveAddress" />
    <result column="card_address" jdbcType="VARCHAR" property="cardAddress" />
    <result column="compliance_prompt" jdbcType="VARCHAR" property="compliancePrompt" />
    <result column="datasource" jdbcType="INTEGER" property="datasource" />
    <result column="service_entity_name" jdbcType="VARCHAR" property="serviceEntityName" />
    <result column="fpqc_code" jdbcType="VARCHAR" property="fpqcCode" />
    <result column="service_confirm_time" jdbcType="TIMESTAMP" property="serviceConfirmTime" />
    <result column="risk_tolerance_date" jdbcType="VARCHAR" property="riskToleranceDate" />
    <result column="investor_qualified_date" jdbcType="VARCHAR" property="investorQualifiedDate" />
    <result column="risk_hint_confirm_dtm" jdbcType="VARCHAR" property="riskHintConfirmDtm" />
    <result column="investor_qualified_hint_confirm_dtm" jdbcType="VARCHAR" property="investorQualifiedHintConfirmDtm" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, deal_no, transactor_id_no, transactor_id_type, transactor_name, operator_no, 
    cons_code, create_dtm, update_dtm, appointment_deal_no, rec_stat, risk_ack_dtm, high_risk_tip_dtm, 
    normal_cust_tip_dtm, portfolio_flag, risk_reveal_book_ack_dtm, transfer_tube_busi_type, 
    t_outletcode, t_seller_txacctno, t_seller_code, original_app_dealno, risk_flag, redeem_direction, 
    info_ack_dtm, info_ack_file_name, check_param_flag, transactor_id_no_cipher, opr_reason, 
    cust_risk_level, invest_ack_dtm, expire_op_type, coupon_id, coupon_type, coupon_discount_rate, 
    max_deductible, live_address, card_address, compliance_prompt, datasource, service_entity_name, 
    fpqc_code, service_confirm_time, risk_tolerance_date, investor_qualified_date, risk_hint_confirm_dtm, 
    investor_qualified_hint_confirm_dtm
  </sql>
  <select id="selectByExample" parameterType="com.howbuy.tms.high.batch.dao.po.order.DealOrderExtendPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from deal_order_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from deal_order_extend
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from deal_order_extend
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.howbuy.tms.high.batch.dao.po.order.DealOrderExtendPoExample">
    delete from deal_order_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.howbuy.tms.high.batch.dao.po.order.DealOrderExtendPo">
    insert into deal_order_extend (id, deal_no, transactor_id_no, 
      transactor_id_type, transactor_name, operator_no, 
      cons_code, create_dtm, update_dtm, 
      appointment_deal_no, rec_stat, risk_ack_dtm, 
      high_risk_tip_dtm, normal_cust_tip_dtm, portfolio_flag, 
      risk_reveal_book_ack_dtm, transfer_tube_busi_type, 
      t_outletcode, t_seller_txacctno, t_seller_code, 
      original_app_dealno, risk_flag, redeem_direction, 
      info_ack_dtm, info_ack_file_name, check_param_flag, 
      transactor_id_no_cipher, opr_reason, cust_risk_level, 
      invest_ack_dtm, expire_op_type, coupon_id, 
      coupon_type, coupon_discount_rate, max_deductible, 
      live_address, card_address, compliance_prompt, 
      datasource, service_entity_name, fpqc_code, 
      service_confirm_time, risk_tolerance_date, 
      investor_qualified_date, risk_hint_confirm_dtm, 
      investor_qualified_hint_confirm_dtm)
    values (#{id,jdbcType=BIGINT}, #{dealNo,jdbcType=VARCHAR}, #{transactorIdNo,jdbcType=VARCHAR}, 
      #{transactorIdType,jdbcType=VARCHAR}, #{transactorName,jdbcType=VARCHAR}, #{operatorNo,jdbcType=VARCHAR}, 
      #{consCode,jdbcType=VARCHAR}, #{createDtm,jdbcType=TIMESTAMP}, #{updateDtm,jdbcType=TIMESTAMP}, 
      #{appointmentDealNo,jdbcType=VARCHAR}, #{recStat,jdbcType=VARCHAR}, #{riskAckDtm,jdbcType=VARCHAR}, 
      #{highRiskTipDtm,jdbcType=VARCHAR}, #{normalCustTipDtm,jdbcType=VARCHAR}, #{portfolioFlag,jdbcType=VARCHAR}, 
      #{riskRevealBookAckDtm,jdbcType=VARCHAR}, #{transferTubeBusiType,jdbcType=CHAR}, 
      #{tOutletcode,jdbcType=VARCHAR}, #{tSellerTxacctno,jdbcType=VARCHAR}, #{tSellerCode,jdbcType=VARCHAR}, 
      #{originalAppDealno,jdbcType=VARCHAR}, #{riskFlag,jdbcType=VARCHAR}, #{redeemDirection,jdbcType=VARCHAR}, 
      #{infoAckDtm,jdbcType=TIMESTAMP}, #{infoAckFileName,jdbcType=VARCHAR}, #{checkParamFlag,jdbcType=VARCHAR}, 
      #{transactorIdNoCipher,jdbcType=VARCHAR}, #{oprReason,jdbcType=VARCHAR}, #{custRiskLevel,jdbcType=VARCHAR}, 
      #{investAckDtm,jdbcType=VARCHAR}, #{expireOpType,jdbcType=CHAR}, #{couponId,jdbcType=VARCHAR}, 
      #{couponType,jdbcType=VARCHAR}, #{couponDiscountRate,jdbcType=DECIMAL}, #{maxDeductible,jdbcType=DECIMAL}, 
      #{liveAddress,jdbcType=VARCHAR}, #{cardAddress,jdbcType=VARCHAR}, #{compliancePrompt,jdbcType=VARCHAR}, 
      #{datasource,jdbcType=INTEGER}, #{serviceEntityName,jdbcType=VARCHAR}, #{fpqcCode,jdbcType=VARCHAR}, 
      #{serviceConfirmTime,jdbcType=TIMESTAMP}, #{riskToleranceDate,jdbcType=VARCHAR}, 
      #{investorQualifiedDate,jdbcType=VARCHAR}, #{riskHintConfirmDtm,jdbcType=VARCHAR}, 
      #{investorQualifiedHintConfirmDtm,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.tms.high.batch.dao.po.order.DealOrderExtendPo">
    insert into deal_order_extend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dealNo != null">
        deal_no,
      </if>
      <if test="transactorIdNo != null">
        transactor_id_no,
      </if>
      <if test="transactorIdType != null">
        transactor_id_type,
      </if>
      <if test="transactorName != null">
        transactor_name,
      </if>
      <if test="operatorNo != null">
        operator_no,
      </if>
      <if test="consCode != null">
        cons_code,
      </if>
      <if test="createDtm != null">
        create_dtm,
      </if>
      <if test="updateDtm != null">
        update_dtm,
      </if>
      <if test="appointmentDealNo != null">
        appointment_deal_no,
      </if>
      <if test="recStat != null">
        rec_stat,
      </if>
      <if test="riskAckDtm != null">
        risk_ack_dtm,
      </if>
      <if test="highRiskTipDtm != null">
        high_risk_tip_dtm,
      </if>
      <if test="normalCustTipDtm != null">
        normal_cust_tip_dtm,
      </if>
      <if test="portfolioFlag != null">
        portfolio_flag,
      </if>
      <if test="riskRevealBookAckDtm != null">
        risk_reveal_book_ack_dtm,
      </if>
      <if test="transferTubeBusiType != null">
        transfer_tube_busi_type,
      </if>
      <if test="tOutletcode != null">
        t_outletcode,
      </if>
      <if test="tSellerTxacctno != null">
        t_seller_txacctno,
      </if>
      <if test="tSellerCode != null">
        t_seller_code,
      </if>
      <if test="originalAppDealno != null">
        original_app_dealno,
      </if>
      <if test="riskFlag != null">
        risk_flag,
      </if>
      <if test="redeemDirection != null">
        redeem_direction,
      </if>
      <if test="infoAckDtm != null">
        info_ack_dtm,
      </if>
      <if test="infoAckFileName != null">
        info_ack_file_name,
      </if>
      <if test="checkParamFlag != null">
        check_param_flag,
      </if>
      <if test="transactorIdNoCipher != null">
        transactor_id_no_cipher,
      </if>
      <if test="oprReason != null">
        opr_reason,
      </if>
      <if test="custRiskLevel != null">
        cust_risk_level,
      </if>
      <if test="investAckDtm != null">
        invest_ack_dtm,
      </if>
      <if test="expireOpType != null">
        expire_op_type,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="couponType != null">
        coupon_type,
      </if>
      <if test="couponDiscountRate != null">
        coupon_discount_rate,
      </if>
      <if test="maxDeductible != null">
        max_deductible,
      </if>
      <if test="liveAddress != null">
        live_address,
      </if>
      <if test="cardAddress != null">
        card_address,
      </if>
      <if test="compliancePrompt != null">
        compliance_prompt,
      </if>
      <if test="datasource != null">
        datasource,
      </if>
      <if test="serviceEntityName != null">
        service_entity_name,
      </if>
      <if test="fpqcCode != null">
        fpqc_code,
      </if>
      <if test="serviceConfirmTime != null">
        service_confirm_time,
      </if>
      <if test="riskToleranceDate != null">
        risk_tolerance_date,
      </if>
      <if test="investorQualifiedDate != null">
        investor_qualified_date,
      </if>
      <if test="riskHintConfirmDtm != null">
        risk_hint_confirm_dtm,
      </if>
      <if test="investorQualifiedHintConfirmDtm != null">
        investor_qualified_hint_confirm_dtm,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dealNo != null">
        #{dealNo,jdbcType=VARCHAR},
      </if>
      <if test="transactorIdNo != null">
        #{transactorIdNo,jdbcType=VARCHAR},
      </if>
      <if test="transactorIdType != null">
        #{transactorIdType,jdbcType=VARCHAR},
      </if>
      <if test="transactorName != null">
        #{transactorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorNo != null">
        #{operatorNo,jdbcType=VARCHAR},
      </if>
      <if test="consCode != null">
        #{consCode,jdbcType=VARCHAR},
      </if>
      <if test="createDtm != null">
        #{createDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDtm != null">
        #{updateDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="appointmentDealNo != null">
        #{appointmentDealNo,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="riskAckDtm != null">
        #{riskAckDtm,jdbcType=VARCHAR},
      </if>
      <if test="highRiskTipDtm != null">
        #{highRiskTipDtm,jdbcType=VARCHAR},
      </if>
      <if test="normalCustTipDtm != null">
        #{normalCustTipDtm,jdbcType=VARCHAR},
      </if>
      <if test="portfolioFlag != null">
        #{portfolioFlag,jdbcType=VARCHAR},
      </if>
      <if test="riskRevealBookAckDtm != null">
        #{riskRevealBookAckDtm,jdbcType=VARCHAR},
      </if>
      <if test="transferTubeBusiType != null">
        #{transferTubeBusiType,jdbcType=CHAR},
      </if>
      <if test="tOutletcode != null">
        #{tOutletcode,jdbcType=VARCHAR},
      </if>
      <if test="tSellerTxacctno != null">
        #{tSellerTxacctno,jdbcType=VARCHAR},
      </if>
      <if test="tSellerCode != null">
        #{tSellerCode,jdbcType=VARCHAR},
      </if>
      <if test="originalAppDealno != null">
        #{originalAppDealno,jdbcType=VARCHAR},
      </if>
      <if test="riskFlag != null">
        #{riskFlag,jdbcType=VARCHAR},
      </if>
      <if test="redeemDirection != null">
        #{redeemDirection,jdbcType=VARCHAR},
      </if>
      <if test="infoAckDtm != null">
        #{infoAckDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="infoAckFileName != null">
        #{infoAckFileName,jdbcType=VARCHAR},
      </if>
      <if test="checkParamFlag != null">
        #{checkParamFlag,jdbcType=VARCHAR},
      </if>
      <if test="transactorIdNoCipher != null">
        #{transactorIdNoCipher,jdbcType=VARCHAR},
      </if>
      <if test="oprReason != null">
        #{oprReason,jdbcType=VARCHAR},
      </if>
      <if test="custRiskLevel != null">
        #{custRiskLevel,jdbcType=VARCHAR},
      </if>
      <if test="investAckDtm != null">
        #{investAckDtm,jdbcType=VARCHAR},
      </if>
      <if test="expireOpType != null">
        #{expireOpType,jdbcType=CHAR},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="couponType != null">
        #{couponType,jdbcType=VARCHAR},
      </if>
      <if test="couponDiscountRate != null">
        #{couponDiscountRate,jdbcType=DECIMAL},
      </if>
      <if test="maxDeductible != null">
        #{maxDeductible,jdbcType=DECIMAL},
      </if>
      <if test="liveAddress != null">
        #{liveAddress,jdbcType=VARCHAR},
      </if>
      <if test="cardAddress != null">
        #{cardAddress,jdbcType=VARCHAR},
      </if>
      <if test="compliancePrompt != null">
        #{compliancePrompt,jdbcType=VARCHAR},
      </if>
      <if test="datasource != null">
        #{datasource,jdbcType=INTEGER},
      </if>
      <if test="serviceEntityName != null">
        #{serviceEntityName,jdbcType=VARCHAR},
      </if>
      <if test="fpqcCode != null">
        #{fpqcCode,jdbcType=VARCHAR},
      </if>
      <if test="serviceConfirmTime != null">
        #{serviceConfirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="riskToleranceDate != null">
        #{riskToleranceDate,jdbcType=VARCHAR},
      </if>
      <if test="investorQualifiedDate != null">
        #{investorQualifiedDate,jdbcType=VARCHAR},
      </if>
      <if test="riskHintConfirmDtm != null">
        #{riskHintConfirmDtm,jdbcType=VARCHAR},
      </if>
      <if test="investorQualifiedHintConfirmDtm != null">
        #{investorQualifiedHintConfirmDtm,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.howbuy.tms.high.batch.dao.po.order.DealOrderExtendPoExample" resultType="java.lang.Long">
    select count(*) from deal_order_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update deal_order_extend
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dealNo != null">
        deal_no = #{record.dealNo,jdbcType=VARCHAR},
      </if>
      <if test="record.transactorIdNo != null">
        transactor_id_no = #{record.transactorIdNo,jdbcType=VARCHAR},
      </if>
      <if test="record.transactorIdType != null">
        transactor_id_type = #{record.transactorIdType,jdbcType=VARCHAR},
      </if>
      <if test="record.transactorName != null">
        transactor_name = #{record.transactorName,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorNo != null">
        operator_no = #{record.operatorNo,jdbcType=VARCHAR},
      </if>
      <if test="record.consCode != null">
        cons_code = #{record.consCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createDtm != null">
        create_dtm = #{record.createDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateDtm != null">
        update_dtm = #{record.updateDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="record.appointmentDealNo != null">
        appointment_deal_no = #{record.appointmentDealNo,jdbcType=VARCHAR},
      </if>
      <if test="record.recStat != null">
        rec_stat = #{record.recStat,jdbcType=VARCHAR},
      </if>
      <if test="record.riskAckDtm != null">
        risk_ack_dtm = #{record.riskAckDtm,jdbcType=VARCHAR},
      </if>
      <if test="record.highRiskTipDtm != null">
        high_risk_tip_dtm = #{record.highRiskTipDtm,jdbcType=VARCHAR},
      </if>
      <if test="record.normalCustTipDtm != null">
        normal_cust_tip_dtm = #{record.normalCustTipDtm,jdbcType=VARCHAR},
      </if>
      <if test="record.portfolioFlag != null">
        portfolio_flag = #{record.portfolioFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.riskRevealBookAckDtm != null">
        risk_reveal_book_ack_dtm = #{record.riskRevealBookAckDtm,jdbcType=VARCHAR},
      </if>
      <if test="record.transferTubeBusiType != null">
        transfer_tube_busi_type = #{record.transferTubeBusiType,jdbcType=CHAR},
      </if>
      <if test="record.tOutletcode != null">
        t_outletcode = #{record.tOutletcode,jdbcType=VARCHAR},
      </if>
      <if test="record.tSellerTxacctno != null">
        t_seller_txacctno = #{record.tSellerTxacctno,jdbcType=VARCHAR},
      </if>
      <if test="record.tSellerCode != null">
        t_seller_code = #{record.tSellerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.originalAppDealno != null">
        original_app_dealno = #{record.originalAppDealno,jdbcType=VARCHAR},
      </if>
      <if test="record.riskFlag != null">
        risk_flag = #{record.riskFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.redeemDirection != null">
        redeem_direction = #{record.redeemDirection,jdbcType=VARCHAR},
      </if>
      <if test="record.infoAckDtm != null">
        info_ack_dtm = #{record.infoAckDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="record.infoAckFileName != null">
        info_ack_file_name = #{record.infoAckFileName,jdbcType=VARCHAR},
      </if>
      <if test="record.checkParamFlag != null">
        check_param_flag = #{record.checkParamFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.transactorIdNoCipher != null">
        transactor_id_no_cipher = #{record.transactorIdNoCipher,jdbcType=VARCHAR},
      </if>
      <if test="record.oprReason != null">
        opr_reason = #{record.oprReason,jdbcType=VARCHAR},
      </if>
      <if test="record.custRiskLevel != null">
        cust_risk_level = #{record.custRiskLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.investAckDtm != null">
        invest_ack_dtm = #{record.investAckDtm,jdbcType=VARCHAR},
      </if>
      <if test="record.expireOpType != null">
        expire_op_type = #{record.expireOpType,jdbcType=CHAR},
      </if>
      <if test="record.couponId != null">
        coupon_id = #{record.couponId,jdbcType=VARCHAR},
      </if>
      <if test="record.couponType != null">
        coupon_type = #{record.couponType,jdbcType=VARCHAR},
      </if>
      <if test="record.couponDiscountRate != null">
        coupon_discount_rate = #{record.couponDiscountRate,jdbcType=DECIMAL},
      </if>
      <if test="record.maxDeductible != null">
        max_deductible = #{record.maxDeductible,jdbcType=DECIMAL},
      </if>
      <if test="record.liveAddress != null">
        live_address = #{record.liveAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.cardAddress != null">
        card_address = #{record.cardAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.compliancePrompt != null">
        compliance_prompt = #{record.compliancePrompt,jdbcType=VARCHAR},
      </if>
      <if test="record.datasource != null">
        datasource = #{record.datasource,jdbcType=INTEGER},
      </if>
      <if test="record.serviceEntityName != null">
        service_entity_name = #{record.serviceEntityName,jdbcType=VARCHAR},
      </if>
      <if test="record.fpqcCode != null">
        fpqc_code = #{record.fpqcCode,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceConfirmTime != null">
        service_confirm_time = #{record.serviceConfirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.riskToleranceDate != null">
        risk_tolerance_date = #{record.riskToleranceDate,jdbcType=VARCHAR},
      </if>
      <if test="record.investorQualifiedDate != null">
        investor_qualified_date = #{record.investorQualifiedDate,jdbcType=VARCHAR},
      </if>
      <if test="record.riskHintConfirmDtm != null">
        risk_hint_confirm_dtm = #{record.riskHintConfirmDtm,jdbcType=VARCHAR},
      </if>
      <if test="record.investorQualifiedHintConfirmDtm != null">
        investor_qualified_hint_confirm_dtm = #{record.investorQualifiedHintConfirmDtm,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update deal_order_extend
    set id = #{record.id,jdbcType=BIGINT},
      deal_no = #{record.dealNo,jdbcType=VARCHAR},
      transactor_id_no = #{record.transactorIdNo,jdbcType=VARCHAR},
      transactor_id_type = #{record.transactorIdType,jdbcType=VARCHAR},
      transactor_name = #{record.transactorName,jdbcType=VARCHAR},
      operator_no = #{record.operatorNo,jdbcType=VARCHAR},
      cons_code = #{record.consCode,jdbcType=VARCHAR},
      create_dtm = #{record.createDtm,jdbcType=TIMESTAMP},
      update_dtm = #{record.updateDtm,jdbcType=TIMESTAMP},
      appointment_deal_no = #{record.appointmentDealNo,jdbcType=VARCHAR},
      rec_stat = #{record.recStat,jdbcType=VARCHAR},
      risk_ack_dtm = #{record.riskAckDtm,jdbcType=VARCHAR},
      high_risk_tip_dtm = #{record.highRiskTipDtm,jdbcType=VARCHAR},
      normal_cust_tip_dtm = #{record.normalCustTipDtm,jdbcType=VARCHAR},
      portfolio_flag = #{record.portfolioFlag,jdbcType=VARCHAR},
      risk_reveal_book_ack_dtm = #{record.riskRevealBookAckDtm,jdbcType=VARCHAR},
      transfer_tube_busi_type = #{record.transferTubeBusiType,jdbcType=CHAR},
      t_outletcode = #{record.tOutletcode,jdbcType=VARCHAR},
      t_seller_txacctno = #{record.tSellerTxacctno,jdbcType=VARCHAR},
      t_seller_code = #{record.tSellerCode,jdbcType=VARCHAR},
      original_app_dealno = #{record.originalAppDealno,jdbcType=VARCHAR},
      risk_flag = #{record.riskFlag,jdbcType=VARCHAR},
      redeem_direction = #{record.redeemDirection,jdbcType=VARCHAR},
      info_ack_dtm = #{record.infoAckDtm,jdbcType=TIMESTAMP},
      info_ack_file_name = #{record.infoAckFileName,jdbcType=VARCHAR},
      check_param_flag = #{record.checkParamFlag,jdbcType=VARCHAR},
      transactor_id_no_cipher = #{record.transactorIdNoCipher,jdbcType=VARCHAR},
      opr_reason = #{record.oprReason,jdbcType=VARCHAR},
      cust_risk_level = #{record.custRiskLevel,jdbcType=VARCHAR},
      invest_ack_dtm = #{record.investAckDtm,jdbcType=VARCHAR},
      expire_op_type = #{record.expireOpType,jdbcType=CHAR},
      coupon_id = #{record.couponId,jdbcType=VARCHAR},
      coupon_type = #{record.couponType,jdbcType=VARCHAR},
      coupon_discount_rate = #{record.couponDiscountRate,jdbcType=DECIMAL},
      max_deductible = #{record.maxDeductible,jdbcType=DECIMAL},
      live_address = #{record.liveAddress,jdbcType=VARCHAR},
      card_address = #{record.cardAddress,jdbcType=VARCHAR},
      compliance_prompt = #{record.compliancePrompt,jdbcType=VARCHAR},
      datasource = #{record.datasource,jdbcType=INTEGER},
      service_entity_name = #{record.serviceEntityName,jdbcType=VARCHAR},
      fpqc_code = #{record.fpqcCode,jdbcType=VARCHAR},
      service_confirm_time = #{record.serviceConfirmTime,jdbcType=TIMESTAMP},
      risk_tolerance_date = #{record.riskToleranceDate,jdbcType=VARCHAR},
      investor_qualified_date = #{record.investorQualifiedDate,jdbcType=VARCHAR},
      risk_hint_confirm_dtm = #{record.riskHintConfirmDtm,jdbcType=VARCHAR},
      investor_qualified_hint_confirm_dtm = #{record.investorQualifiedHintConfirmDtm,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.tms.high.batch.dao.po.order.DealOrderExtendPo">
    update deal_order_extend
    <set>
      <if test="dealNo != null">
        deal_no = #{dealNo,jdbcType=VARCHAR},
      </if>
      <if test="transactorIdNo != null">
        transactor_id_no = #{transactorIdNo,jdbcType=VARCHAR},
      </if>
      <if test="transactorIdType != null">
        transactor_id_type = #{transactorIdType,jdbcType=VARCHAR},
      </if>
      <if test="transactorName != null">
        transactor_name = #{transactorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorNo != null">
        operator_no = #{operatorNo,jdbcType=VARCHAR},
      </if>
      <if test="consCode != null">
        cons_code = #{consCode,jdbcType=VARCHAR},
      </if>
      <if test="createDtm != null">
        create_dtm = #{createDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDtm != null">
        update_dtm = #{updateDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="appointmentDealNo != null">
        appointment_deal_no = #{appointmentDealNo,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        rec_stat = #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="riskAckDtm != null">
        risk_ack_dtm = #{riskAckDtm,jdbcType=VARCHAR},
      </if>
      <if test="highRiskTipDtm != null">
        high_risk_tip_dtm = #{highRiskTipDtm,jdbcType=VARCHAR},
      </if>
      <if test="normalCustTipDtm != null">
        normal_cust_tip_dtm = #{normalCustTipDtm,jdbcType=VARCHAR},
      </if>
      <if test="portfolioFlag != null">
        portfolio_flag = #{portfolioFlag,jdbcType=VARCHAR},
      </if>
      <if test="riskRevealBookAckDtm != null">
        risk_reveal_book_ack_dtm = #{riskRevealBookAckDtm,jdbcType=VARCHAR},
      </if>
      <if test="transferTubeBusiType != null">
        transfer_tube_busi_type = #{transferTubeBusiType,jdbcType=CHAR},
      </if>
      <if test="tOutletcode != null">
        t_outletcode = #{tOutletcode,jdbcType=VARCHAR},
      </if>
      <if test="tSellerTxacctno != null">
        t_seller_txacctno = #{tSellerTxacctno,jdbcType=VARCHAR},
      </if>
      <if test="tSellerCode != null">
        t_seller_code = #{tSellerCode,jdbcType=VARCHAR},
      </if>
      <if test="originalAppDealno != null">
        original_app_dealno = #{originalAppDealno,jdbcType=VARCHAR},
      </if>
      <if test="riskFlag != null">
        risk_flag = #{riskFlag,jdbcType=VARCHAR},
      </if>
      <if test="redeemDirection != null">
        redeem_direction = #{redeemDirection,jdbcType=VARCHAR},
      </if>
      <if test="infoAckDtm != null">
        info_ack_dtm = #{infoAckDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="infoAckFileName != null">
        info_ack_file_name = #{infoAckFileName,jdbcType=VARCHAR},
      </if>
      <if test="checkParamFlag != null">
        check_param_flag = #{checkParamFlag,jdbcType=VARCHAR},
      </if>
      <if test="transactorIdNoCipher != null">
        transactor_id_no_cipher = #{transactorIdNoCipher,jdbcType=VARCHAR},
      </if>
      <if test="oprReason != null">
        opr_reason = #{oprReason,jdbcType=VARCHAR},
      </if>
      <if test="custRiskLevel != null">
        cust_risk_level = #{custRiskLevel,jdbcType=VARCHAR},
      </if>
      <if test="investAckDtm != null">
        invest_ack_dtm = #{investAckDtm,jdbcType=VARCHAR},
      </if>
      <if test="expireOpType != null">
        expire_op_type = #{expireOpType,jdbcType=CHAR},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="couponType != null">
        coupon_type = #{couponType,jdbcType=VARCHAR},
      </if>
      <if test="couponDiscountRate != null">
        coupon_discount_rate = #{couponDiscountRate,jdbcType=DECIMAL},
      </if>
      <if test="maxDeductible != null">
        max_deductible = #{maxDeductible,jdbcType=DECIMAL},
      </if>
      <if test="liveAddress != null">
        live_address = #{liveAddress,jdbcType=VARCHAR},
      </if>
      <if test="cardAddress != null">
        card_address = #{cardAddress,jdbcType=VARCHAR},
      </if>
      <if test="compliancePrompt != null">
        compliance_prompt = #{compliancePrompt,jdbcType=VARCHAR},
      </if>
      <if test="datasource != null">
        datasource = #{datasource,jdbcType=INTEGER},
      </if>
      <if test="serviceEntityName != null">
        service_entity_name = #{serviceEntityName,jdbcType=VARCHAR},
      </if>
      <if test="fpqcCode != null">
        fpqc_code = #{fpqcCode,jdbcType=VARCHAR},
      </if>
      <if test="serviceConfirmTime != null">
        service_confirm_time = #{serviceConfirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="riskToleranceDate != null">
        risk_tolerance_date = #{riskToleranceDate,jdbcType=VARCHAR},
      </if>
      <if test="investorQualifiedDate != null">
        investor_qualified_date = #{investorQualifiedDate,jdbcType=VARCHAR},
      </if>
      <if test="riskHintConfirmDtm != null">
        risk_hint_confirm_dtm = #{riskHintConfirmDtm,jdbcType=VARCHAR},
      </if>
      <if test="investorQualifiedHintConfirmDtm != null">
        investor_qualified_hint_confirm_dtm = #{investorQualifiedHintConfirmDtm,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.tms.high.batch.dao.po.order.DealOrderExtendPo">
    update deal_order_extend
    set deal_no = #{dealNo,jdbcType=VARCHAR},
      transactor_id_no = #{transactorIdNo,jdbcType=VARCHAR},
      transactor_id_type = #{transactorIdType,jdbcType=VARCHAR},
      transactor_name = #{transactorName,jdbcType=VARCHAR},
      operator_no = #{operatorNo,jdbcType=VARCHAR},
      cons_code = #{consCode,jdbcType=VARCHAR},
      create_dtm = #{createDtm,jdbcType=TIMESTAMP},
      update_dtm = #{updateDtm,jdbcType=TIMESTAMP},
      appointment_deal_no = #{appointmentDealNo,jdbcType=VARCHAR},
      rec_stat = #{recStat,jdbcType=VARCHAR},
      risk_ack_dtm = #{riskAckDtm,jdbcType=VARCHAR},
      high_risk_tip_dtm = #{highRiskTipDtm,jdbcType=VARCHAR},
      normal_cust_tip_dtm = #{normalCustTipDtm,jdbcType=VARCHAR},
      portfolio_flag = #{portfolioFlag,jdbcType=VARCHAR},
      risk_reveal_book_ack_dtm = #{riskRevealBookAckDtm,jdbcType=VARCHAR},
      transfer_tube_busi_type = #{transferTubeBusiType,jdbcType=CHAR},
      t_outletcode = #{tOutletcode,jdbcType=VARCHAR},
      t_seller_txacctno = #{tSellerTxacctno,jdbcType=VARCHAR},
      t_seller_code = #{tSellerCode,jdbcType=VARCHAR},
      original_app_dealno = #{originalAppDealno,jdbcType=VARCHAR},
      risk_flag = #{riskFlag,jdbcType=VARCHAR},
      redeem_direction = #{redeemDirection,jdbcType=VARCHAR},
      info_ack_dtm = #{infoAckDtm,jdbcType=TIMESTAMP},
      info_ack_file_name = #{infoAckFileName,jdbcType=VARCHAR},
      check_param_flag = #{checkParamFlag,jdbcType=VARCHAR},
      transactor_id_no_cipher = #{transactorIdNoCipher,jdbcType=VARCHAR},
      opr_reason = #{oprReason,jdbcType=VARCHAR},
      cust_risk_level = #{custRiskLevel,jdbcType=VARCHAR},
      invest_ack_dtm = #{investAckDtm,jdbcType=VARCHAR},
      expire_op_type = #{expireOpType,jdbcType=CHAR},
      coupon_id = #{couponId,jdbcType=VARCHAR},
      coupon_type = #{couponType,jdbcType=VARCHAR},
      coupon_discount_rate = #{couponDiscountRate,jdbcType=DECIMAL},
      max_deductible = #{maxDeductible,jdbcType=DECIMAL},
      live_address = #{liveAddress,jdbcType=VARCHAR},
      card_address = #{cardAddress,jdbcType=VARCHAR},
      compliance_prompt = #{compliancePrompt,jdbcType=VARCHAR},
      datasource = #{datasource,jdbcType=INTEGER},
      service_entity_name = #{serviceEntityName,jdbcType=VARCHAR},
      fpqc_code = #{fpqcCode,jdbcType=VARCHAR},
      service_confirm_time = #{serviceConfirmTime,jdbcType=TIMESTAMP},
      risk_tolerance_date = #{riskToleranceDate,jdbcType=VARCHAR},
      investor_qualified_date = #{investorQualifiedDate,jdbcType=VARCHAR},
      risk_hint_confirm_dtm = #{riskHintConfirmDtm,jdbcType=VARCHAR},
      investor_qualified_hint_confirm_dtm = #{investorQualifiedHintConfirmDtm,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>