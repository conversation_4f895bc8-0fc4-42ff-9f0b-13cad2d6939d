package com.howbuy.tms.high.batch.service.log;

import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.lookup.StrLookup;
import org.slf4j.MDC;
import com.howbuy.trace.RequestChainTrace;
import org.apache.commons.lang3.StringUtils;

/**
 * 自定义Log4j2 Context Lookup
 * 
 * 解决${ctx:uuid}和${ctx:ranNo}占位符问题
 * 优先级：ThreadContext > MDC > RequestChainTrace > 默认值
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Plugin(name = "ctx", category = StrLookup.CATEGORY)
public class CustomContextLookup implements StrLookup {
    
    @Override
    public String lookup(String key) {
        if ("uuid".equals(key)) {
            // 1. 优先从Log4j2 ThreadContext获取
            String uuid = org.apache.logging.log4j.ThreadContext.get("uuid");
            if (StringUtils.isNotEmpty(uuid)) {
                return uuid;
            }
            
            // 2. 其次从SLF4J MDC获取
            uuid = MDC.get("uuid");
            if (StringUtils.isNotEmpty(uuid)) {
                return uuid;
            }
            
            // 3. 最后从RequestChainTrace获取
            uuid = RequestChainTrace.getReqId();
            if (StringUtils.isNotEmpty(uuid)) {
                return uuid;
            }
            
            // 4. 如果都没有，返回默认值
            return "unknown-uuid";
            
        } else if ("ranNo".equals(key)) {
            // 1. 优先从Log4j2 ThreadContext获取
            String ranNo = org.apache.logging.log4j.ThreadContext.get("ranNo");
            if (StringUtils.isNotEmpty(ranNo)) {
                return ranNo;
            }
            
            // 2. 其次从SLF4J MDC获取
            ranNo = MDC.get("ranNo");
            if (StringUtils.isNotEmpty(ranNo)) {
                return ranNo;
            }
            
            // 3. 最后从RequestChainTrace获取
            ranNo = RequestChainTrace.getRanNo();
            if (StringUtils.isNotEmpty(ranNo)) {
                return ranNo;
            }
            
            // 4. 如果都没有，生成默认值
            return String.valueOf(System.currentTimeMillis() % 100000);
        }
        
        return null;
    }
    
    @Override
    public String lookup(LogEvent event, String key) {
        return lookup(key);
    }
}
