package com.howbuy.tms.high.batch.service.task;

import com.howbuy.tms.high.batch.service.business.finacxgfile.FinaCxgFinishCheckProcessor;
import com.howbuy.tms.high.batch.service.business.message.FinaFileMessageBean;
import com.howbuy.tms.high.batch.service.common.utils.AbstractHowbuyBaseTask;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Description:资金给中台存入OK文件校验并生成给储蓄罐ok文件
 * @Author: yun.lu
 * Date: 2025/3/25 16:15
 */
@Data
@AllArgsConstructor
public class FinaCxgFinishCheckTask extends AbstractHowbuyBaseTask {
    private String batchNo;
    private FinaFileMessageBean finaFileMessageBean;
    private FinaCxgFinishCheckProcessor finaCxgFinishCheckProcessor;

    @Override
    protected void callTask() {
        finaCxgFinishCheckProcessor.execute(batchNo, finaFileMessageBean);
    }
}
