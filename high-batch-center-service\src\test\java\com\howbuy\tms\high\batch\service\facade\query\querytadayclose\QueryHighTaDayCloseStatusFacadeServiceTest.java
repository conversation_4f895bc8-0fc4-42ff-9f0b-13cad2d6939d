/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.query.querytadayclose;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.BatchStatEnum;
import com.howbuy.tms.common.enums.database.BusinessProcessingStepEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.high.batch.dao.po.batch.BusinessBatchFlowPo;
import com.howbuy.tms.high.batch.dao.po.batch.TaBusinessBatchFlowPo;
import com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusRequest;
import com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusResponse;
import com.howbuy.tms.high.batch.service.repository.BusinessBatchFlowRepository;
import com.howbuy.tms.high.batch.service.repository.TaBusinessBatchFlowRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Description: 查询高端TA收市状态接口集成测试
 *
 * <AUTHOR>
 * @date 2025-05-16 10:56:54
 * @since JDK 1.8
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class QueryHighTaDayCloseStatusFacadeServiceTest {

    /**
     * 准备测试配置
     */
    @Configuration
    static class TestConfig {
        @Bean
        public QueryHighTaDayCloseStatusFacadeService queryHighTaDayCloseStatusFacadeService() {
            return new QueryHighTaDayCloseStatusFacadeService();
        }
    }

    @Autowired
    private QueryHighTaDayCloseStatusFacadeService facadeService;

    @MockBean
    private BusinessBatchFlowRepository businessBatchFlowRepository;

    @MockBean
    private TaBusinessBatchFlowRepository taBusinessBatchFlowRepository;

    /**
     * 测试集成场景：已收市
     */
    @Test
    public void testIntegrationAlreadyDayClose() {
        // 准备测试数据
        QueryHighTaDayCloseStatusRequest request = new QueryHighTaDayCloseStatusRequest();
        request.setTaCode("001");
        request.setWorkDay("20250516");

        // 模拟依赖
        BusinessBatchFlowPo batchFlowPo = new BusinessBatchFlowPo();
        when(businessBatchFlowRepository.selectByTaskIdAndTradeDtAndSysCode(
                eq(BusinessProcessingStepEnum.BPS_WORK_DAY_INIT.getCode()),
                eq(request.getWorkDay()),
                eq(SysCodeEnum.BATCH_HIGH.getCode()))).thenReturn(batchFlowPo);

        TaBusinessBatchFlowPo taBusinessBatchFlowPo = new TaBusinessBatchFlowPo();
        taBusinessBatchFlowPo.setFlowStat(BatchStatEnum.PROCESS_SUCCESS.getKey());
        when(taBusinessBatchFlowRepository.selectByTaCodeAndSysCodeAndTradeDtAndTaskId(
                eq(request.getTaCode()),
                eq(SysCodeEnum.BATCH_HIGH.getCode()),
                eq(request.getWorkDay()),
                eq(BusinessProcessingStepEnum.BPS_COUNTER_DAY_CLOSE.getCode()))).thenReturn(taBusinessBatchFlowPo);

        // 执行测试
        QueryHighTaDayCloseStatusResponse response = facadeService.execute(request);

        // 验证结果
        assertEquals(ExceptionCodes.SUCCESS, response.getReturnCode());
        assertEquals("0", response.getDayCloseStatus());
    }

    /**
     * 测试集成场景：未收市
     */
    @Test
    public void testIntegrationNotDayClose() {
        // 准备测试数据
        QueryHighTaDayCloseStatusRequest request = new QueryHighTaDayCloseStatusRequest();
        request.setTaCode("001");
        request.setWorkDay("20250516");

        // 模拟依赖
        BusinessBatchFlowPo batchFlowPo = new BusinessBatchFlowPo();
        when(businessBatchFlowRepository.selectByTaskIdAndTradeDtAndSysCode(
                eq(BusinessProcessingStepEnum.BPS_WORK_DAY_INIT.getCode()),
                eq(request.getWorkDay()),
                eq(SysCodeEnum.BATCH_HIGH.getCode()))).thenReturn(batchFlowPo);

        TaBusinessBatchFlowPo taBusinessBatchFlowPo = new TaBusinessBatchFlowPo();
        taBusinessBatchFlowPo.setFlowStat(BatchStatEnum.PROCESS_FAIL.getKey()); // 非成功状态
        when(taBusinessBatchFlowRepository.selectByTaCodeAndSysCodeAndTradeDtAndTaskId(
                eq(request.getTaCode()),
                eq(SysCodeEnum.BATCH_HIGH.getCode()),
                eq(request.getWorkDay()),
                eq(BusinessProcessingStepEnum.BPS_COUNTER_DAY_CLOSE.getCode()))).thenReturn(taBusinessBatchFlowPo);

        // 执行测试
        QueryHighTaDayCloseStatusResponse response = facadeService.execute(request);

        // 验证结果
        assertEquals(ExceptionCodes.SUCCESS, response.getReturnCode());
        assertEquals("1", response.getDayCloseStatus());
    }

    /**
     * 测试集成场景：不支持的TA
     */
    @Test
    public void testIntegrationNotSupportedTa() {
        // 准备测试数据
        QueryHighTaDayCloseStatusRequest request = new QueryHighTaDayCloseStatusRequest();
        request.setTaCode("001");
        request.setWorkDay("20250516");

        // 模拟依赖
        BusinessBatchFlowPo batchFlowPo = new BusinessBatchFlowPo();
        when(businessBatchFlowRepository.selectByTaskIdAndTradeDtAndSysCode(
                eq(BusinessProcessingStepEnum.BPS_WORK_DAY_INIT.getCode()),
                eq(request.getWorkDay()),
                eq(SysCodeEnum.BATCH_HIGH.getCode()))).thenReturn(batchFlowPo);

        when(taBusinessBatchFlowRepository.selectByTaCodeAndSysCodeAndTradeDtAndTaskId(
                eq(request.getTaCode()),
                eq(SysCodeEnum.BATCH_HIGH.getCode()),
                eq(request.getWorkDay()),
                eq(BusinessProcessingStepEnum.BPS_COUNTER_DAY_CLOSE.getCode()))).thenReturn(null);

        // 执行测试
        QueryHighTaDayCloseStatusResponse response = facadeService.execute(request);

        // 验证结果
        assertEquals(ExceptionCodes.SUCCESS, response.getReturnCode());
        assertEquals("2", response.getDayCloseStatus());
    }
} 