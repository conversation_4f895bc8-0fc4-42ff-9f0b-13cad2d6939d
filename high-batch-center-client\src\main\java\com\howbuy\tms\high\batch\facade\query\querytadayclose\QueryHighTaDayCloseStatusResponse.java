/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.facade.query.querytadayclose;


import com.howbuy.tms.high.batch.facade.common.BatchBaseResponse;
import lombok.Getter;
import lombok.Setter;

/**
 * Description:查询高端TA收市状态响应类
 *
 * <AUTHOR>
 * @date 2025-05-15 15:35:23
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryHighTaDayCloseStatusResponse extends BatchBaseResponse {

    private static final long serialVersionUID = 1L;

    /**
     * 日终状态
     * 0-已收市，1-未收市，2-不支持TA
     */
    private String dayCloseStatus;
} 