<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!-- 应用名称 -->
    <dubbo:application name="high-batch-center-test-client"/>
    
    <!-- 注册中心 -->
    <dubbo:registry protocol="zookeeper" address="zookeeper:2181"/>
    
    <!-- 消费者默认配置 -->
    <dubbo:consumer timeout="30000" retries="0"/>
    
    <!-- 引用服务 -->
    <dubbo:reference id="queryHighTaDayCloseStatusFacade" 
                     interface="com.howbuy.tms.high.batch.facade.query.querytadayclose.QueryHighTaDayCloseStatusFacade"
                     check="false"/>

</beans> 