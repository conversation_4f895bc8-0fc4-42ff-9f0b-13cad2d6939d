package com.howbuy.tms.high.batch.service.business.tradecheck;

import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.service.common.utils.AbstractHowbuyBaseTask;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * @Description:交易对账
 * @Author: yun.lu
 * Date: 2025/6/26 14:09
 */
public class TradeCheckTask extends AbstractHowbuyBaseTask {
    private static final Logger logger = LogManager.getLogger(TradeCheckTask.class);

    private List<SimuFundCheckOrderPo> orderList;
    private List<TradeCheckDetailBean> result;
    private DoTradeCheckService doTradeCheckService;

    @Override
    protected void callTask() {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        for (SimuFundCheckOrderPo simuFundCheckOrderPo : orderList) {
            TradeCheckDetailBean bean = doTradeCheckService.check(simuFundCheckOrderPo);
            if (bean != null) {
                result.add(bean);
            }
        }

    }

    public List<SimuFundCheckOrderPo> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<SimuFundCheckOrderPo> orderList) {
        this.orderList = orderList;
    }

    public List<TradeCheckDetailBean> getResult() {
        return result;
    }

    public void setResult(List<TradeCheckDetailBean> result) {
        this.result = result;
    }

    public DoTradeCheckService getDoTradeCheckService() {
        return doTradeCheckService;
    }

    public void setDoTradeCheckService(DoTradeCheckService doTradeCheckService) {
        this.doTradeCheckService = doTradeCheckService;
    }

    public TradeCheckTask(List<SimuFundCheckOrderPo> orderList, List<TradeCheckDetailBean> result, DoTradeCheckService doTradeCheckService) {
        this.orderList = orderList;
        this.result = result;
        this.doTradeCheckService = doTradeCheckService;
    }
}
