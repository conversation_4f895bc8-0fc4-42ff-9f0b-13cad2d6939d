package com.howbuy.tms.high.batch.service.business.onwaymonitor;

import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.high.batch.service.business.BatchMessageProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @api {MQ} high_onway_monitor_queue
 * @apiGroup schedule
 * @apiName 在途金额异常监控
 * @apiDescription 在途金额异常监控
 */
@Service
public class OnWayMonitorProcessor extends BatchMessageProcessor {

    private static final Logger logger = LoggerFactory.getLogger(OnWayMonitorProcessor.class);

    @Value("${high.onway.monitor.queue}")
    private String onWayMonitorQueue;

    @Autowired
    private OnWayMonitorService onWayMonitorService;

    @Override
    protected String getQuartMessageChannel() {
        return onWayMonitorQueue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        logger.info("OnWayMonitorProcessor|doProcessMessage|start");
        try {
            onWayMonitorService.process();
        } catch (Throwable e) {
            logger.error("OnWayMonitorProcessor|doProcessMessage|error:", e);
        }
        logger.info("OnWayMonitorProcessor|doProcessMessage|end");
    }
}
