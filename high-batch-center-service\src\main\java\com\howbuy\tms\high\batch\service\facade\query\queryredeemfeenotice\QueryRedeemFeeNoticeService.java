package com.howbuy.tms.high.batch.service.facade.query.queryredeemfeenotice;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.SubmitAppFlagEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.high.batch.dao.vo.QueryRedeemFeeNoticeParamVo;
import com.howbuy.tms.high.batch.dao.vo.RedeemNoticeOrderVo;
import com.howbuy.tms.high.batch.facade.query.queryredeemfeenotice.QueryRedeemFeeNoticeFacade;
import com.howbuy.tms.high.batch.facade.query.queryredeemfeenotice.QueryRedeemFeeNoticeRequest;
import com.howbuy.tms.high.batch.facade.query.queryredeemfeenotice.QueryRedeemFeeNoticeResponse;
import com.howbuy.tms.high.batch.facade.query.queryredeemfeenotice.bean.RedeemFeeNoticeDto;
import com.howbuy.tms.high.batch.service.repository.RedeemFeeNoticeRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:查询赎回费告警接口
 * @Author: yun.lu
 * Date: 2025/6/10 17:09
 */
@DubboService
@Service("queryRedeemFeeNoticeFacade")
@Slf4j
public class QueryRedeemFeeNoticeService implements QueryRedeemFeeNoticeFacade {
    @Autowired
    private RedeemFeeNoticeRepository redeemFeeNoticeRepository;

    @Override
    public QueryRedeemFeeNoticeResponse execute(QueryRedeemFeeNoticeRequest queryRedeemFeeNoticeRequest) {
        log.info("查询赎回费告警接口-queryRedeemFeeNoticeRequest={}", JSON.toJSONString(queryRedeemFeeNoticeRequest));
        // 1.参数校验
        if (StringUtils.isBlank(queryRedeemFeeNoticeRequest.getQueryStartDt()) || StringUtils.isBlank(queryRedeemFeeNoticeRequest.getQueryEndDt())) {
            throw new BusinessException(ExceptionCodes.BATCH_CENTER_PARAM_ERROR, "查询日期区间不能为空");
        }
        // 2.分页查询数据
        QueryRedeemFeeNoticeParamVo queryRedeemFeeNoticeParamVo = buildQueryParam(queryRedeemFeeNoticeRequest);
        Page<RedeemNoticeOrderVo> pageResult = redeemFeeNoticeRepository.queryRedeemFeeNoticeWithPage(queryRedeemFeeNoticeParamVo);
        QueryRedeemFeeNoticeResponse queryRedeemFeeNoticeResponse = new QueryRedeemFeeNoticeResponse();
        queryRedeemFeeNoticeResponse.setReturnCode(ExceptionCodes.SUCCESS);
        queryRedeemFeeNoticeResponse.setTotalCount(pageResult.getTotal());
        queryRedeemFeeNoticeResponse.setPageNo(pageResult.getPageNum());
        queryRedeemFeeNoticeResponse.setTotalPage(pageResult.getPages());
        if (CollectionUtils.isNotEmpty(pageResult.getResult())) {
            List<RedeemFeeNoticeDto> redeemFeeNoticeDtoList = new ArrayList<>(pageResult.getResult().size());
            for (RedeemNoticeOrderVo redeemNoticeOrderVo : pageResult.getResult()) {
                RedeemFeeNoticeDto redeemFeeNoticeDto = new RedeemFeeNoticeDto();
                redeemFeeNoticeDto.setFundCode(redeemNoticeOrderVo.getFundCode());
                redeemFeeNoticeDto.setFundName(redeemNoticeOrderVo.getFundName());
                redeemFeeNoticeDto.setDataDt(redeemNoticeOrderVo.getDataDt());
                redeemFeeNoticeDto.setDealNo(redeemNoticeOrderVo.getDealNo());
                redeemFeeNoticeDto.setTxAcctNo(redeemNoticeOrderVo.getTxAcctNo());
                redeemFeeNoticeDto.setCustName(redeemNoticeOrderVo.getCustName());
                redeemFeeNoticeDto.setMBusiCode(redeemNoticeOrderVo.getMBusiCode());
                redeemFeeNoticeDto.setIsCycleProduct(redeemNoticeOrderVo.getIsCycleProduct());
                redeemFeeNoticeDto.setApplyVol(redeemNoticeOrderVo.getApplyVol());
                redeemFeeNoticeDto.setTradeDt(redeemNoticeOrderVo.getTradeDt());
                redeemFeeNoticeDto.setTradeTm(redeemNoticeOrderVo.getTradeTm());
                redeemFeeNoticeDto.setEstimateDataSource(redeemNoticeOrderVo.getEstimateDataSource());
                redeemFeeNoticeDto.setIsMultiCard(redeemNoticeOrderVo.getIsMultiCard());
                redeemFeeNoticeDto.setCurrentPeriodOpenDt(redeemNoticeOrderVo.getCurrentPeriodOpenDt());
                redeemFeeNoticeDto.setCurrentPeriodDetail(redeemNoticeOrderVo.getCurrentPeriodDetail());
                redeemFeeNoticeDto.setNextPeriodOpenDt(redeemNoticeOrderVo.getNextPeriodOpenDt());
                redeemFeeNoticeDto.setNextPeriodDetail(redeemNoticeOrderVo.getNextPeriodDetail());
                redeemFeeNoticeDto.setNoticeStatus(redeemNoticeOrderVo.getNoticeStatus());
                redeemFeeNoticeDto.setSubmitAppFlag(StringUtils.isBlank(redeemNoticeOrderVo.getSubmitAppFlag()) ? SubmitAppFlagEnum.NO_NEED_SUBMITTING.getCode() : redeemNoticeOrderVo.getSubmitAppFlag());
                redeemFeeNoticeDtoList.add(redeemFeeNoticeDto);
            }
            queryRedeemFeeNoticeResponse.setRedeemFeeNoticeDtoList(redeemFeeNoticeDtoList);
        }
        return queryRedeemFeeNoticeResponse;
    }

    /**
     * 构建查询入参
     */
    private QueryRedeemFeeNoticeParamVo buildQueryParam(QueryRedeemFeeNoticeRequest queryRedeemFeeNoticeRequest) {
        QueryRedeemFeeNoticeParamVo queryRedeemFeeNoticeParamVo = new QueryRedeemFeeNoticeParamVo();
        queryRedeemFeeNoticeParamVo.setQueryStartDt(queryRedeemFeeNoticeRequest.getQueryStartDt());
        queryRedeemFeeNoticeParamVo.setQueryEndDt(queryRedeemFeeNoticeRequest.getQueryEndDt());
        queryRedeemFeeNoticeParamVo.setFundCodeList(queryRedeemFeeNoticeRequest.getFundCodeList());
        queryRedeemFeeNoticeParamVo.setSubmitAppFlagList(queryRedeemFeeNoticeRequest.getSubmitAppFlagList());
        queryRedeemFeeNoticeParamVo.setPageNo(queryRedeemFeeNoticeRequest.getPageNo());
        queryRedeemFeeNoticeParamVo.setPageSize(queryRedeemFeeNoticeRequest.getPageSize());
        // 检查是否需要查询submit_app_flag为null的记录
        if (CollectionUtils.isNotEmpty(queryRedeemFeeNoticeRequest.getSubmitAppFlagList()) &&
                queryRedeemFeeNoticeRequest.getSubmitAppFlagList().contains(SubmitAppFlagEnum.NO_NEED_SUBMITTING.getCode())) {
            queryRedeemFeeNoticeParamVo.setNeedQueryWithOutSubmitAppFlag(YesOrNoEnum.YES.getCode());
        } else {
            queryRedeemFeeNoticeParamVo.setNeedQueryWithOutSubmitAppFlag(YesOrNoEnum.NO.getCode());
        }
        return queryRedeemFeeNoticeParamVo;
    }
}
