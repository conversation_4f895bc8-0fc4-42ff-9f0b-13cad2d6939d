/**
 * Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.query.querytradereportcheck;

import com.howbuy.interlayer.product.enums.YesOrNoEnum;
import com.howbuy.interlayer.product.model.HighProductControlModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.IdTypeIndiAndInstEnum;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.crm.nt.consultant.QueryConsInfoOuterService;
import com.howbuy.tms.common.outerservice.crm.nt.consultant.QueryConsInfoResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.vo.HighTradeReportCheckVo;
import com.howbuy.tms.high.batch.facade.query.querytradereportcheck.QueryTradeReportCheckFacade;
import com.howbuy.tms.high.batch.facade.query.querytradereportcheck.QueryTradeReportCheckRequest;
import com.howbuy.tms.high.batch.facade.query.querytradereportcheck.QueryTradeReportCheckResponse;
import com.howbuy.tms.high.batch.facade.query.querytradereportcheck.bean.TradeReportCheckBean;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description:(交易合规上报检查报表)
 * @date 2018年6月19日 下午3:08:48
 * @since JDK 1.7
 */
@DubboService
@Service("queryTradeReportCheckFacade")
public class QueryTradeReportCheckFacadeService implements QueryTradeReportCheckFacade {

    private static Logger logger = LogManager.getLogger(QueryTradeReportCheckFacadeService.class);
    // 千分位金额转换
    private static final String ZERO = "0.00";

    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private HighProductService highProductService;
    @Autowired
    private QueryConsInfoOuterService queryConsInfoOuterService;
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    @Override
    public QueryTradeReportCheckResponse execute(QueryTradeReportCheckRequest request) {
        QueryTradeReportCheckResponse response = new QueryTradeReportCheckResponse();
        // 入参校验
        if (StringUtils.isNotBlank(request.getSubmitTaDtBegin()) && StringUtils.isBlank(request.getSubmitTaDtEnd())) {
            response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
            response.setDescription("上报日开始日与结束日必须同时有值,或者同时没有值");
            return response;
        } else if (StringUtils.isBlank(request.getSubmitTaDtBegin()) && StringUtils.isNotBlank(request.getSubmitTaDtEnd())) {
            response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
            response.setDescription("报日开始日与结束日必须同时有值,或者同时没有值");
            return response;
        }
        // 获取list数据
        List<HighTradeReportCheckVo> voList = getPoList(request);
        BigDecimal appAmtCount = new BigDecimal("0.00");
        BigDecimal feeCount = new BigDecimal("0.00");
        List<TradeReportCheckBean> beanList = new ArrayList<>();
        // vo转换bean并统计
        vo2Bean(voList, beanList, appAmtCount, feeCount);
        // 设置投顾信息
        setConsInfo(beanList);

        response.setBeanList(beanList);
        response.setAppAmtCount(appAmtCount);
        response.setFeeCount(feeCount);
        response.setAppNetAmtCount(appAmtCount.subtract(feeCount));
        return response;
    }

    /**
     * 设置投顾信息
     *
     * @param beanList
     * @return void
     * @author: huaqiang.liu
     * @date: 2020/11/4 11:32
     * @since JDK 1.8
     */
    private void setConsInfo(List<TradeReportCheckBean> beanList) {
        Map<String, QueryConsInfoResult> consInfoMap = new HashMap<>();
        beanList.forEach(bean -> {
            String txAcctNo = bean.getTxAcctNo();
            // 查询投顾信息
            QueryConsInfoResult consInfo = consInfoMap.get(txAcctNo);
            if (consInfo == null) {
                String hboneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(txAcctNo);
                if (StringUtils.isBlank(hboneNo)) {
                    logger.error("QueryTradeReportCheckFacadeService|setConsInfo|queryHbOneNoByTxAcctNo return null, txAcctNo:{}", txAcctNo);
                    return;
                }
                consInfo = queryConsInfoOuterService.queryConsInfo(hboneNo);
                if (consInfo == null) {
                    logger.error("QueryTradeReportCheckFacadeService|setConsInfo|queryConsInfo return null, txAcctNo:{}, hboneNo:{}", txAcctNo, hboneNo);
                    return;
                } else {
                    consInfoMap.put(txAcctNo, consInfo);
                }
            }
            // 设置投顾信息
            bean.setConsName(consInfo.getConsName());
            bean.setOrgName(consInfo.getOrgName());
        });
    }

    private void vo2Bean(List<HighTradeReportCheckVo> voList, List<TradeReportCheckBean> beanList, BigDecimal appAmtCount, BigDecimal feeCount) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        for (HighTradeReportCheckVo vo : voList) {
            TradeReportCheckBean bean = new TradeReportCheckBean();
            bean.setDealNo(vo.getDealNo());
            bean.setAppDt(vo.getAppDt());
            bean.setAppTime(vo.getAppTime());
            bean.setFundName(vo.getFundName());
            bean.setFundManInfo(vo.getFundManInfo());
            bean.setTaInfo(vo.getTaInfo());
            bean.setBusiName(BusinessCodeEnum.getName(vo.getmBusiCode()));
            bean.setTxAcctNo(vo.getTxAcctNo());
            bean.setCustName(vo.getCustName());
            bean.setIdType(IdTypeIndiAndInstEnum.getValue(vo.getInvestType() + vo.getIdType()));
            bean.setIdNo(vo.getIdNo());
            bean.setFundName(vo.getFundName());
            if (vo.getAppAmt() == null) {
                bean.setAppAmt(new BigDecimal(ZERO));
            } else {
                bean.setAppAmt(vo.getAppAmt());
            }
            if (vo.getFee() == null) {
                bean.setFee(new BigDecimal(ZERO));
            } else {
                bean.setFee(vo.getFee());
            }
            bean.setAppNetAmt(bean.getAppAmt().subtract(bean.getFee()));
            bean.setSubmitTaDt(vo.getSubmitTaDt());
            bean.setCalmDtm(DateUtils.formatToString(vo.getCalmDtm(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            bean.setCalmFlag(InterposeFlagEnum.getNameByCode(vo.getCalmFlag()));
            bean.setCallBackStatus(CallbackStatusEnum.getName(vo.getCallBackStatus()));
            bean.setCallBackFlag(InterposeFlagEnum.getNameByCode(vo.getCallBackFlag()));
            bean.setDualEntryStatus(DualentryStatusEnum.getNameByCode(vo.getDualEntryStatus()));
            bean.setDualEntryFlag(InterposeFlagEnum.getNameByCode(vo.getDualEntryFlag()));
            bean.setAssetStatus(AssetcertificateStatusEnum.getNameByCode(vo.getAssetStatus()));
            bean.setAssetFlag(InterposeFlagEnum.getNameByCode(vo.getAssetFlag()));
            bean.setCallBackDtm(DateUtils.formatToString(vo.getCallBackDtm(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            beanList.add(bean);
            appAmtCount = appAmtCount.add(vo.getAppAmt() == null ? new BigDecimal("0.00") : vo.getAppAmt());
            feeCount = feeCount.add(vo.getFee() == null ? new BigDecimal("0.00") : vo.getFee());
        }
    }

    private List<HighTradeReportCheckVo> getPoList(QueryTradeReportCheckRequest request) {
        List<HighTradeReportCheckVo> poList = null;
        String submitTaStartDt = request.getSubmitTaDtBegin();
        String submitTaEndDt = request.getSubmitTaDtEnd();
        String payDeadLineDt = request.getPayDeadlineDt();

        String fundCode = request.getFundCode();
        List<String> fundCodeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(request.getFundCodeList())) {
            fundCodeList.addAll(request.getFundCodeList());
        }
        if (StringUtils.isNotEmpty(fundCode)) {
            fundCodeList.add(fundCode);
        }

        String txAcctNo = request.getTxAcctNo();
        String taCode = request.getTaCode();
        Date date = new Date();

        // 查询该渠道下所有产品的控制信息获取上报日是否控制冷静期资产证明回访状态
        Map<String, HighProductControlModel> map = new HashMap<>();
        // 专户
        Map<String, HighProductControlModel> hfMap = queryHighProductOuterService.getAllMapHighProductControl(ProductChannelEnum.HIGH_FUND.getCode());
        // 私募
        Map<String, HighProductControlModel> tpMap = queryHighProductOuterService.getAllMapHighProductControl(ProductChannelEnum.TP_SM.getCode());

        if (MapUtils.isNotEmpty(hfMap)) {
            map.putAll(hfMap);
        }
        if (MapUtils.isNotEmpty(tpMap)) {
            map.putAll(tpMap);
        }
        // 获取上报日所在开放日历区间的产品列表
        if (StringUtils.isNotBlank(submitTaStartDt) && StringUtils.isNotBlank(submitTaEndDt)) {
            fundCodeList = getFundCodeBySubmitDt(submitTaStartDt, submitTaEndDt, fundCodeList);
            if (CollectionUtils.isEmpty(fundCodeList)) {
                return new ArrayList<>();
            }
        }
        if ("1".equals(request.getReportType())) {
            poList = getNotPassCalm(null, null, payDeadLineDt, fundCodeList, txAcctNo, date, map, taCode);
        } else if ("2".equals(request.getReportType())) {
            poList = getNotDualentry(null, null, payDeadLineDt, fundCodeList, txAcctNo, taCode);
        } else if ("3".equals(request.getReportType())) {
            poList = getNotCallback(null, null, payDeadLineDt, fundCodeList, txAcctNo, map, taCode);
        } else if ("4".equals(request.getReportType())) {
            poList = getInvalidAssetcertificate(null, null, payDeadLineDt, fundCodeList, txAcctNo, map, taCode);
        }
        // 募集期的产品无需在合规报表中展示出来。当前工作日没到打款截止日，无需展示出来。
        if (!CollectionUtils.isEmpty(poList)) {
            List<HighTradeReportCheckVo> newList = new ArrayList<>();
            for (HighTradeReportCheckVo highTradeReportCheckVo : poList) {
                if (BusinessCodeEnum.SUBS.getMCode().equals(highTradeReportCheckVo.getmBusiCode())) {
                    String payDeadDt = DateUtils.formatToString(highTradeReportCheckVo.getPayDeadlineDtm(), DateUtils.YYYYMMDD);
                    String nowDt = DateUtils.formatToString(date, DateUtils.YYYYMMDD);
                    if (nowDt.equals(payDeadDt)) {
                        newList.add(highTradeReportCheckVo);
                    }
                } else {
                    newList.add(highTradeReportCheckVo);
                }
            }
            poList = newList;
        }
        return poList;
    }

    private List<String> getFundCodeBySubmitDt(String submitTaStartDt, String submitTaEndDt, List<String> fundCodeList) {
        return highProductService.getAppointFundCodeByOpenDt(submitTaStartDt, submitTaEndDt, fundCodeList);
    }

    private List<HighTradeReportCheckVo> getNotPassCalm(String submitTaStartDt, String submitTaEndDt, String payDeadLineDt, List<String> fundCodeList, String txAcctNo,
                                                        Date date, Map<String, HighProductControlModel> map, String taCode) {
        List<HighTradeReportCheckVo> poList = new ArrayList<>();
        List<HighTradeReportCheckVo> list = highDealOrderDtlRepository.getNotPassCalmForReport(submitTaStartDt, submitTaEndDt, payDeadLineDt, fundCodeList, txAcctNo,
                date, taCode);
        if (!CollectionUtils.isEmpty(list)) {
            for (HighTradeReportCheckVo po : list) {
                if (MapUtils.isNotEmpty(map)) {
                    HighProductControlModel model = map.get(po.getFundCode());
                    if (model != null && YesOrNoEnum.NO.getCode().equals(model.getCalmOverFlag())) {
                        continue;
                    }
                }
                poList.add(po);
            }
        }
        return poList;
    }

    private List<HighTradeReportCheckVo> getInvalidAssetcertificate(String submitTaStartDt, String submitTaEndDt, String payDeadLineDt, List<String> fundCodeList,
                                                                    String txAcctNo, Map<String, HighProductControlModel> map, String taCode) {
        List<HighTradeReportCheckVo> poList = new ArrayList<>();
        List<HighTradeReportCheckVo> list = highDealOrderDtlRepository.getInvalidAssetcertificateForReport(submitTaStartDt, submitTaEndDt, payDeadLineDt, fundCodeList,
                txAcctNo, taCode, null);
        if (!CollectionUtils.isEmpty(list)) {
            for (HighTradeReportCheckVo po : list) {
                if (MapUtils.isNotEmpty(map)) {
                    HighProductControlModel model = map.get(po.getFundCode());
                    if (model != null && YesOrNoEnum.NO.getCode().equals(model.getAssetProvFlag())) {
                        continue;
                    }
                }
                poList.add(po);
            }
        }
        return poList;
    }

    private List<HighTradeReportCheckVo> getNotDualentry(String submitTaStartDt, String submitTaEndDt, String payDeadLineDt, List<String> fundCodeList, String txAcctNo, String taCode) {
        return highDealOrderDtlRepository.getNotDualentryForReport(submitTaStartDt, submitTaEndDt, payDeadLineDt, fundCodeList, txAcctNo, taCode, null);
    }

    private List<HighTradeReportCheckVo> getNotCallback(String submitTaStartDt, String submitTaEndDt, String payDeadLineDt, List<String> fundCodeList, String txAcctNo,
                                                        Map<String, HighProductControlModel> map, String taCode) {
        List<HighTradeReportCheckVo> poList = new ArrayList<>();
        List<HighTradeReportCheckVo> list = highDealOrderDtlRepository.getNotCallbackForReport(submitTaStartDt, submitTaEndDt, payDeadLineDt, fundCodeList, txAcctNo,
                taCode);
        if (!CollectionUtils.isEmpty(list)) {
            for (HighTradeReportCheckVo po : list) {
                if (MapUtils.isNotEmpty(map)) {
                    HighProductControlModel model = map.get(po.getFundCode());
                    if (model != null && YesOrNoEnum.NO.getCode().equals(model.getVisitForceFlag())) {
                        continue;
                    }
                }
                poList.add(po);
            }
        }
        return poList;
    }
}
