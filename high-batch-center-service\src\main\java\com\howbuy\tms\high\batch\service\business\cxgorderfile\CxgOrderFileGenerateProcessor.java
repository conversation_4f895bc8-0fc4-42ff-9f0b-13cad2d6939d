/**
 * Copyright (c) 2018, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.cxgorderfile;

import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.tms.cache.service.lock.LockService;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.PurchaseDirectionEnum;
import com.howbuy.tms.common.enums.busi.BusiTypeEnum;
import com.howbuy.tms.common.enums.busi.CxgFileTypeEnum;
import com.howbuy.tms.common.enums.busi.FinaFileTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.ProcessStatusEnum;
import com.howbuy.tms.common.enums.database.ProductTypeEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.enums.database.ZDtlBusiCodeEnum;
import com.howbuy.tms.common.outerservice.ftxonlinesearch.batchpiggypur.BatchPiggyPurContext;
import com.howbuy.tms.common.outerservice.ftxonlinesearch.batchpiggypur.BatchPiggyPurOuterService;
import com.howbuy.tms.high.batch.dao.po.batch.FinaFileProcessRecPo;
import com.howbuy.tms.high.batch.dao.po.batch.HighCxgSaveFileRecPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.batch.facade.enums.FileOpStatusEnum;
import com.howbuy.tms.high.batch.service.common.OpsSysMonitor;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.repository.FinaFileProcessRecRepository;
import com.howbuy.tms.high.batch.service.repository.HighCxgSaveFileRecRepository;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.repository.SimuFundCheckOrderRepository;
import com.howbuy.tms.high.batch.service.service.file.fileexport.txt.bean.TxtFileExportContext;
import com.howbuy.tms.high.batch.service.service.file.fileexport.txt.impl.ExportCxgOrderFileService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description:生成下发储蓄罐存入文件
 * @date 2019年7月9日 下午4:09:10
 * @since JDK 1.6
 */
@Service("cxgOrderFileGenerateProcessor")
public class CxgOrderFileGenerateProcessor {

    private static final Logger logger = LogManager.getLogger(CxgOrderFileGenerateProcessor.class);

    protected static final CacheService CACHE_SERVICE = CacheServiceImpl.getInstance();

    private static final String SEPARATOR = "|";
    @Autowired
    private HighCxgSaveFileRecRepository highCxgSaveFileRecRepository;
    @Autowired
    private BatchPiggyPurOuterService batchPiggyPurOuterService;
    @Autowired
    private FinaFileProcessRecRepository finaFileProcessRecRepository;
    @Autowired
    private SimuFundCheckOrderRepository simuFundCheckOrderRepository;
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private LockService lockService;
    @Autowired
    private ExportCxgOrderFileService exportCxgOrderFileService;

    private static String CXG_ORDER_FILE_GEN = "CXG_ORDER_FILE_GEN_";

    public void execute(String taTradeDt, String batchNo) {
        logger.info("CxgOrderFileGenerateProcessor|execute|batchNo:{} start", batchNo);
        if (StringUtils.isEmpty(batchNo)) {
            return;
        }
        List<String> recordNos = new ArrayList<>();
        recordNos.add(batchNo);
        String processStatus = ProcessStatusEnum.PROCESS_SUCCESS.getCode();
        String uniqKey = CacheKeyPrefix.HIGH_LOCK_CONCURRENT_PREFIX + CXG_ORDER_FILE_GEN + batchNo;
        boolean lockFlag = lockService.getLock(uniqKey, 100);
        if (!lockFlag) {
            logger.error("CxgOrderFileGenerateProcessor|execute|get lockflag err");
            return;
        }
        try {
            int updateFileCount = finaFileProcessRecRepository.updateByRecordNos(recordNos, ProcessStatusEnum.PROCESSING.getCode(), new Date(), ProcessStatusEnum.NOT_PROCESS.getCode());
            logger.info("CxgOrderFileGenerateProcessor|execute|updateFileCount:{}", updateFileCount);
            if (updateFileCount != 1) {
                return;
            }
            // 查询数据
            List<HighCxgSaveFileRecPo> recList = highCxgSaveFileRecRepository.selectByBatchNo(batchNo);
            logger.info("CxgOrderFileGenerateProcessor|execute|recList.size:{}", recList.size());
            if (CollectionUtils.isEmpty(recList)) {
                return;
            }
            String productType = recList.get(0).getProductType();
            if (StringUtils.isEmpty(productType)) {
                logger.error("CxgOrderFileGenerateProcessor|execute|batchNo:{},productType is empty", batchNo);
                throw new Exception(ExceptionCodes.HIGH_BATCH_CENTER_FINA_FILE_FAIL);
            }
            // 生成文件
            String filePath = generateNewFile(taTradeDt, batchNo, recList);
            // 记录文件表
            FinaFileProcessRecPo filePo = buildFinaFileRecPo(filePath, taTradeDt);
            if (filePo == null) {
                logger.error("储蓄罐存入文件生成失败,taTradeDt={},batchNo={}", taTradeDt, batchNo);
                return;
            }
            saveFinaFileProcessRec(filePo);
            // 调用储蓄罐批量存入
            String piggyBatchId = batchPiggyPurOuterService.doBatchPiggyPur(buildContext(filePath, productType, taTradeDt));
            if (StringUtils.isEmpty(piggyBatchId)) {
                String msg = "储蓄罐存入文件生成成功,但通知储蓄罐失败,filePath:" + filePath + ",productType:" + productType + ",taTradeDt:" + taTradeDt;
                OpsSysMonitor.warn(msg, OpsSysMonitor.ERROR);
            }
            logger.info("CxgOrderFileGenerateProcessor|execute|batchNo:{},piggyBatchId:{}", batchNo, piggyBatchId);
            // 更新文件表
            updateFinaFileProcessRec(filePo);
            // 更新数据处理状态为已处理
            int count = highCxgSaveFileRecRepository.updateDataStatByBatchNo(batchNo, YesOrNoEnum.YES.getCode());
            logger.info("CxgOrderFileGenerateProcessor|execute|updateDataStatByBatchNo|batchNo:{},count:{}", batchNo, count);
        } catch (Exception e) {
            processStatus = ProcessStatusEnum.PROCESS_FAIL.getCode();
            logger.error("CxgOrderFileGenerateProcessor|execute|err:{}", e.getMessage(), e);
            String msg = String.format("高端资金文件处理失败批次号:%s", batchNo);
            OpsSysMonitor.warn(msg, OpsSysMonitor.ERROR);
        } finally {
            // 更新文件处理状态
            finaFileProcessRecRepository.updateByRecordNos(recordNos, processStatus, new Date(), ProcessStatusEnum.PROCESSING.getCode());
            lockService.releaseLock(uniqKey);
        }
        logger.info("CxgOrderFileGenerateProcessor|execute|batchNo:{} end", batchNo);
    }

    private String generateNewFile(String tradeDt, String batchNo, List<HighCxgSaveFileRecPo> recList) {
        try {
            TxtFileExportContext txtFileExportContext = new TxtFileExportContext();
            txtFileExportContext.setRelationPath(getPath(tradeDt));
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("batchNo", batchNo);
            paramMap.put("tradeDt", tradeDt);
            BigDecimal amount = BigDecimal.ZERO;
            int totalCount = 0;
            if (!CollectionUtils.isEmpty(recList)) {
                for (HighCxgSaveFileRecPo po : recList) {
                    amount = amount.add(new BigDecimal(po.getApplyAmt()));
                }
                totalCount = recList.size();
            }
            paramMap.put("amount", amount);
            paramMap.put("totalCount", totalCount);
            txtFileExportContext.setParams(paramMap);
            txtFileExportContext.setFileName(getFileName(tradeDt));
            return exportCxgOrderFileService.process(txtFileExportContext);
        } catch (Exception e) {
            logger.info("储蓄罐下发存入文件生成异常:", e);
        }
        return null;
    }

    private FinaFileProcessRecPo buildFinaFileRecPo(String filePath, String taTradeDt) {
        if (StringUtils.isEmpty(filePath)) {
            return null;
        }
        FinaFileProcessRecPo filePo = new FinaFileProcessRecPo();
        filePo.setFileName(filePath);
        filePo.setFileType(FinaFileTypeEnum.TO_PIG_BUY.getValue());
        filePo.setFileOpStatus(FileOpStatusEnum.MAKE_SUCCESSFUL.getKey());
        filePo.setProcessCount(new BigDecimal(0));
        filePo.setProcessStatus(ProcessStatusEnum.NOT_PROCESS.getCode());
        filePo.setSysCode(SysCodeEnum.BATCH_HIGH.getCode());
        filePo.setTaTradeDt(taTradeDt);
        Date date = new Date();
        filePo.setCreateDtm(date);
        filePo.setUpdateDtm(date);
        return filePo;
    }

    private int saveFinaFileProcessRec(FinaFileProcessRecPo filePo) {
        return finaFileProcessRecRepository.insert(filePo);
    }

    private int updateFinaFileProcessRec(FinaFileProcessRecPo filePo) {
        FinaFileProcessRecPo newPo = new FinaFileProcessRecPo();
        newPo.setRecordNo(filePo.getRecordNo());
        newPo.setProcessStatus(ProcessStatusEnum.NOTICE_SUCCESS.getCode());
        newPo.setUpdateDtm(new Date());
        return finaFileProcessRecRepository.updateByRecordNo(newPo);
    }

    private BatchPiggyPurContext buildContext(String filePath, String productType, String taTradeDt) {
        String piggyProductType = PurchaseDirectionEnum.HOWBUY_PRIVATE_PAY.getKey();
        if (ProductTypeEnum.ZHUANHU.getCode().equals(productType) ||
                ProductTypeEnum.QUANSHANG_XIAOJIHE.getCode().equals(productType)) {
            piggyProductType = PurchaseDirectionEnum.HIGH_FUND_PAY.getKey();
        }
        BatchPiggyPurContext context = new BatchPiggyPurContext();
        context.setFilePath(filePath);
        context.setProductType(piggyProductType);
        context.setTaTradeDt(taTradeDt);
        context.setFileType(CxgFileTypeEnum.SM_ZJ_FILE.getCode());
        return context;
    }


    public String convertToString(HighCxgSaveFileRecPo po) {
        // 订单号（中台订单明细）|申请金额|产品编号|产品名称|分销|客户名称|身份证号|银行卡号|业务类型|业务来源流水号|备注|中台订单号|交易账号|资金账号
        StringBuilder line = new StringBuilder(300);
        line.append(po.getDealNo()).append(SEPARATOR);
        line.append(po.getApplyAmt()).append(SEPARATOR);
        line.append(po.getProdCode()).append(SEPARATOR);
        line.append(po.getProdName()).append(SEPARATOR);
        line.append(po.getDisCode()).append(SEPARATOR);
        line.append(po.getCustName()).append(SEPARATOR);
        line.append(po.getIdNo()).append(SEPARATOR);
        line.append(po.getCustBankNo()).append(SEPARATOR);
        line.append(po.getBusiType()).append(SEPARATOR);
        line.append(po.getBusiDealNo()).append(SEPARATOR);
        try {
            List<SimuFundCheckOrderPo> fundChkOrderPoList = simuFundCheckOrderRepository.getByContractNo(po.getBusiDealNo());
            //赎回根据来源业务流水号取
            if (fundChkOrderPoList != null && fundChkOrderPoList.size() == 1 && ZDtlBusiCodeEnum.REDEEM.getCode().equals(fundChkOrderPoList.get(0).getBusiCode())) {
                HighDealOrderDtlPo highDealOrderDtlPo = highDealOrderDtlRepository.selectByDealDtlNo(fundChkOrderPoList.get(0).getDealDtlNo());
                String name = highDealOrderDtlPo.getFundName() != null ? highDealOrderDtlPo.getFundName() : po.getProdName();
                line.append("来源于赎回").append(name).append(SEPARATOR);
                line.append(highDealOrderDtlPo.getDealNo()).append(SEPARATOR);
            } else {
                String memo = "";
                if (BusiTypeEnum.SALE.getCode().equals(po.getBusiType())) {
                    memo = "来源于" + po.getProdName() + "强赎";
                } else if (BusiTypeEnum.DIV.getCode().equals(po.getBusiType())) {
                    memo = "来源于" + po.getProdName() + "分红";
                }
                HighDealOrderDtlPo highDealOrderDtlPo = highDealOrderDtlRepository.queryByOriginSerialNo(po.getBusiDealNo());
                line.append(memo).append(SEPARATOR);
                if (highDealOrderDtlPo != null) {
                    line.append(highDealOrderDtlPo.getDealNo()).append(SEPARATOR);
                } else {
                    line.append("").append(SEPARATOR);

                }
            }
        } catch (Exception e) {
            logger.error("parse cxg CxgOrderFileGenerateProcessor file err", e);
        }
        line.append(po.getTxAcctNo()).append(SEPARATOR);
        line.append(po.getCustBankId());

        return line.toString();
    }


    /**
     * 中间路径
     *
     * @param taTradeDt ta交易日
     * @return 中间路径
     */
    private String getPath(String taTradeDt) {
        return taTradeDt + File.separator + Constant.CXG_FILE_PATH_ACTINAME;
    }

    /**
     * 文件名
     *
     * @param taTradeDt ta交易日
     * @return 中间路径
     */
    private String getFileName(String taTradeDt) {
        // 获取缓存key
        String key = getKey(taTradeDt);
        // 获取文件名序号
        String seqNo = String.format("%03d", CACHE_SERVICE.incrBy(key, 1L));
        return Constant.CXG_FILE_NAME_PREFIX + "_" + taTradeDt + "_" + seqNo + ".txt";
    }

    /**
     * 根路径
     *
     * @return 根路径Key
     */
    private String getBusinessCode() {
        return FilePathStoreBusinessCodeConfig.FTX_REGTRAN_CURRENT_FILE_STORE;
    }

    private String getKey(String taTradeDt) {
        return CacheKeyPrefix.HIGH_CXG_SAVE_FILE_SEQ_PREFIX + taTradeDt;
    }
}
