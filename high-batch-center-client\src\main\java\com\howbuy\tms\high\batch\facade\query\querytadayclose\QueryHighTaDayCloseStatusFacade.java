/**
 *Copyright (c) 2016, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.high.batch.facade.query.querytadayclose;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * Description:查询高端TA收市状态接口
 * 
 * <AUTHOR>
 * @date 2025-05-15 15:35:23
 * @since JDK 1.8
 */
public interface QueryHighTaDayCloseStatusFacade extends BaseFacade<QueryHighTaDayCloseStatusRequest, QueryHighTaDayCloseStatusResponse> {

} 