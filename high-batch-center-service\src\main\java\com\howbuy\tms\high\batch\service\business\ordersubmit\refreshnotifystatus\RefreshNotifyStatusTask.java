/**
 * Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.business.ordersubmit.refreshnotifystatus;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.NotifySubmitFlagEnum;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.service.business.submittadtcal.SubmitTaDtCalService;
import com.howbuy.tms.high.batch.service.common.OpsSysMonitor;
import com.howbuy.tms.high.batch.service.logic.ComplianceSubmitCheckLogicService;
import com.howbuy.tms.high.batch.service.logic.bean.ComplianceSubmitCheckResultDto;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.service.order.highdealorderdtl.HighDealOrderDtlService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @description:(刷新订单通知状态任务)
 * @reason:
 * @date 2018年5月29日 下午2:08:35
 * @since JDK 1.7
 */
public class RefreshNotifyStatusTask implements Runnable {
    private static Logger logger = LogManager.getLogger(RefreshNotifyStatusTask.class);

    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    private HighDealOrderDtlService highDealOrderDtlService;
    private SubmitTaDtCalService submitTaDtCalService;
    private String retrievePeriod;
    private List<HighDealOrderDtlPo> list;
    private ComplianceSubmitCheckLogicService complianceSubmitCheckLogicService;

    /**
     * 计数栅
     */
    private CountDownLatch latch;

    private Date now;

    public RefreshNotifyStatusTask(HighDealOrderDtlRepository highDealOrderDtlRepository,
                                   SubmitTaDtCalService submitTaDtCalService,
                                   HighDealOrderDtlService highDealOrderDtlService,
                                   List<HighDealOrderDtlPo> list, String retrievePeriod,
                                   ComplianceSubmitCheckLogicService complianceSubmitCheckLogicService,
                                   Date now, CountDownLatch latch) {
        super();
        this.highDealOrderDtlRepository = highDealOrderDtlRepository;
        this.submitTaDtCalService = submitTaDtCalService;
        this.retrievePeriod = retrievePeriod;
        this.highDealOrderDtlService = highDealOrderDtlService;
        this.complianceSubmitCheckLogicService = complianceSubmitCheckLogicService;
        this.list = list;
        this.now = now;
        this.latch = latch;
    }

    @Override
    public void run() {
        logger.info("RefreshNotifyStatusTask|run|start...");
        try {
            boolean result = false;
            for (HighDealOrderDtlPo po : list) {
                List<HighDealOrderDtlPo> orders;
                if (YesOrNoEnum.YES.getCode().equals(po.getMergeSubmitFlag())) {
                    // 查询子订单列表
                    orders = getOrdersByMainDealNo(po.getMainDealOrderNo());
                } else {
                    orders = new ArrayList<>();
                    orders.add(po);
                }
                // 记录订单原始信息
                Map<String, OldOrderInfoBean> oldOrderInfoMap = new HashMap<>(orders.size());
                orders.forEach(order -> {
                    OldOrderInfoBean old = new OldOrderInfoBean();
                    old.setRetrieveDtm(order.getRetrieveDtm());
                    old.setNotifySubmitFlag(order.getNotifySubmitFlag());
                    old.setSubmitTaDt(order.getSubmitTaDt());
                    old.setAssetStatus(order.getAssetcertificateStatus());
                    oldOrderInfoMap.put(order.getDealNo(), old);
                });
                logger.info("RefreshNotifyStatusTask|run|oldOrderInfo:{}", JSON.toJSONString(oldOrderInfoMap));
                try {
                    result = proecess(orders);
                    logger.info("RefreshNotifyStatusTask|run|dealDtlNo:{}, result:{}", po.getDealDtlNo(), result);

                    // 更新结果
                    updateResult(result, orders, oldOrderInfoMap);
                } catch (Exception e) {
                    logger.error("RefreshNotifyStatusTask|run|error,msg:", e);
                    po.setRetrieveDtm(calRetrieveDtm(null, false));
                    updateResult(false, orders, oldOrderInfoMap);
                    String msg = "RefreshNotifyStatusTask-更新订单通知状态失败,dealNo:" + po.getDealNo();
                    OpsSysMonitor.warn(msg, OpsSysMonitor.INFO);
                    logger.error(msg);
                }
            }
        } catch (Exception ex) {
            logger.error("RefreshNotifyStatusTask|run|error, msg:{}", ex.getMessage(), ex);
        } finally {
            latch.countDown();
        }
    }

    /**
     * 查询子订单列表
     *
     * @param mainDealNo
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo>
     * @author: huaqiang.liu
     * @date: 2021/7/2 16:38
     * @since JDK 1.8
     */
    private List<HighDealOrderDtlPo> getOrdersByMainDealNo(String mainDealNo) {
        return highDealOrderDtlRepository.getOrdersByMainDealNo(mainDealNo);
    }

    /**
     * updateResult:(更新结果)
     *
     * @param result
     * @param orders
     * @param oldOrderInfoMap
     * <AUTHOR>
     * @date 2018年5月29日 下午8:52:14
     */
    private void updateResult(boolean result, List<HighDealOrderDtlPo> orders, Map<String, OldOrderInfoBean> oldOrderInfoMap) {
        highDealOrderDtlService.updateNotifyStatus(result, orders, oldOrderInfoMap);
    }

    /**
     * proecess:(高端TP订单处理)
     *
     * @param orders
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午7:23:32
     */
    private boolean proecess(List<HighDealOrderDtlPo> orders) {
        // 上报前合规校验
        ComplianceSubmitCheckResultDto complianceSubmitCheckResultDto = complianceSubmitCheckLogicService.complianceSubmitCheck(orders, now);
        logger.info("合规校验结果:{}", JSON.toJSONString(complianceSubmitCheckResultDto));
        if (complianceSubmitCheckResultDto.isCfgMiss()) {
            return false;
        }
        HighDealOrderDtlPo po = orders.get(0);
        if (!complianceSubmitCheckResultDto.isAssetPass()) {
            // 资产证明需要及时去刷新，无需设置查询时间
            orders.forEach(order -> order.setRetrieveDtm(null));
            logger.error("RefreshNotifyStatusTask|run|assetResult is false, dealNo:{}", po.getDealNo());
            return false;
        }

        // 冷静期校验
        if (!complianceSubmitCheckResultDto.isCalmDtmPass()) {
            orders.forEach(order -> order.setRetrieveDtm(calRetrieveDtm(order.getCalmDtm(), true)));
            logger.error("RefreshNotifyStatusTask|run|calmDtmResult is false, dealNo:{}", po.getDealNo());
            return false;
        }

        // 双录校验
        if (!complianceSubmitCheckResultDto.isDualentryPass()) {
            orders.forEach(order -> order.setRetrieveDtm(calRetrieveDtm(null, false)));
            logger.error("RefreshNotifyStatusTask|run|dualentryResult is false, dealNo:{}", po.getDealNo());
            return false;
        }

        // 回访校验
        if (!complianceSubmitCheckResultDto.isCallbackPass()) {
            orders.forEach(order -> order.setRetrieveDtm(calRetrieveDtm(null, false)));
            logger.error("RefreshNotifyStatusTask|run|callbackResult is false, dealNo:{}", po.getDealNo());
            return false;
        }

        // 产品通道通过上报TA日计算模块计算新的上报TA日
        String submitTaDt = submitTaDtCalService.cal(po.getDealDtlNo());
        orders.forEach(order -> order.setSubmitTaDt(submitTaDt));

        // 更新订单通知状态
        orders.forEach(order -> order.setNotifySubmitFlag(NotifySubmitFlagEnum.NO_NOTIFY.getCode()));

        return true;
    }


    /**
     * calRetrieveDtm:(计算检索时间)
     *
     * @param calmDtm
     * @param calmDtmFlag
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午4:20:38
     */
    private Date calRetrieveDtm(Date calmDtm, boolean calmDtmFlag) {
        if (calmDtmFlag && calmDtm != null) {
            return new Date(calmDtm.getTime() - now.getTime());
        } else {
            return DateUtils.addDateByType(now, Calendar.MINUTE, Integer.parseInt(retrievePeriod));
        }
    }

}