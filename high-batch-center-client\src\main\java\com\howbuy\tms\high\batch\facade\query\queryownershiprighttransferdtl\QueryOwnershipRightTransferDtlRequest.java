package com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl;

import com.howbuy.tms.common.client.BaseRequest;
import com.howbuy.tms.common.client.TxCodes;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * @Description:股权份额转让订单详情查询入参
 * @Author: yun.lu
 * Date: 2023/5/18 15:48
 */
@Data
public class QueryOwnershipRightTransferDtlRequest extends BaseRequest {
    private static final long serialVersionUID = -1652429857463552155L;
    /**
     * 订单号
     */
    private String dealDtlNo;

    /**
     * 变更申请单号
     */
    private String dealAppNo;

    public QueryOwnershipRightTransferDtlRequest(){
        super.setTxCode(TxCodes.OWNERSHIP_RIGHT_TRANSFER_TX_CODE);
    }
}
