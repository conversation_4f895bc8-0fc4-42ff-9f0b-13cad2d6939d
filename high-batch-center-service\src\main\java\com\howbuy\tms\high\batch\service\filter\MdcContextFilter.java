package com.howbuy.tms.high.batch.service.filter;

import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.MDC;
import com.howbuy.trace.RequestChainTrace;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;



/**
 * Dubbo MDC上下文传递Filter
 * 
 * 解决Dubbo 3.x升级后MDC上下文传递问题，确保日志中的tid和ranNo能够正确显示
 * 
 * <AUTHOR>
 * @date 2024-11-08
 */
@Activate(group = {"provider", "consumer"}, order = -10000)
public class MdcContextFilter implements Filter {
    
    private static final Logger logger = LogManager.getLogger(MdcContextFilter.class);
    
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        // 保存调用前的ThreadContext状态
        String originalUuid = ThreadContext.get("uuid");
        String originalRanNo = ThreadContext.get("ranNo");

        // 获取当前的traceId和ranNo
        String traceId = RequestChainTrace.getReqId();
        String currentRanNo = RequestChainTrace.getRanNo();

        // 如果RequestChainTrace有值，确保ThreadContext也有值
        if (StringUtils.isNotEmpty(traceId)) {
            ThreadContext.put("uuid", traceId);
        }
        if (StringUtils.isNotEmpty(currentRanNo)) {
            ThreadContext.put("ranNo", currentRanNo);
        }

        logger.debug("MdcContextFilter before invoke: uuid={}, ranNo={}", traceId, currentRanNo);

        try {
            return invoker.invoke(invocation);
        } finally {
            // 恢复调用前的ThreadContext状态，而不是清空
            try {
                if (StringUtils.isNotEmpty(originalUuid)) {
                    ThreadContext.put("uuid", originalUuid);
                } else if (StringUtils.isNotEmpty(traceId)) {
                    // 如果原来没有，但RequestChainTrace有值，则设置回去
                    ThreadContext.put("uuid", traceId);
                }

                if (StringUtils.isNotEmpty(originalRanNo)) {
                    ThreadContext.put("ranNo", originalRanNo);
                } else if (StringUtils.isNotEmpty(currentRanNo)) {
                    // 如果原来没有，但RequestChainTrace有值，则设置回去
                    ThreadContext.put("ranNo", currentRanNo);
                }

                logger.debug("MdcContextFilter restored ThreadContext: uuid={}, ranNo={}",
                    ThreadContext.get("uuid"),
                   ThreadContext.get("ranNo"));
            } catch (Exception e) {
                logger.warn("MdcContextFilter restore context error: {}", e.getMessage());
            }
        }
    }
}
