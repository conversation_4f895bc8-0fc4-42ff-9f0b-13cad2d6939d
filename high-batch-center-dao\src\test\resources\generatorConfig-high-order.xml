<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="context" targetRuntime="MyBatis3" defaultModelType="flat">

        <!--生成mapper.xml时覆盖原文件-->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <!-- 增加序列化-->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>

        <!-- type值为自定义的MyCommentGenerator-->
        <commentGenerator>
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
            <!-- 是否添加数据表中字段的注释 true：是 ： false:否 -->
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <!--数据库链接地址账号密码-->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="*************************************************" userId="tms" password="tms">
        </jdbcConnection>
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!--        文件生成位置-->
        <javaModelGenerator targetPackage="com.howbuy.tms.high.batch.dao.po.order"
                            targetProject="src/main/java">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>
        <!--生成xml映射文件存放位置-->
        <sqlMapGenerator targetPackage="com.howbuy.tms.high.batch.dao.mapper.order"
                         targetProject="src/main/resources">
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>
        <!--生成mapper类存放位置-->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.howbuy.tms.high.batch.dao.mapper.order"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>


        <!-- 指定库时oracle用schema，mysql用catalog -->
        <!-- 生成的sql不带库名：在table标签内加<property name="ignoreQualifiersAtRuntime" value="true"/> -->
<!--                <table catalog="docker_it31_high" tableName="redeem_fee_notice" mapperName="RedeemFeeNoticePoAutoMapper"-->
<!--                       domainObjectName="RedeemFeeNoticePo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--                       enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--                    <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--                </table>-->

<!--        <table catalog="docker_it03_high" tableName="DEAL_ORDER" mapperName="DealOrderPoAutoMapper"-->
<!--               domainObjectName="DealOrderPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="HIGH_DEAL_ORDER_DTL" mapperName="HighDealOrderDtlPoAutoMapper"-->
<!--               domainObjectName="HighDealOrderDtlPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--            <columnOverride column="SUBMITTADT_INTERPOSE_FLAG" property="submitTaDtInterposeFlag"/>-->
<!--            <columnOverride column="FORCE_REDEEM_MEMO" property="forceRedeemMemo"/>-->
<!--            <columnOverride column="ORIGIN_SERIAL_NO" property="originSerialNo"/>-->
<!--            <columnOverride column="APPOINTMENT_DEALNO_TYPE" property="appointmentDealNoType"/>-->
<!--            <columnOverride column="CALM_TIME" property="calmTime" javaType="Integer"/>-->
<!--        </table>-->

        <table catalog="docker_it06_high" tableName="DEAL_ORDER_EXTEND" mapperName="DealOrderExtendPoAutoMapper"
               domainObjectName="DealOrderExtendPo" enableCountByExample="true" enableUpdateByExample="true"
               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">
            <property name="ignoreQualifiersAtRuntime" value="true"/>
        </table>

<!--        <table catalog="docker_it03_high" tableName="PAYMENT_ORDER" mapperName="PaymentOrderPoAutoMapper"-->
<!--               domainObjectName="PaymentOrderPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="HIGH_ORDER_APPOINTINFO"-->
<!--               mapperName="HighOrderAppointinfoPoAutoMapper"-->
<!--               domainObjectName="HighOrderAppointinfoPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="DEAL_ORDER_REFUND" mapperName="DealOrderRefundPoAutoMapper"-->
<!--               domainObjectName="DealOrderRefundPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="CUST_ESIGNATURE" mapperName="CustEsignaturePoAutoMapper"-->
<!--               domainObjectName="CustEsignaturePo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="CUST_ESIGNATURE_AGREEMENT"-->
<!--               mapperName="CustEsignatureAgreementPoAutoMapper"-->
<!--               domainObjectName="CustEsignatureAgreementPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="CUST_REPURCHASE_PROTOCOL"-->
<!--               mapperName="CustRepurchaseProtocolPoAutoMapper"-->
<!--               domainObjectName="CustRepurchaseProtocolPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="ES_PRO_ORDER_MONITOR" mapperName="EsProOrderMonitorPoAutoMapper"-->
<!--               domainObjectName="EsProOrderMonitorPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->


<!--        <table catalog="docker_it03_high" tableName="CUST_FUND_DIV_MODE" mapperName="CustFundDivModePoAutoMapper"-->
<!--               domainObjectName="CustFundDivModePo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="SIMU_FUND_CHECK_ORDER" mapperName="SimuFundCheckOrderPoAutoMapper"-->
<!--               domainObjectName="SimuFundCheckOrderPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="HIGH_FUND_INV_PLAN" mapperName="HighFundInvPlanPoAutoMapper"-->
<!--               domainObjectName="HighFundInvPlanPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--            <columnOverride column="PLAN_RATE" property="planRate" javaType="Integer"/>-->
<!--            <columnOverride column="PLAN_TOTAL_NUM" property="planTotalNum" javaType="Integer"/>-->
<!--            <columnOverride column="PLAN_SUCCESS_COUNT" property="planSuccessCount" javaType="Integer"/>-->
<!--            <columnOverride column="PLAN_FAIL_COUNT" property="planFailCount" javaType="Integer"/>-->
<!--            <columnOverride column="PLAN_CONTINU_FAIL_COUNT" property="planContinuFailCount" javaType="Integer"/>-->
<!--        </table>-->


<!--        <table catalog="docker_it03_high" tableName="HIGH_REDEEM_SPLIT_DTL" mapperName="HighRedeemSplitDtlPoAutoMapper"-->
<!--               domainObjectName="HighRedeemSplitDtlPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="CUSTOMER_SUBMIT_FORM" mapperName="CustomerSubmitFormPoAutoMapper"-->
<!--               domainObjectName="CustomerSubmitFormPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="FORM_TEMPLATE" mapperName="FormTemplatePoAutoMapper"-->
<!--               domainObjectName="FormTemplatePo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="HZ_ORIGINAL_ORDER_HIS_RECORD"-->
<!--               mapperName="HzOriginalOrderHisRecordPoAutoMapper"-->
<!--               domainObjectName="HzOriginalOrderHisRecordPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="SUBSCRIBE_AMT_DETAIL" mapperName="SubscribeAmtDetailPoAutoMapper"-->
<!--               domainObjectName="SubscribeAmtDetailPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="SIMU_FUND_TA_ORDER" mapperName="SimuFundTaOrderPoAutoMapper"-->
<!--               domainObjectName="SimuFundTaOrderPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="SIMU_TMP_CUST_BOOKS_DTL"-->
<!--               mapperName="SimuTmpCustBooksDtlPoAutoMapper"-->
<!--               domainObjectName="SimuTmpCustBooksDtlPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="HIGH_FUND_INV_PLAN_DTL" mapperName="HighFundInvPlanDtlPoAutoMapper"-->
<!--               domainObjectName="HighFundInvPlanDtlPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--            <columnOverride column="PLAN_NUM" property="planNum" javaType="Integer"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="HIGH_FUND_INV_PLAN_ORDER_DTL"-->
<!--               mapperName="HighFundInvPlanOrderDtlPoAutoMapper"-->
<!--               domainObjectName="HighFundInvPlanOrderDtlPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--            <columnOverride column="PLAN_NUM" property="planNum" javaType="Integer"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="HIGH_FUND_INV_PLAN_TERMINATE"-->
<!--               mapperName="HighFundInvPlanTerminatePoAutoMapper"-->
<!--               domainObjectName="HighFundInvPlanTerminatePo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="CM_BLACKLIST_DIRECT" mapperName="CmBlacklistDirectPoAutoMapper"-->
<!--               domainObjectName="CmBlacklistDirectPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--            <columnOverride column="FUNDCODE" property="fundCode"/>-->
<!--            <columnOverride column="HBONENO" property="hbOneNo"/>-->
<!--            <columnOverride column="VERSIONNO" property="versionNo"/>-->
<!--            <columnOverride column="IMPORTDT" property="importDt"/>-->
<!--        </table>-->


        <!--        <table catalog="docker_it03_high" tableName="export_cm_blacklist_direct" mapperName="ExportCmBlacklistDirectPoAutoMapper"-->
        <!--               domainObjectName="ExportCmBlacklistDirectPo" enableCountByExample="true" enableUpdateByExample="true"-->
        <!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
        <!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="export_he_vol_check_file_rec" mapperName="ExportHeVolCheckFileRecPoAutoMapper"-->
<!--               domainObjectName="ExportHeVolCheckFileRecPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--            <columnOverride column="HBONE_NO" property="hbOneNo"/>-->
<!--        </table>-->
<!--        <table catalog="docker_it58_high" tableName="CM_CUSTFUND_DIRECT" domainObjectName="CmCustFundDirectPo"-->
<!--               mapperName="CmCustFundDirectPoAutoMapper"-->
<!--               enableCountByExample="true" enableUpdateByExample="true" enableDeleteByExample="true"-->
<!--               enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--            <columnOverride column="HBONENO" property="hbOneNo"/>-->
<!--            <columnOverride column="FUNDCODE" property="fundCode"/>-->
<!--            <columnOverride column="FUNDTYPE" property="fundType"/>-->
<!--            <columnOverride column="FUNDNAME" property="fundName"/>-->
<!--            <columnOverride column="DIVIDEAMT" property="divideAmt"/>-->
<!--            <columnOverride column="DIVAMT" property="divAmt"/>-->
<!--            <columnOverride column="BALANCEVOL" property="balanceVol"/>-->
<!--            <columnOverride column="TOTALACKAMT" property="totalAckAmt"/>-->
<!--            <columnOverride column="TOTALCOST" property="totalCost"/>-->
<!--            <columnOverride column="CREDT" property="creDt"/>-->
<!--            <columnOverride column="MODDT" property="modDt"/>-->
<!--            <columnOverride column="MJJDM" property="mjjDm"/>-->
<!--            <columnOverride column="VERSIONNO" property="versionNo"/>-->
<!--            <columnOverride column="IMPORTDT" property="importDt"/>-->
<!--            <columnOverride column="DISCODE" property="disCode"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="CM_CUST_PROD_YJYZ" mapperName="CmCustProdYjyzPoAutoMapper"-->
<!--               domainObjectName="CmCustProdYjyzPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="CM_NA_RECEIV_FEE" mapperName="CmNaReceivFeePoAutoMapper"-->
<!--               domainObjectName="CmNaReceivFeePo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->


<!--        <table catalog="docker_it11_high" tableName="CM_NA_RECEIV_FEE_TMP" mapperName="CmNaReceivFeeTmpPoAutoMapper"-->
<!--               domainObjectName="CmNaReceivFeeTmpPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it11_high" tableName="CM_NA_RECEIV_FEE" mapperName="CmNaReceivFeePoAutoMapper"-->
<!--               domainObjectName="CmNaReceivFeePo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->


<!--        <table catalog="docker_it58_high" tableName="CM_CUSTFUND_DIRECT_TMP" domainObjectName="CmCustFundDirectTmpPo"-->
<!--               mapperName="CmCustFundDirectTmpPoAutoMapper"-->
<!--               enableCountByExample="true" enableUpdateByExample="true" enableDeleteByExample="true"-->
<!--               enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--            <columnOverride column="HBONENO" property="hbOneNo"/>-->
<!--            <columnOverride column="FUNDCODE" property="fundCode"/>-->
<!--            <columnOverride column="FUNDTYPE" property="fundType"/>-->
<!--            <columnOverride column="FUNDNAME" property="fundName"/>-->
<!--            <columnOverride column="DIVIDEAMT" property="divideAmt"/>-->
<!--            <columnOverride column="DIVAMT" property="divAmt"/>-->
<!--            <columnOverride column="BALANCEVOL" property="balanceVol"/>-->
<!--            <columnOverride column="TOTALACKAMT" property="totalAckAmt"/>-->
<!--            <columnOverride column="TOTALCOST" property="totalCost"/>-->
<!--            <columnOverride column="CREDT" property="creDt"/>-->
<!--            <columnOverride column="MODDT" property="modDt"/>-->
<!--            <columnOverride column="MJJDM" property="mjjDm"/>-->
<!--            <columnOverride column="VERSIONNO" property="versionNo"/>-->
<!--            <columnOverride column="IMPORTDT" property="importDt"/>-->
<!--            <columnOverride column="DISCODE" property="disCode"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it31_high" tableName="CM_CUSTTRADE_DIRECT" domainObjectName="CmCusttradeDirectPo"-->
<!--               mapperName="CmCusttradeDirectPoAutoMapper"-->
<!--               enableCountByExample="true" enableUpdateByExample="true" enableDeleteByExample="true"-->
<!--               enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--            <columnOverride column="MJJDM" property="mjjDm"/>-->
<!--            <columnOverride column="TXACCTNO" property="txAcctNo"/>-->
<!--            <columnOverride column="IMPORTDT" property="importDt"/>-->
<!--            <columnOverride column="RECSTAT" property="recStat"/>-->
<!--            <columnOverride column="VERSIONNO" property="versionNo"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it31_high" tableName="CM_CUSTTRADE_DIRECT_TMP"-->
<!--               mapperName="CmCusttradeDirectTmpPoAutoMapper"-->
<!--               domainObjectName="CmCusttradeDirectTmpPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--            <columnOverride column="MJJDM" property="mjjDm"/>-->
<!--            <columnOverride column="VERSIONNO" property="versionNo"/>-->
<!--            <columnOverride column="IMPORTDT" property="importDt"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="CIS_ORDER_DETAIL" mapperName="CisOrderDetailPoAutoMapper"-->
<!--               domainObjectName="CisOrderDetailPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->


<!--        <table catalog="docker_it03_high" tableName="CIS_ORDER_BENEFICIARY" mapperName="CisOrderBeneficiaryPoAutoMapper"-->
<!--               domainObjectName="CisOrderBeneficiaryPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="CIS_ORDER_COVERAGE" mapperName="CisOrderCoveragePoAutoMapper"-->
<!--               domainObjectName="CisOrderCoveragePo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="CIS_ORDER_CUST" mapperName="CisOrderCustPoAutoMapper"-->
<!--               domainObjectName="CisOrderCustPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="CUST_BOOKS" mapperName="CustBooksPoAutoMapper"-->
<!--               domainObjectName="CustBooksPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="HIS_CUST_BOOKS" mapperName="HisCustBooksPoAutoMapper"-->
<!--               domainObjectName="HisCustBooksPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="CUST_BOOKS_DTL" mapperName="CustBooksDtlPoAutoMapper"-->
<!--               domainObjectName="CustBooksDtlPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->
<!--        <table catalog="docker_it03_high" tableName="HIS_CUST_BOOKS_DTL" mapperName="HisCustBooksDtlPoAutoMapper"-->
<!--               domainObjectName="HisCustBooksDtlPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->


<!--        <table catalog="docker_it03_high" tableName="SUB_CUST_BOOKS" mapperName="SubCustBooksPoAutoMapper"-->
<!--               domainObjectName="SubCustBooksPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="HIS_SUB_CUST_BOOKS" mapperName="HisSubCustBooksPoAutoMapper"-->
<!--               domainObjectName="HisSubCustBooksPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="SUB_CUST_BOOKS_DTL" mapperName="SubCustBooksDtlPoAutoMapper"-->
<!--               domainObjectName="SubCustBooksDtlPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="HIS_SUB_CUST_BOOKS_DTL" mapperName="HisSubCustBooksDtlPoAutoMapper"-->
<!--               domainObjectName="HisSubCustBooksDtlPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="TMP_SUB_CUST_BOOKS_DTL" mapperName="TmpSubCustBooksDtlPoAutoMapper"-->
<!--               domainObjectName="TmpSubCustBooksDtlPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="CUST_PROTOCOL" mapperName="CustProtocolPoAutoMapper"-->
<!--               domainObjectName="CustProtocolPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->


<!--        <table catalog="docker_it31_high" tableName="export_he_fund_ack_file_rec" mapperName="ExportHeFundAckFileRecPoAutoMapper"-->
<!--               domainObjectName="ExportHeFundAckFileRecPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->



<!--        <table catalog="docker_it03_high" tableName="export_cm_cust_prod_yjyz" mapperName="ExportCmCustProdYjyzPoAutoMapper"-->
<!--               domainObjectName="ExportCmCustProdYjyzPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->




<!--        <table catalog="docker_it03_high" tableName="cm_blacklist_direct_tmp" mapperName="CmBlacklistDirectTmpPoAutoMapper"-->
<!--               domainObjectName="CmBlacklistDirectTmpPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--            <columnOverride column="fundcode" property="fundCode"/>-->
<!--            <columnOverride column="hboneno" property="hbOneNo"/>-->
<!--            <columnOverride column="versionno" property="versionNo"/>-->
<!--            <columnOverride column="importdt" property="importDt"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="cm_cust_prod_yjyz_tmp" mapperName="CmCustProdYjyzTmpPoAutoMapper"-->
<!--               domainObjectName="CmCustProdYjyzTmpPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--            <columnOverride column="versionno" property="versionNo"/>-->
<!--            <columnOverride column="importdt" property="importDt"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="export_cm_na_receiv_fee" mapperName="ExportCmNaReceivFeePoAutoMapper"-->
<!--               domainObjectName="ExportCmNaReceivFeePo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->


<!--        <table catalog="docker_it03_high" tableName="cust_econtract" mapperName="CustEcontractPoAutoMapper"-->
<!--               domainObjectName="CustEcontractPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="high_redeem_split_order_back" mapperName="HighRedeemSplitOrderBackPoAutoMapper"-->
<!--               domainObjectName="HighRedeemSplitOrderBackPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="high_redeem_split_order_error" mapperName="HighRedeemSplitOrderErrorPoAutoMapper"-->
<!--               domainObjectName="HighRedeemSplitOrderErrorPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->

<!--        <table catalog="docker_it03_high" tableName="HIS_CUST_PROTOCOL" mapperName="HisCustProtocolPoAutoMapper"-->
<!--               domainObjectName="HisCustProtocolPo" enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--            <property name="ignoreQualifiersAtRuntime" value="true"/>-->
<!--        </table>-->
    </context>
</generatorConfiguration>
