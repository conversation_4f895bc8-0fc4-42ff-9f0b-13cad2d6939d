package com.howbuy.tms.high.batch.service.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.common.date.DateUtil;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 运维监控告警
 * 
 */
public class OpsMonitor {

    private static final Logger logger =  LoggerFactory.getLogger(OpsMonitor.class);
    public static final String ERROR = "ERROR";
    public static final String WARN = "WARN";
    public static final String INFO = "INFO";
    private static Logger warnLogger = null;
    static{
        try {
            warnLogger = LoggerFactory.getLogger("monitorjson");
        } catch (Exception e) {
                        
        }
    }


    /**
     * 监控告警
     */
    public static void warn(String content, String level) {
        try {
            Map<String, String> warnMsg = new HashMap<String, String>();
            warnMsg.put("time", DateUtil.formatToString(new Date(), DateUtil.YYYYMMDDHHMMssSSS));
            warnMsg.put("msg", content);
            warnMsg.put("level", level);
            warnMsg.put("host", getIp());
            String uuid = ThreadContext.get("uuid");
            warnMsg.put("traceId", uuid);
            warnMsg.put("appName", "high-batch-center-remote");
            warnLogger.warn(JSON.toJSONString(warnMsg));
        } catch (Exception e) {
            logger.error("OpsMonitor Error:", e);
        }
    }

    private static String getIp() {
        String result = "";
        try {
            // 获取IP地址
            result = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            logger.error("Exception：get Ip failure ", e);
        }
        return result;
    }

    public static String convertLog(String traceId, String txCode, String returnCode, String custNo, long costTime, String remoteHost) {
        JSONObject json = new JSONObject();
        json.put("time", getDetailTimeStr());
        json.put("tx_code", txCode);
        json.put("costs", costTime);
        json.put("return_code", returnCode);
        json.put("traceId", traceId);
        json.put("custNo", custNo);
        json.put("remoteHost", remoteHost);
        return json.toJSONString();
    }

    private static String getDetailTimeStr() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date());
    }
    
}
