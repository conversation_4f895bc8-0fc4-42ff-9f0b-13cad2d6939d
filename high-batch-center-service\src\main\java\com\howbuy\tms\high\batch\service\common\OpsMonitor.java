package com.howbuy.tms.high.batch.service.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.common.date.DateUtil;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 运维监控告警
 * 
 */
public class OpsMonitor {

    private static final Logger logger =  LoggerFactory.getLogger("monitorjson");
    public static final String ERROR = "ERROR";
    public static final String WARN = "WARN";
    public static final String INFO = "INFO";
    private static Logger warnLogger = null;
    static{
        try {
            // 先尝试原来的 monitorjson
            warnLogger = LoggerFactory.getLogger("monitorjson");
            System.out.println("=== DEBUG: monitorjson logger class: " + warnLogger.getClass().getName());

            // 如果是 NOPLogger，说明 monitorjson 配置不存在，尝试使用其他 logger
            if (warnLogger instanceof org.slf4j.helpers.NOPLogger) {
                System.out.println("=== DEBUG: monitorjson is NOPLogger, trying alternative loggers...");

                // 尝试使用 mainlog
                Logger mainLogger = LoggerFactory.getLogger("mainlog");
                System.out.println("=== DEBUG: mainlog logger class: " + mainLogger.getClass().getName());
                if (!(mainLogger instanceof org.slf4j.helpers.NOPLogger)) {
                    warnLogger = mainLogger;
                    System.out.println("=== DEBUG: Using mainlog as warnLogger");
                } else {
                    // 如果 mainlog 也不行，使用 com.howbuy.opsmonitor（会匹配 com.howbuy 配置）
                    Logger comLogger = LoggerFactory.getLogger("com.howbuy.opsmonitor");
                    System.out.println("=== DEBUG: com.howbuy.opsmonitor logger class: " + comLogger.getClass().getName());
                    warnLogger = comLogger;
                    System.out.println("=== DEBUG: Using com.howbuy.opsmonitor as warnLogger");
                }
            }

            System.out.println("=== DEBUG: Final warnLogger class: " + warnLogger.getClass().getName());
            System.out.println("=== DEBUG: Final warnLogger isWarnEnabled: " + warnLogger.isWarnEnabled());
            System.out.println("=== DEBUG: Final warnLogger isErrorEnabled: " + warnLogger.isErrorEnabled());

        } catch (Exception e) {
            System.out.println("=== DEBUG: OpsMonitor warnLogger initialization failed: " + e.getMessage());
            e.printStackTrace();
        }
    }


    /**
     * 监控告警
     */
    public static void warn(String content, String level) {
        System.out.println("=== DEBUG: OpsMonitor.warn() called with content: " + content + ", level: " + level);

        try {
            if (warnLogger == null) {
                System.out.println("=== DEBUG: warnLogger is null!");
                return;
            }

            System.out.println("=== DEBUG: warnLogger isWarnEnabled: " + warnLogger.isWarnEnabled());
            System.out.println("=== DEBUG: warnLogger isErrorEnabled: " + warnLogger.isErrorEnabled());

            // 尝试强制设置 logger 级别为 INFO
            try {
                if (warnLogger instanceof org.apache.logging.log4j.core.Logger) {
                    org.apache.logging.log4j.core.Logger log4jLogger = (org.apache.logging.log4j.core.Logger) warnLogger;
                    System.out.println("=== DEBUG: Current logger level: " + log4jLogger.getLevel());
                    log4jLogger.setLevel(org.apache.logging.log4j.Level.INFO);
                    System.out.println("=== DEBUG: Set logger level to INFO, now isWarnEnabled: " + warnLogger.isWarnEnabled());
                }
            } catch (Exception levelEx) {
                System.out.println("=== DEBUG: Failed to set logger level: " + levelEx.getMessage());
            }

            Map<String, String> warnMsg = new HashMap<String, String>();
            warnMsg.put("time", DateUtil.formatToString(new Date(), DateUtil.YYYYMMDDHHMMssSSS));
            warnMsg.put("msg", content);
            warnMsg.put("level", level);
            warnMsg.put("host", getIp());
            String uuid = ThreadContext.get("uuid");
            warnMsg.put("traceId", uuid);
            warnMsg.put("appName", "high-batch-center-remote");

            String jsonMsg = JSON.toJSONString(warnMsg);
            System.out.println("=== DEBUG: About to call warnLogger.error() with: " + jsonMsg);

            // 使用 error 级别而不是 warn 级别
            warnLogger.error(jsonMsg);
            logger.info(jsonMsg);
            System.out.println("=== DEBUG: warnLogger.error() called successfully");

        } catch (Exception e) {
            System.out.println("=== DEBUG: Exception in OpsMonitor.warn(): " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static String getIp() {
        String result = "";
        try {
            // 获取IP地址
            result = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            logger.error("Exception：get Ip failure ", e);
        }
        return result;
    }

    public static String convertLog(String traceId, String txCode, String returnCode, String custNo, long costTime, String remoteHost) {
        JSONObject json = new JSONObject();
        json.put("time", getDetailTimeStr());
        json.put("tx_code", txCode);
        json.put("costs", costTime);
        json.put("return_code", returnCode);
        json.put("traceId", traceId);
        json.put("custNo", custNo);
        json.put("remoteHost", remoteHost);
        return json.toJSONString();
    }

    private static String getDetailTimeStr() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date());
    }
    
}
